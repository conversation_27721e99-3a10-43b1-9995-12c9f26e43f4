#!/usr/bin/env python3
"""
Smart Start for Devin Local AI
Auto-discovers all your models and configures the best one automatically!
"""

import sys
import yaml
import json
from pathlib import Path
from utils.model_discovery import ModelDiscovery, discover_and_configure


def print_banner():
    """Print awesome banner"""
    print("""
🧠 DEVIN LOCAL AI - SMART START
═══════════════════════════════════════════════════════════════
🔥 AUTO-DISCOVERING ALL YOUR MODELS... 🔥
""")


def print_model_summary(discovery: ModelDiscovery):
    """Print discovered models in a nice format"""
    models = discovery.discovered_models
    
    if not models:
        print("❌ No models found! Make sure you have Ollama, LM Studio, or other LLM servers running.")
        return
    
    print(f"\n🎯 FOUND {len(models)} MODELS ACROSS {len(discovery.active_providers)} PROVIDERS:")
    print("═" * 70)
    
    # Group by provider
    from collections import defaultdict
    by_provider = defaultdict(list)
    
    for model in models:
        by_provider[model.provider.value].append(model)
    
    for provider, provider_models in by_provider.items():
        print(f"\n🔌 {provider.upper()}:")
        for model in provider_models:
            score_stars = "⭐" * min(5, int(model.performance_score))
            capabilities = ", ".join(model.capabilities[:3])  # Show first 3
            print(f"   • {model.name} {score_stars}")
            print(f"     └─ {capabilities}")
            if model.size:
                print(f"     └─ Size: {model.size}")
    
    # Show the winner
    if discovery.best_model:
        best = discovery.best_model
        print(f"\n🏆 BEST MODEL FOR CODING:")
        print(f"   🥇 {best.name} ({best.provider.value})")
        print(f"   📊 Score: {best.performance_score:.1f}/10")
        print(f"   🎯 Capabilities: {', '.join(best.capabilities)}")
        print(f"   🌐 URL: {best.base_url}")


def auto_configure_system():
    """Auto-configure the system with the best model"""
    print("\n🔧 AUTO-CONFIGURING SYSTEM...")
    
    result = discover_and_configure()
    
    if "error" in result:
        print(f"❌ Configuration failed: {result['error']}")
        return False
    
    # Save the auto-generated config
    config = result["config"]
    model_info = result["model_info"]
    
    # Update config.yaml
    config_path = Path("config.yaml")
    
    try:
        # Load existing config
        if config_path.exists():
            with open(config_path, 'r') as f:
                existing_config = yaml.safe_load(f) or {}
        else:
            existing_config = {}
        
        # Merge with auto-discovered LLM config
        existing_config.update(config)
        
        # Save updated config
        with open(config_path, 'w') as f:
            yaml.dump(existing_config, f, default_flow_style=False, indent=2)
        
        print(f"✅ CONFIGURED WITH: {model_info['name']}")
        print(f"   Provider: {model_info['provider']}")
        print(f"   Score: {model_info['score']:.1f}/10")
        print(f"   Capabilities: {', '.join(model_info['capabilities'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to save configuration: {e}")
        return False


def test_configuration():
    """Test the configured model"""
    print("\n🧪 TESTING CONFIGURATION...")
    
    try:
        from main import DevinLocal
        
        # Try to initialize the system
        devin = DevinLocal()
        
        print("✅ System initialized successfully!")
        print("🚀 Ready to accept coding tasks!")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def show_quick_start():
    """Show quick start options"""
    print(f"""
🚀 SYSTEM READY! CHOOSE YOUR ADVENTURE:

1️⃣  Interactive Mode:
   python main.py -i

2️⃣  Web Interface:
   streamlit run ui/streamlit_app.py

3️⃣  Try a Quick Task:
   python main.py "Build a simple calculator function"

4️⃣  See Full Demo:
   python demo.py

5️⃣  Run Tests:
   python test_system.py

💡 Example tasks you can try:
   • "Build a REST API in Flask that returns random jokes"
   • "Create a data analysis script for CSV files"  
   • "Build a command-line password generator"
   • "Create a web scraper for product prices"

🔥 Your models are locked and loaded! Let's build something awesome! 🔥
""")


def main():
    """Main smart start function"""
    print_banner()
    
    # Discover all models
    discovery = ModelDiscovery()
    discovery.discover_all_models()
    
    # Show what we found
    print_model_summary(discovery)
    
    if not discovery.discovered_models:
        print("\n💡 TIPS TO GET MODELS RUNNING:")
        print("   • Ollama: ollama serve (then ollama pull codellama:13b)")
        print("   • LM Studio: Start the local server")
        print("   • Oobabooga: python server.py --api")
        print("   • KoboldCpp: ./koboldcpp --port 5001")
        return
    
    # Auto-configure with best model
    if auto_configure_system():
        # Test the configuration
        if test_configuration():
            show_quick_start()
        else:
            print("\n⚠️  Configuration saved but system test failed.")
            print("   Try running: python test_system.py")
    else:
        print("\n❌ Auto-configuration failed.")
        print("   You may need to manually configure config.yaml")


if __name__ == "__main__":
    main()
