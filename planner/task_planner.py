"""
Task Planner for Devin Local AI
Breaks down high-level coding tasks into executable subtasks.
"""

import re
from typing import List, Dict, Optional
from dataclasses import dataclass
from enum import Enum


class TaskType(Enum):
    """Types of subtasks"""
    SETUP = "setup"
    CODE = "code"
    TEST = "test"
    DEBUG = "debug"
    INSTALL = "install"
    RUN = "run"


@dataclass
class SubTask:
    """Individual subtask"""
    id: int
    type: TaskType
    description: str
    code_hint: Optional[str] = None
    expected_output: Optional[str] = None
    dependencies: List[int] = None
    completed: bool = False
    attempts: int = 0
    max_attempts: int = 3
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class TaskPlanner:
    """Plans and manages coding tasks"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.max_subtasks = self.config.get('max_subtasks', 20)
        self.include_testing = self.config.get('include_testing', True)
        self.detail_level = self.config.get('detail_level', 'high')
    
    def plan_task(self, task_description: str) -> List[SubTask]:
        """
        Break down a high-level task into executable subtasks
        
        Args:
            task_description: Natural language description of the coding task
            
        Returns:
            List of SubTask objects
        """
        # Analyze the task to determine the type of project
        project_type = self._analyze_project_type(task_description)
        
        # Generate subtasks based on project type
        subtasks = []
        
        if project_type == "web_api":
            subtasks = self._plan_web_api_task(task_description)
        elif project_type == "data_analysis":
            subtasks = self._plan_data_analysis_task(task_description)
        elif project_type == "cli_tool":
            subtasks = self._plan_cli_tool_task(task_description)
        elif project_type == "web_scraper":
            subtasks = self._plan_web_scraper_task(task_description)
        else:
            subtasks = self._plan_generic_task(task_description)
        
        # Add testing subtasks if enabled
        if self.include_testing:
            subtasks.extend(self._add_testing_subtasks(subtasks))
        
        # Assign IDs and validate
        for i, subtask in enumerate(subtasks):
            subtask.id = i + 1
        
        return subtasks[:self.max_subtasks]
    
    def _analyze_project_type(self, description: str) -> str:
        """Analyze task description to determine project type"""
        description_lower = description.lower()
        
        # Web API patterns
        if any(keyword in description_lower for keyword in 
               ['api', 'flask', 'fastapi', 'rest', 'endpoint', 'server']):
            return "web_api"
        
        # Data analysis patterns
        elif any(keyword in description_lower for keyword in 
                ['data', 'analysis', 'pandas', 'csv', 'plot', 'chart', 'visualization']):
            return "data_analysis"
        
        # CLI tool patterns
        elif any(keyword in description_lower for keyword in 
                ['cli', 'command line', 'script', 'tool', 'argparse']):
            return "cli_tool"
        
        # Web scraper patterns
        elif any(keyword in description_lower for keyword in 
                ['scrape', 'crawl', 'web', 'html', 'beautifulsoup', 'selenium']):
            return "web_scraper"
        
        return "generic"
    
    def _plan_web_api_task(self, description: str) -> List[SubTask]:
        """Plan subtasks for web API development"""
        subtasks = [
            SubTask(0, TaskType.SETUP, "Set up project structure and imports"),
            SubTask(0, TaskType.INSTALL, "Install required packages (Flask/FastAPI)"),
            SubTask(0, TaskType.CODE, "Create basic app instance and configuration"),
            SubTask(0, TaskType.CODE, "Define main route/endpoint"),
            SubTask(0, TaskType.CODE, "Implement core functionality"),
            SubTask(0, TaskType.CODE, "Add error handling"),
            SubTask(0, TaskType.RUN, "Start the server and test basic functionality"),
        ]
        
        # Add specific endpoints based on description
        if "joke" in description.lower():
            subtasks.insert(4, SubTask(0, TaskType.CODE, "Integrate joke API or create joke data"))
        elif "user" in description.lower():
            subtasks.insert(4, SubTask(0, TaskType.CODE, "Implement user management"))
        
        return subtasks
    
    def _plan_data_analysis_task(self, description: str) -> List[SubTask]:
        """Plan subtasks for data analysis"""
        return [
            SubTask(0, TaskType.SETUP, "Import required libraries (pandas, matplotlib, etc.)"),
            SubTask(0, TaskType.CODE, "Load and examine the data"),
            SubTask(0, TaskType.CODE, "Clean and preprocess data"),
            SubTask(0, TaskType.CODE, "Perform analysis"),
            SubTask(0, TaskType.CODE, "Create visualizations"),
            SubTask(0, TaskType.CODE, "Generate summary/report"),
        ]
    
    def _plan_cli_tool_task(self, description: str) -> List[SubTask]:
        """Plan subtasks for CLI tool development"""
        return [
            SubTask(0, TaskType.SETUP, "Set up argument parsing"),
            SubTask(0, TaskType.CODE, "Implement main functionality"),
            SubTask(0, TaskType.CODE, "Add input validation"),
            SubTask(0, TaskType.CODE, "Add output formatting"),
            SubTask(0, TaskType.TEST, "Test with different command line arguments"),
        ]
    
    def _plan_web_scraper_task(self, description: str) -> List[SubTask]:
        """Plan subtasks for web scraping"""
        return [
            SubTask(0, TaskType.INSTALL, "Install scraping libraries (requests, BeautifulSoup)"),
            SubTask(0, TaskType.CODE, "Set up HTTP session and headers"),
            SubTask(0, TaskType.CODE, "Fetch webpage content"),
            SubTask(0, TaskType.CODE, "Parse HTML and extract data"),
            SubTask(0, TaskType.CODE, "Handle pagination/multiple pages"),
            SubTask(0, TaskType.CODE, "Save data to file/database"),
        ]
    
    def _plan_generic_task(self, description: str) -> List[SubTask]:
        """Plan subtasks for generic coding tasks"""
        return [
            SubTask(0, TaskType.SETUP, "Set up basic project structure"),
            SubTask(0, TaskType.CODE, "Implement core functionality"),
            SubTask(0, TaskType.CODE, "Add error handling"),
            SubTask(0, TaskType.TEST, "Test the implementation"),
        ]
    
    def _add_testing_subtasks(self, main_subtasks: List[SubTask]) -> List[SubTask]:
        """Add testing subtasks"""
        testing_subtasks = []
        
        # Find code subtasks to test
        code_subtasks = [st for st in main_subtasks if st.type == TaskType.CODE]
        
        if code_subtasks:
            testing_subtasks.extend([
                SubTask(0, TaskType.TEST, "Write unit tests for core functions"),
                SubTask(0, TaskType.TEST, "Run tests and verify functionality"),
            ])
        
        return testing_subtasks
    
    def get_next_subtask(self, subtasks: List[SubTask]) -> Optional[SubTask]:
        """Get the next subtask to execute"""
        for subtask in subtasks:
            if not subtask.completed and subtask.attempts < subtask.max_attempts:
                # Check if dependencies are completed
                if all(subtasks[dep_id - 1].completed for dep_id in subtask.dependencies):
                    return subtask
        return None
    
    def mark_completed(self, subtasks: List[SubTask], subtask_id: int) -> bool:
        """Mark a subtask as completed"""
        if 1 <= subtask_id <= len(subtasks):
            subtasks[subtask_id - 1].completed = True
            return True
        return False
    
    def increment_attempts(self, subtasks: List[SubTask], subtask_id: int) -> bool:
        """Increment attempt counter for a subtask"""
        if 1 <= subtask_id <= len(subtasks):
            subtasks[subtask_id - 1].attempts += 1
            return True
        return False
    
    def get_progress_summary(self, subtasks: List[SubTask]) -> Dict:
        """Get progress summary"""
        total = len(subtasks)
        completed = sum(1 for st in subtasks if st.completed)
        failed = sum(1 for st in subtasks if st.attempts >= st.max_attempts and not st.completed)
        
        return {
            "total": total,
            "completed": completed,
            "failed": failed,
            "remaining": total - completed - failed,
            "progress_percent": (completed / total * 100) if total > 0 else 0
        }


def create_task_plan(task_description: str, config: Dict = None) -> List[SubTask]:
    """
    Convenience function to create a task plan
    
    Args:
        task_description: Natural language description of the task
        config: Optional configuration dictionary
        
    Returns:
        List of SubTask objects
    """
    planner = TaskPlanner(config)
    return planner.plan_task(task_description)
