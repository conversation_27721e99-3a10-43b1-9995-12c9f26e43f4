"""
Example: Flask Joke API
This is an example of what Devin Local AI might generate for the task:
"Build a REST API in Flask that returns random jokes"
"""

from flask import Flask, jsonify, request
import random
import json
from datetime import datetime

app = Flask(__name__)

# Sample jokes database
jokes = [
    {
        "id": 1,
        "joke": "Why don't scientists trust atoms? Because they make up everything!",
        "category": "science"
    },
    {
        "id": 2,
        "joke": "Why did the scarecrow win an award? He was outstanding in his field!",
        "category": "general"
    },
    {
        "id": 3,
        "joke": "Why don't eggs tell jokes? They'd crack each other up!",
        "category": "food"
    },
    {
        "id": 4,
        "joke": "What do you call a fake noodle? An impasta!",
        "category": "food"
    },
    {
        "id": 5,
        "joke": "Why did the math book look so sad? Because it had too many problems!",
        "category": "education"
    }
]

@app.route('/')
def home():
    """Welcome endpoint"""
    return jsonify({
        "message": "Welcome to the Joke API!",
        "endpoints": {
            "/joke": "Get a random joke",
            "/joke/<category>": "Get a random joke from a specific category",
            "/jokes": "Get all jokes",
            "/categories": "Get all available categories"
        }
    })

@app.route('/joke')
def get_random_joke():
    """Get a random joke"""
    try:
        joke = random.choice(jokes)
        return jsonify({
            "success": True,
            "joke": joke,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/joke/<category>')
def get_joke_by_category(category):
    """Get a random joke from a specific category"""
    try:
        category_jokes = [j for j in jokes if j['category'].lower() == category.lower()]
        
        if not category_jokes:
            return jsonify({
                "success": False,
                "error": f"No jokes found for category: {category}"
            }), 404
        
        joke = random.choice(category_jokes)
        return jsonify({
            "success": True,
            "joke": joke,
            "category": category,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/jokes')
def get_all_jokes():
    """Get all jokes"""
    try:
        return jsonify({
            "success": True,
            "jokes": jokes,
            "count": len(jokes),
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/categories')
def get_categories():
    """Get all available categories"""
    try:
        categories = list(set(joke['category'] for joke in jokes))
        return jsonify({
            "success": True,
            "categories": categories,
            "count": len(categories),
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/joke', methods=['POST'])
def add_joke():
    """Add a new joke"""
    try:
        data = request.get_json()
        
        if not data or 'joke' not in data:
            return jsonify({
                "success": False,
                "error": "Missing 'joke' field in request body"
            }), 400
        
        new_joke = {
            "id": max(j['id'] for j in jokes) + 1,
            "joke": data['joke'],
            "category": data.get('category', 'general')
        }
        
        jokes.append(new_joke)
        
        return jsonify({
            "success": True,
            "message": "Joke added successfully",
            "joke": new_joke,
            "timestamp": datetime.now().isoformat()
        }), 201
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        "success": False,
        "error": "Endpoint not found"
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        "success": False,
        "error": "Internal server error"
    }), 500

if __name__ == '__main__':
    print("🎭 Starting Joke API server...")
    print("📍 Available endpoints:")
    print("   GET  /           - API information")
    print("   GET  /joke       - Random joke")
    print("   GET  /joke/<cat> - Joke by category")
    print("   GET  /jokes      - All jokes")
    print("   GET  /categories - All categories")
    print("   POST /joke       - Add new joke")
    print("\n🚀 Server starting on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
