"""
Memory Manager for Devin Local AI
Handles persistent storage of task state, history, and progress.
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class TaskSession:
    """Represents a complete task session"""
    id: str
    description: str
    start_time: float
    end_time: Optional[float] = None
    status: str = "running"  # running, completed, failed, paused
    subtasks: List[Dict] = None
    attempts: List[Dict] = None
    final_code: Optional[str] = None
    success: bool = False
    
    def __post_init__(self):
        if self.subtasks is None:
            self.subtasks = []
        if self.attempts is None:
            self.attempts = []


@dataclass
class ExecutionAttempt:
    """Represents a single code execution attempt"""
    timestamp: float
    subtask_id: int
    code: str
    result: Dict
    success: bool
    error_info: Optional[Dict] = None


class MemoryManager:
    """Manages persistent memory for the Devin agent"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        memory_config = self.config.get('memory', {})
        
        self.file_path = Path(memory_config.get('file_path', './memory/memory.json'))
        self.max_history = memory_config.get('max_history', 100)
        self.auto_save = memory_config.get('auto_save', True)
        
        # Ensure memory directory exists
        self.file_path.parent.mkdir(exist_ok=True)
        
        # Load existing memory
        self.memory = self._load_memory()
        
        # Current session
        self.current_session: Optional[TaskSession] = None
    
    def start_session(self, task_description: str) -> str:
        """
        Start a new task session
        
        Args:
            task_description: Description of the task
            
        Returns:
            Session ID
        """
        session_id = f"session_{int(time.time())}"
        
        self.current_session = TaskSession(
            id=session_id,
            description=task_description,
            start_time=time.time()
        )
        
        # Add to memory
        if 'sessions' not in self.memory:
            self.memory['sessions'] = []
        
        self.memory['sessions'].append(asdict(self.current_session))
        
        if self.auto_save:
            self._save_memory()
        
        return session_id
    
    def end_session(self, success: bool = False, final_code: str = None):
        """End the current session"""
        if self.current_session:
            self.current_session.end_time = time.time()
            self.current_session.status = "completed" if success else "failed"
            self.current_session.success = success
            self.current_session.final_code = final_code
            
            # Update in memory
            for session in self.memory['sessions']:
                if session['id'] == self.current_session.id:
                    session.update(asdict(self.current_session))
                    break
            
            if self.auto_save:
                self._save_memory()
            
            self.current_session = None
    
    def save_subtasks(self, subtasks: List[Dict]):
        """Save subtasks for the current session"""
        if self.current_session:
            self.current_session.subtasks = subtasks
            
            # Update in memory
            for session in self.memory['sessions']:
                if session['id'] == self.current_session.id:
                    session['subtasks'] = subtasks
                    break
            
            if self.auto_save:
                self._save_memory()
    
    def save_attempt(self, subtask_id: int, code: str, result: Dict, error_info: Dict = None):
        """
        Save an execution attempt
        
        Args:
            subtask_id: ID of the subtask
            code: Code that was executed
            result: Execution result
            error_info: Error information if execution failed
        """
        if not self.current_session:
            return
        
        attempt = ExecutionAttempt(
            timestamp=time.time(),
            subtask_id=subtask_id,
            code=code,
            result=result,
            success=result.get('success', False),
            error_info=error_info
        )
        
        self.current_session.attempts.append(asdict(attempt))
        
        # Update in memory
        for session in self.memory['sessions']:
            if session['id'] == self.current_session.id:
                session['attempts'] = self.current_session.attempts
                break
        
        if self.auto_save:
            self._save_memory()
    
    def get_session_history(self, limit: int = 10) -> List[Dict]:
        """Get recent session history"""
        sessions = self.memory.get('sessions', [])
        return sorted(sessions, key=lambda x: x['start_time'], reverse=True)[:limit]
    
    def get_current_session(self) -> Optional[Dict]:
        """Get current session data"""
        if self.current_session:
            return asdict(self.current_session)
        return None
    
    def get_session_by_id(self, session_id: str) -> Optional[Dict]:
        """Get session by ID"""
        for session in self.memory.get('sessions', []):
            if session['id'] == session_id:
                return session
        return None
    
    def get_successful_patterns(self, task_keywords: List[str] = None) -> List[Dict]:
        """
        Get patterns from successful sessions
        
        Args:
            task_keywords: Keywords to filter similar tasks
            
        Returns:
            List of successful patterns
        """
        successful_sessions = [
            session for session in self.memory.get('sessions', [])
            if session.get('success', False)
        ]
        
        if task_keywords:
            # Filter by task similarity
            filtered_sessions = []
            for session in successful_sessions:
                description = session.get('description', '').lower()
                if any(keyword.lower() in description for keyword in task_keywords):
                    filtered_sessions.append(session)
            successful_sessions = filtered_sessions
        
        # Extract patterns (successful code, subtask structures, etc.)
        patterns = []
        for session in successful_sessions:
            if session.get('final_code'):
                patterns.append({
                    'type': 'successful_code',
                    'task_description': session.get('description'),
                    'code': session.get('final_code'),
                    'subtasks': session.get('subtasks', [])
                })
        
        return patterns
    
    def get_error_patterns(self) -> List[Dict]:
        """Get common error patterns to avoid"""
        error_patterns = []
        
        for session in self.memory.get('sessions', []):
            for attempt in session.get('attempts', []):
                if not attempt.get('success') and attempt.get('error_info'):
                    error_patterns.append({
                        'error_type': attempt['error_info'].get('error_type'),
                        'error_message': attempt['error_info'].get('message'),
                        'code_snippet': attempt.get('code', '')[:200],  # First 200 chars
                        'suggestion': attempt['error_info'].get('suggestion')
                    })
        
        # Group similar errors
        grouped_errors = {}
        for error in error_patterns:
            error_type = error.get('error_type', 'Unknown')
            if error_type not in grouped_errors:
                grouped_errors[error_type] = []
            grouped_errors[error_type].append(error)
        
        return grouped_errors
    
    def cleanup_old_sessions(self):
        """Remove old sessions to keep memory size manageable"""
        sessions = self.memory.get('sessions', [])
        if len(sessions) > self.max_history:
            # Keep the most recent sessions
            sorted_sessions = sorted(sessions, key=lambda x: x['start_time'], reverse=True)
            self.memory['sessions'] = sorted_sessions[:self.max_history]
            
            if self.auto_save:
                self._save_memory()
    
    def export_session(self, session_id: str, export_path: str) -> bool:
        """Export a session to a separate file"""
        session = self.get_session_by_id(session_id)
        if not session:
            return False
        
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(session, f, indent=2, default=str)
            return True
        except Exception:
            return False
    
    def get_statistics(self) -> Dict:
        """Get memory statistics"""
        sessions = self.memory.get('sessions', [])
        
        total_sessions = len(sessions)
        successful_sessions = sum(1 for s in sessions if s.get('success', False))
        failed_sessions = total_sessions - successful_sessions
        
        total_attempts = sum(len(s.get('attempts', [])) for s in sessions)
        
        return {
            'total_sessions': total_sessions,
            'successful_sessions': successful_sessions,
            'failed_sessions': failed_sessions,
            'success_rate': (successful_sessions / total_sessions * 100) if total_sessions > 0 else 0,
            'total_attempts': total_attempts,
            'average_attempts_per_session': (total_attempts / total_sessions) if total_sessions > 0 else 0
        }
    
    def _load_memory(self) -> Dict:
        """Load memory from file"""
        if self.file_path.exists():
            try:
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                # If file is corrupted, start fresh
                pass
        
        return {
            'sessions': [],
            'metadata': {
                'created': time.time(),
                'version': '1.0'
            }
        }
    
    def _save_memory(self):
        """Save memory to file"""
        try:
            # Update metadata
            self.memory['metadata']['last_updated'] = time.time()
            
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.memory, f, indent=2, default=str)
        except Exception as e:
            print(f"Warning: Failed to save memory: {e}")


def create_memory_manager(config: Dict = None) -> MemoryManager:
    """
    Create a memory manager with the given configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        MemoryManager instance
    """
    return MemoryManager(config)
