#!/usr/bin/env python3
"""
Devin Local AI - Self-Iterating Coding Agent
Main entry point for the local Devin-style AI system.
"""

import sys
import yaml
import time
import argparse
from pathlib import Path
from typing import Dict, List, Optional

# Import our modules
from agents.devin_agent import <PERSON><PERSON><PERSON>, AgentContext
from planner.task_planner import TaskPlanner, TaskType
from executor.runner import CodeExecutor
from utils.error_parser import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, parse_execution_result
from memory.memory_manager import MemoryManager


class DevinLocal:
    """Main orchestrator for the Devin Local AI system"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the Devin Local system"""
        self.config = self._load_config(config_path)
        
        # Initialize components
        self.agent = DevinAgent(self.config)
        self.planner = TaskPlanner(self.config.get('planning', {}))
        self.executor = CodeExecutor(self.config.get('execution', {}))
        self.error_parser = ErrorParser()
        self.memory = MemoryManager(self.config)
        
        # State
        self.current_session_id = None
        self.current_subtasks = []
        self.max_retries = self.config.get('execution', {}).get('max_retries', 5)
    
    def run_task(self, task_description: str) -> Dict:
        """
        Run a complete coding task
        
        Args:
            task_description: Natural language description of the task
            
        Returns:
            Dictionary with task results
        """
        print(f"🎯 Starting task: {task_description}")
        print("=" * 60)
        
        # Start session
        self.current_session_id = self.memory.start_session(task_description)
        
        try:
            # Plan the task
            print("📋 Planning task...")
            self.current_subtasks = self.planner.plan_task(task_description)
            self.memory.save_subtasks([vars(st) for st in self.current_subtasks])
            
            print(f"   Created {len(self.current_subtasks)} subtasks")
            for i, subtask in enumerate(self.current_subtasks, 1):
                print(f"   {i}. {subtask.description}")
            print()
            
            # Execute subtasks
            success = self._execute_task_loop(task_description)
            
            # Get final code
            final_code = self._get_final_code()
            
            # End session
            self.memory.end_session(success, final_code)
            
            result = {
                'success': success,
                'session_id': self.current_session_id,
                'subtasks_completed': sum(1 for st in self.current_subtasks if st.completed),
                'total_subtasks': len(self.current_subtasks),
                'final_code': final_code
            }
            
            if success:
                print("✅ Task completed successfully!")
                if final_code:
                    print("\n📄 Final code:")
                    print("-" * 40)
                    print(final_code)
                    print("-" * 40)
            else:
                print("❌ Task failed to complete")
            
            return result
            
        except Exception as e:
            print(f"💥 Unexpected error: {str(e)}")
            self.memory.end_session(False)
            return {
                'success': False,
                'error': str(e),
                'session_id': self.current_session_id
            }
    
    def _execute_task_loop(self, task_description: str) -> bool:
        """Execute the main task loop"""
        context = AgentContext(task_description=task_description, current_subtask="")
        
        while True:
            # Get next subtask
            current_subtask = self.planner.get_next_subtask(self.current_subtasks)
            if not current_subtask:
                break
            
            print(f"🔧 Working on: {current_subtask.description}")
            context.current_subtask = current_subtask.description
            
            # Execute subtask
            success = self._execute_subtask(current_subtask, context)
            
            if success:
                self.planner.mark_completed(self.current_subtasks, current_subtask.id)
                print(f"   ✅ Completed: {current_subtask.description}")
            else:
                self.planner.increment_attempts(self.current_subtasks, current_subtask.id)
                print(f"   ❌ Failed: {current_subtask.description} (attempt {current_subtask.attempts})")
                
                if current_subtask.attempts >= current_subtask.max_attempts:
                    print(f"   🚫 Max attempts reached for: {current_subtask.description}")
            
            print()
        
        # Check overall success
        progress = self.planner.get_progress_summary(self.current_subtasks)
        return progress['failed'] == 0 and progress['completed'] > 0
    
    def _execute_subtask(self, subtask, context: AgentContext) -> bool:
        """Execute a single subtask"""
        if subtask.type == TaskType.INSTALL:
            return self._handle_install_subtask(subtask)
        elif subtask.type in [TaskType.CODE, TaskType.SETUP, TaskType.TEST]:
            return self._handle_code_subtask(subtask, context)
        elif subtask.type == TaskType.RUN:
            return self._handle_run_subtask(subtask, context)
        else:
            print(f"   ⚠️  Unknown subtask type: {subtask.type}")
            return False
    
    def _handle_install_subtask(self, subtask) -> bool:
        """Handle package installation subtasks"""
        # Extract package name from description
        description = subtask.description.lower()
        
        # Common package mappings
        package_map = {
            'flask': 'flask',
            'fastapi': 'fastapi',
            'requests': 'requests',
            'beautifulsoup': 'beautifulsoup4',
            'pandas': 'pandas',
            'numpy': 'numpy',
            'matplotlib': 'matplotlib'
        }
        
        package_name = None
        for keyword, package in package_map.items():
            if keyword in description:
                package_name = package
                break
        
        if not package_name:
            print(f"   ⚠️  Could not determine package to install from: {subtask.description}")
            return False
        
        print(f"   📦 Installing {package_name}...")
        result = self.executor.install_package(package_name)
        
        if result['success']:
            print(f"   ✅ Successfully installed {package_name}")
            return True
        else:
            print(f"   ❌ Failed to install {package_name}: {result['stderr']}")
            return False
    
    def _handle_code_subtask(self, subtask, context: AgentContext) -> bool:
        """Handle code generation/modification subtasks"""
        # Generate code
        code = self.agent.generate_code(context)
        
        if not code.strip():
            print("   ⚠️  No code generated")
            return False
        
        # Execute code
        filename = f"subtask_{subtask.id}_{int(time.time())}.py"
        exec_result = self.executor.execute_code(code, filename)
        
        # Parse results
        parsed_result = parse_execution_result(
            exec_result['stdout'],
            exec_result['stderr'],
            exec_result['return_code'],
            code
        )
        
        # Save attempt
        self.memory.save_attempt(
            subtask.id,
            code,
            exec_result,
            parsed_result.get('error_info')
        )
        
        if parsed_result['success']:
            print(f"   ✅ Code executed successfully")
            if exec_result['stdout']:
                print(f"   📤 Output: {exec_result['stdout'][:200]}...")
            return True
        else:
            print(f"   ❌ Code execution failed")
            if parsed_result.get('error_feedback'):
                print(f"   🐛 Error: {parsed_result['error_feedback']}")
            
            # Try to debug and fix
            if subtask.attempts < subtask.max_attempts - 1:
                print("   🔧 Attempting to debug...")
                fixed_code = self.agent.debug_code(context, parsed_result)
                
                if fixed_code and fixed_code != code:
                    # Try the fixed code
                    exec_result = self.executor.execute_code(fixed_code, f"fixed_{filename}")
                    parsed_result = parse_execution_result(
                        exec_result['stdout'],
                        exec_result['stderr'],
                        exec_result['return_code'],
                        fixed_code
                    )
                    
                    if parsed_result['success']:
                        print("   ✅ Fixed code executed successfully")
                        return True
            
            return False
    
    def _handle_run_subtask(self, subtask, context: AgentContext) -> bool:
        """Handle run/test subtasks"""
        # For now, just check if we have any working code files
        files = self.executor.list_files()
        python_files = [f for f in files if f.endswith('.py')]
        
        if python_files:
            print(f"   ✅ Found {len(python_files)} Python files ready to run")
            return True
        else:
            print("   ❌ No Python files found to run")
            return False
    
    def _get_final_code(self) -> Optional[str]:
        """Get the final working code"""
        files = self.executor.list_files()
        python_files = [f for f in files if f.endswith('.py') and not f.startswith('temp_')]
        
        if python_files:
            # Get the most recent file
            latest_file = max(python_files, key=lambda f: Path(self.executor.working_directory / f).stat().st_mtime)
            return self.executor.read_file(latest_file)
        
        return None
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"Warning: Could not load config from {config_path}: {e}")
            return {}


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="Devin Local AI - Self-Iterating Coding Agent")
    parser.add_argument("task", nargs="?", help="Coding task description")
    parser.add_argument("--config", default="config.yaml", help="Configuration file path")
    parser.add_argument("--interactive", "-i", action="store_true", help="Interactive mode")
    parser.add_argument("--stats", action="store_true", help="Show statistics")
    
    args = parser.parse_args()
    
    # Initialize system
    devin = DevinLocal(args.config)
    
    if args.stats:
        # Show statistics
        stats = devin.memory.get_statistics()
        print("📊 Devin Local AI Statistics")
        print("=" * 30)
        print(f"Total sessions: {stats['total_sessions']}")
        print(f"Successful: {stats['successful_sessions']}")
        print(f"Failed: {stats['failed_sessions']}")
        print(f"Success rate: {stats['success_rate']:.1f}%")
        print(f"Total attempts: {stats['total_attempts']}")
        print(f"Avg attempts/session: {stats['average_attempts_per_session']:.1f}")
        return
    
    if args.interactive or not args.task:
        # Interactive mode
        print("🧠 Devin Local AI - Interactive Mode")
        print("Type 'quit' to exit, 'stats' for statistics")
        print("=" * 50)
        
        while True:
            try:
                task = input("\n💭 Enter coding task: ").strip()
                
                if task.lower() in ['quit', 'exit', 'q']:
                    break
                elif task.lower() == 'stats':
                    stats = devin.memory.get_statistics()
                    print(f"\n📊 Sessions: {stats['total_sessions']} | Success rate: {stats['success_rate']:.1f}%")
                    continue
                elif not task:
                    continue
                
                # Run the task
                result = devin.run_task(task)
                
                if result['success']:
                    print(f"\n🎉 Task completed! Session: {result['session_id']}")
                else:
                    print(f"\n😞 Task failed. Session: {result['session_id']}")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n💥 Error: {e}")
    else:
        # Single task mode
        result = devin.run_task(args.task)
        sys.exit(0 if result['success'] else 1)


if __name__ == "__main__":
    main()
