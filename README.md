# 🧠 Devin Local AI

A local, self-iterating AI coding agent that can accept tasks, plan steps, write code, execute it, debug errors, and retry until success - all running on your local machine with a 13B model.

## 🎯 Features

- **Self-Iterating**: Automatically debugs and fixes code based on execution errors
- **Local LLM**: Works with Ollama, LM Studio, or any OpenAI-compatible API
- **Task Planning**: Breaks down complex tasks into manageable subtasks
- **Memory System**: Learns from previous attempts and successful patterns
- **Safe Execution**: Sandboxed code execution with configurable security
- **Error Intelligence**: Advanced error parsing and suggestion generation

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up Local LLM

**Option A: Ollama (Recommended)**
```bash
# Install Ollama from https://ollama.ai
ollama pull codellama:13b
# or
ollama pull mistral:7b-instruct
```

**Option B: LM Studio**
- Download from https://lmstudio.ai
- Load a code-capable model (<PERSON>lla<PERSON>, <PERSON>stra<PERSON>, etc.)
- Start the local server

### 3. Configure

Edit `config.yaml` to match your setup:

```yaml
llm:
  provider: "ollama"  # or "lm_studio"
  model: "codellama:13b"
  base_url: "http://localhost:11434"
```

### 4. Run Your First Task

```bash
# Interactive mode
python main.py -i

# Direct task
python main.py "Build a REST API in Flask that returns random jokes"
```

## 📁 Project Structure

```
devin_local/
├── agents/
│   └── devin_agent.py      # Core AI agent logic
├── executor/
│   ├── runner.py           # Code execution engine
│   └── sandbox.py          # Sandboxing (future)
├── memory/
│   └── memory_manager.py   # Persistent state management
├── planner/
│   └── task_planner.py     # Task breakdown logic
├── utils/
│   └── error_parser.py     # Error analysis and suggestions
├── workspace/              # Generated code execution area
├── logs/                   # System logs
├── main.py                 # Entry point
├── config.yaml             # Configuration
└── requirements.txt        # Dependencies
```

## 🔄 How It Works

1. **Task Input**: You provide a natural language coding task
2. **Planning**: AI breaks it into executable subtasks
3. **Code Generation**: AI writes Python code for each subtask
4. **Execution**: Code runs in a controlled environment
5. **Error Analysis**: If errors occur, they're parsed and analyzed
6. **Debugging**: AI fixes the code based on error feedback
7. **Retry Loop**: Process repeats until success or max attempts
8. **Memory**: Successful patterns are saved for future use

## 🛠️ Example Tasks

### Web API Development
```bash
python main.py "Create a FastAPI server with user registration and login endpoints"
```

### Data Analysis
```bash
python main.py "Analyze a CSV file and create visualizations of sales trends"
```

### CLI Tools
```bash
python main.py "Build a command-line tool that converts JSON to CSV"
```

### Web Scraping
```bash
python main.py "Scrape product prices from an e-commerce site and save to database"
```

## ⚙️ Configuration Options

### LLM Settings
```yaml
llm:
  provider: "ollama"          # ollama, lm_studio, openai
  model: "codellama:13b"      # Model name
  temperature: 0.1            # Creativity (0.0-1.0)
  max_tokens: 2048           # Response length
  timeout: 60                # Request timeout
```

### Execution Settings
```yaml
execution:
  timeout: 30                # Code execution timeout
  max_retries: 5             # Max attempts per subtask
  sandbox_mode: false        # Use Docker (requires setup)
  allowed_imports:           # Whitelist imports
    - "requests"
    - "flask"
    - "pandas"
```

### Memory Settings
```yaml
memory:
  max_history: 100           # Max sessions to remember
  auto_save: true            # Auto-save progress
```

## 🔧 Advanced Usage

### Interactive Mode
```bash
python main.py -i
```

### View Statistics
```bash
python main.py --stats
```

### Custom Configuration
```bash
python main.py --config my_config.yaml "Your task here"
```

## 🧪 Example Session

```
🎯 Starting task: Build a REST API in Flask that returns random jokes
============================================================
📋 Planning task...
   Created 7 subtasks
   1. Set up project structure and imports
   2. Install required packages (Flask/FastAPI)
   3. Create basic app instance and configuration
   4. Define main route/endpoint
   5. Integrate joke API or create joke data
   6. Add error handling
   7. Start the server and test basic functionality

🔧 Working on: Set up project structure and imports
   ✅ Code executed successfully
   ✅ Completed: Set up project structure and imports

🔧 Working on: Install required packages (Flask/FastAPI)
   📦 Installing flask...
   ✅ Successfully installed flask
   ✅ Completed: Install required packages (Flask/FastAPI)

🔧 Working on: Create basic app instance and configuration
   ✅ Code executed successfully
   ✅ Completed: Create basic app instance and configuration

... (continues until completion)

✅ Task completed successfully!

📄 Final code:
----------------------------------------
from flask import Flask, jsonify
import random

app = Flask(__name__)

jokes = [
    "Why don't scientists trust atoms? Because they make up everything!",
    "Why did the scarecrow win an award? He was outstanding in his field!",
    "Why don't eggs tell jokes? They'd crack each other up!"
]

@app.route('/joke')
def get_joke():
    return jsonify({"joke": random.choice(jokes)})

if __name__ == '__main__':
    app.run(debug=True)
----------------------------------------
```

## 🔒 Security Notes

- Code execution is limited by configurable timeouts
- Import restrictions can be enforced
- Sandbox mode available (Docker required)
- All generated code is saved for review
- No automatic package installation without permission

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📝 License

MIT License - see LICENSE file for details

## 🆘 Troubleshooting

### LLM Connection Issues
- Ensure Ollama/LM Studio is running
- Check the base_url in config.yaml
- Verify the model is downloaded

### Code Execution Failures
- Check Python environment
- Verify working directory permissions
- Review allowed_imports settings

### Memory Issues
- Increase max_tokens for complex tasks
- Clear old sessions: delete memory/memory.json
- Reduce max_history in config

## 🔮 Roadmap

- [ ] Docker sandbox integration
- [ ] Web UI with Streamlit/Gradio
- [ ] Multi-language support (JavaScript, Go, etc.)
- [ ] Integration with external APIs
- [ ] Advanced debugging with step-through
- [ ] Collaborative multi-agent workflows
- [ ] Plugin system for custom tools

---

**Built with ❤️ for the local AI community**
