#!/usr/bin/env python3
"""
Demo script for Devin Local AI
Shows the system capabilities without requiring a local LLM.
"""

import time
from pathlib import Path

# Import our modules
from planner.task_planner import TaskPlanner
from executor.runner import CodeExecutor
from utils.error_parser import <PERSON>rrorParser
from memory.memory_manager import <PERSON><PERSON><PERSON><PERSON>


def demo_task_planning():
    """Demonstrate task planning capabilities"""
    print("🎯 Demo: Task Planning")
    print("-" * 30)
    
    planner = TaskPlanner()
    
    # Test different types of tasks
    tasks = [
        "Build a REST API in Flask that returns random jokes",
        "Create a data analysis script for sales data",
        "Build a command-line calculator tool",
        "Create a web scraper for product prices"
    ]
    
    for task in tasks:
        print(f"\n📋 Task: {task}")
        subtasks = planner.plan_task(task)
        
        print(f"   Generated {len(subtasks)} subtasks:")
        for i, subtask in enumerate(subtasks[:5], 1):  # Show first 5
            print(f"   {i}. {subtask.description}")
        
        if len(subtasks) > 5:
            print(f"   ... and {len(subtasks) - 5} more")


def demo_code_execution():
    """Demonstrate code execution"""
    print("\n🔧 Demo: Code Execution")
    print("-" * 30)
    
    executor = CodeExecutor()
    
    # Test successful code
    print("\n✅ Testing successful code:")
    success_code = """
# Simple calculator demo
def add(a, b):
    return a + b

def multiply(a, b):
    return a * b

# Test the functions
result1 = add(5, 3)
result2 = multiply(4, 7)

print(f"5 + 3 = {result1}")
print(f"4 × 7 = {result2}")
print("Calculator demo completed successfully!")
"""
    
    result = executor.execute_code(success_code, "calculator_demo.py")
    if result['success']:
        print(f"   Output: {result['stdout'].strip()}")
    else:
        print(f"   Error: {result['stderr']}")
    
    # Test error handling
    print("\n❌ Testing error handling:")
    error_code = """
# Code with intentional error
def divide(a, b):
    return a / b

# This will cause a NameError
result = divide(10, undefined_variable)
print(f"Result: {result}")
"""
    
    result = executor.execute_code(error_code, "error_demo.py")
    if not result['success']:
        error_lines = result['stderr'].split('\n')
        print(f"   Detected error: {error_lines[-2]}")


def demo_error_parsing():
    """Demonstrate error parsing"""
    print("\n🐛 Demo: Error Analysis")
    print("-" * 30)
    
    parser = ErrorParser()
    
    # Test different error types
    errors = [
        {
            "type": "NameError",
            "traceback": """Traceback (most recent call last):
  File "test.py", line 3, in <module>
    print(undefined_var)
NameError: name 'undefined_var' is not defined"""
        },
        {
            "type": "ModuleNotFoundError", 
            "traceback": """Traceback (most recent call last):
  File "test.py", line 1, in <module>
    import nonexistent_module
ModuleNotFoundError: No module named 'nonexistent_module'"""
        },
        {
            "type": "SyntaxError",
            "traceback": """  File "test.py", line 2
    if x = 5:
         ^
SyntaxError: invalid syntax"""
        }
    ]
    
    for error in errors:
        print(f"\n🔍 Analyzing {error['type']}:")
        error_info = parser.parse_error(error['traceback'])
        print(f"   Type: {error_info.error_type}")
        print(f"   Message: {error_info.message}")
        if error_info.line_number:
            print(f"   Line: {error_info.line_number}")
        print(f"   Suggestion: {error_info.suggestion}")


def demo_memory_system():
    """Demonstrate memory management"""
    print("\n🧠 Demo: Memory System")
    print("-" * 30)
    
    # Use test config
    config = {
        'memory': {
            'file_path': './memory/demo_memory.json',
            'max_history': 10,
            'auto_save': True
        }
    }
    
    memory = MemoryManager(config)
    
    # Simulate a session
    print("\n📝 Creating demo session...")
    session_id = memory.start_session("Demo: Build a calculator")
    
    # Simulate some attempts
    memory.save_attempt(1, "print('Hello')", {'success': True}, None)
    memory.save_attempt(2, "print(undefined)", {'success': False}, 
                       {'error_type': 'NameError', 'message': 'undefined not defined'})
    memory.save_attempt(3, "def add(a,b): return a+b", {'success': True}, None)
    
    memory.end_session(True, "def add(a,b): return a+b\nprint(add(2,3))")
    
    # Show statistics
    stats = memory.get_statistics()
    print(f"   Session ID: {session_id}")
    print(f"   Total sessions: {stats['total_sessions']}")
    print(f"   Success rate: {stats['success_rate']:.1f}%")
    print(f"   Total attempts: {stats['total_attempts']}")
    
    # Cleanup
    demo_file = Path('./memory/demo_memory.json')
    if demo_file.exists():
        demo_file.unlink()


def demo_integration():
    """Demonstrate integrated workflow"""
    print("\n🔄 Demo: Integrated Workflow")
    print("-" * 30)
    
    # Plan a simple task
    planner = TaskPlanner()
    task = "Create a simple greeting function"
    subtasks = planner.plan_task(task)
    
    print(f"\n📋 Task: {task}")
    print(f"   Planned {len(subtasks)} subtasks")
    
    # Simulate executing the first subtask
    executor = CodeExecutor()
    
    # Generate some code (simulating AI generation)
    generated_code = """
def greet(name):
    \"\"\"Generate a friendly greeting\"\"\"
    return f"Hello, {name}! Welcome to Devin Local AI!"

def greet_multiple(names):
    \"\"\"Greet multiple people\"\"\"
    greetings = []
    for name in names:
        greetings.append(greet(name))
    return greetings

# Test the functions
print(greet("Alice"))
print(greet("Bob"))

names = ["Charlie", "Diana"]
for greeting in greet_multiple(names):
    print(greeting)
"""
    
    print("\n🤖 Generated code:")
    print("   def greet(name): ...")
    print("   def greet_multiple(names): ...")
    
    # Execute the code
    result = executor.execute_code(generated_code, "greeting_demo.py")
    
    if result['success']:
        print("\n✅ Execution successful!")
        print("   Output:")
        for line in result['stdout'].strip().split('\n'):
            print(f"     {line}")
    else:
        print(f"\n❌ Execution failed: {result['stderr']}")


def main():
    """Run all demos"""
    print("🧠 Devin Local AI - System Demo")
    print("=" * 40)
    print("This demo shows the system capabilities without requiring a local LLM.")
    print()
    
    try:
        demo_task_planning()
        demo_code_execution()
        demo_error_parsing()
        demo_memory_system()
        demo_integration()
        
        print("\n" + "=" * 40)
        print("🎉 Demo completed successfully!")
        print("\nThis demonstrates the core capabilities of Devin Local AI:")
        print("• Intelligent task planning")
        print("• Safe code execution")
        print("• Advanced error analysis")
        print("• Persistent memory system")
        print("• Integrated workflow")
        print("\nTo use with a real LLM:")
        print("1. Install Ollama or LM Studio")
        print("2. Configure config.yaml")
        print("3. Run: python main.py -i")
        
    except Exception as e:
        print(f"\n💥 Demo failed: {e}")
        print("Please run 'python test_system.py' to check for issues")


if __name__ == "__main__":
    main()
