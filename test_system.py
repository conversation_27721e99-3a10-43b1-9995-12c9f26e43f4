#!/usr/bin/env python3
"""
Test script for Devin Local AI system
Verifies that all components work correctly.
"""

import sys
import yaml
from pathlib import Path

def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from agents.devin_agent import <PERSON>Agent, AgentContext
        from planner.task_planner import TaskPlanner, TaskType
        from executor.runner import CodeExecutor
        from utils.error_parser import ErrorParser
        from memory.memory_manager import MemoryManager
        print("   ✅ All imports successful")
        return True
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print("🧪 Testing configuration...")
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        required_sections = ['llm', 'execution', 'memory', 'planning']
        for section in required_sections:
            if section not in config:
                print(f"   ❌ Missing config section: {section}")
                return False
        
        print("   ✅ Configuration valid")
        return True
    except Exception as e:
        print(f"   ❌ Config loading failed: {e}")
        return False

def test_task_planner():
    """Test task planning functionality"""
    print("🧪 Testing task planner...")
    
    try:
        from planner.task_planner import TaskPlanner
        
        planner = TaskPlanner()
        subtasks = planner.plan_task("Create a simple calculator")
        
        if not subtasks:
            print("   ❌ No subtasks generated")
            return False
        
        if len(subtasks) < 2:
            print("   ❌ Too few subtasks generated")
            return False
        
        print(f"   ✅ Generated {len(subtasks)} subtasks")
        for i, subtask in enumerate(subtasks[:3], 1):
            print(f"      {i}. {subtask.description}")
        
        return True
    except Exception as e:
        print(f"   ❌ Task planner failed: {e}")
        return False

def test_code_executor():
    """Test code execution"""
    print("🧪 Testing code executor...")
    
    try:
        from executor.runner import CodeExecutor

        # Use test config to avoid path issues
        test_config = {
            'timeout': 30,
            'working_directory': './workspace',
            'allowed_imports': ['os', 'sys']
        }

        executor = CodeExecutor(test_config)
        
        # Test simple code execution
        test_code = """
print("Hello from Devin Local AI!")
result = 2 + 2
print(f"2 + 2 = {result}")
"""
        
        result = executor.execute_code(test_code, "test_execution.py")

        if not result['success']:
            print(f"   ❌ Code execution failed: {result['stderr']}")
            return False
        
        if "Hello from Devin Local AI!" not in result['stdout']:
            print("   ❌ Expected output not found")
            return False
        
        print("   ✅ Code execution successful")
        print(f"      Output: {result['stdout'].strip()}")
        return True
    except Exception as e:
        print(f"   ❌ Code executor failed: {e}")
        return False

def test_error_parser():
    """Test error parsing"""
    print("🧪 Testing error parser...")
    
    try:
        from utils.error_parser import ErrorParser
        
        parser = ErrorParser()
        
        # Test error parsing
        test_error = """Traceback (most recent call last):
  File "test.py", line 5, in <module>
    print(undefined_variable)
NameError: name 'undefined_variable' is not defined"""
        
        error_info = parser.parse_error(test_error)
        
        if error_info.error_type != "NameError":
            print(f"   ❌ Wrong error type: {error_info.error_type}")
            return False
        
        if error_info.line_number != 5:
            print(f"   ❌ Wrong line number: {error_info.line_number}")
            return False
        
        print("   ✅ Error parsing successful")
        print(f"      Type: {error_info.error_type}")
        print(f"      Line: {error_info.line_number}")
        print(f"      Suggestion: {error_info.suggestion}")
        return True
    except Exception as e:
        print(f"   ❌ Error parser failed: {e}")
        return False

def test_memory_manager():
    """Test memory management"""
    print("🧪 Testing memory manager...")
    
    try:
        from memory.memory_manager import MemoryManager
        
        # Use test config to avoid interfering with real memory
        test_config = {
            'memory': {
                'file_path': './memory/test_memory.json',
                'max_history': 10,
                'auto_save': True
            }
        }
        
        memory = MemoryManager(test_config)
        
        # Test session management
        session_id = memory.start_session("Test task")
        if not session_id:
            print("   ❌ Failed to start session")
            return False
        
        memory.end_session(True, "print('test')")
        
        # Test statistics
        stats = memory.get_statistics()
        if stats['total_sessions'] < 1:
            print("   ❌ Session not recorded")
            return False
        
        print("   ✅ Memory management successful")
        print(f"      Session ID: {session_id}")
        print(f"      Total sessions: {stats['total_sessions']}")
        
        # Cleanup test file
        test_file = Path('./memory/test_memory.json')
        if test_file.exists():
            test_file.unlink()
        
        return True
    except Exception as e:
        print(f"   ❌ Memory manager failed: {e}")
        return False

def test_directory_structure():
    """Test that all required directories exist"""
    print("🧪 Testing directory structure...")
    
    required_dirs = [
        'agents', 'executor', 'memory', 'planner', 'utils', 
        'logs', 'workspace'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"   ❌ Missing directories: {missing_dirs}")
        return False
    
    print("   ✅ All directories present")
    return True

def main():
    """Run all tests"""
    print("🧠 Devin Local AI - System Test")
    print("=" * 40)
    
    tests = [
        test_directory_structure,
        test_imports,
        test_config_loading,
        test_task_planner,
        test_code_executor,
        test_error_parser,
        test_memory_manager,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"   💥 Test crashed: {e}")
            print()
    
    print("=" * 40)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        print("\nNext steps:")
        print("1. Set up your local LLM (Ollama/LM Studio)")
        print("2. Update config.yaml with your LLM settings")
        print("3. Run: python main.py -i")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before using the system.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
