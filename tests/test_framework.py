"""
Comprehensive Testing Framework for Devin Local AI
Provides automated testing capabilities for generated code and system components.
"""

import sys
import os
import unittest
import pytest
import time
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import json


class TestType(Enum):
    """Types of tests"""
    UNIT = "unit"
    INTEGRATION = "integration"
    FUNCTIONAL = "functional"
    PERFORMANCE = "performance"
    SECURITY = "security"


@dataclass
class TestResult:
    """Result of a test execution"""
    name: str
    type: TestType
    passed: bool
    duration: float
    output: str
    error: Optional[str] = None
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


@dataclass
class TestSuite:
    """Collection of related tests"""
    name: str
    description: str
    tests: List[Callable]
    setup: Optional[Callable] = None
    teardown: Optional[Callable] = None


class CodeTester:
    """Tests generated code for correctness and quality"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.timeout = self.config.get('test_timeout', 30)
        self.temp_dir = Path(tempfile.mkdtemp(prefix="devin_test_"))
    
    def test_syntax(self, code: str) -> TestResult:
        """Test if code has valid Python syntax"""
        start_time = time.time()
        
        try:
            compile(code, '<string>', 'exec')
            return TestResult(
                name="Syntax Check",
                type=TestType.UNIT,
                passed=True,
                duration=time.time() - start_time,
                output="Code syntax is valid"
            )
        except SyntaxError as e:
            return TestResult(
                name="Syntax Check",
                type=TestType.UNIT,
                passed=False,
                duration=time.time() - start_time,
                output="",
                error=f"Syntax error: {e}",
                details={'line': e.lineno, 'offset': e.offset}
            )
    
    def test_execution(self, code: str, expected_output: str = None) -> TestResult:
        """Test if code executes without errors"""
        start_time = time.time()
        
        # Write code to temporary file
        test_file = self.temp_dir / f"test_{int(time.time())}.py"
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(code)
            
            # Execute the code
            result = subprocess.run(
                [sys.executable, str(test_file)],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=self.temp_dir
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                # Check expected output if provided
                if expected_output and expected_output.strip() not in result.stdout:
                    return TestResult(
                        name="Execution Test",
                        type=TestType.FUNCTIONAL,
                        passed=False,
                        duration=duration,
                        output=result.stdout,
                        error=f"Expected output not found: {expected_output}",
                        details={'stdout': result.stdout, 'stderr': result.stderr}
                    )
                
                return TestResult(
                    name="Execution Test",
                    type=TestType.FUNCTIONAL,
                    passed=True,
                    duration=duration,
                    output=result.stdout,
                    details={'stdout': result.stdout, 'stderr': result.stderr}
                )
            else:
                return TestResult(
                    name="Execution Test",
                    type=TestType.FUNCTIONAL,
                    passed=False,
                    duration=duration,
                    output=result.stdout,
                    error=result.stderr,
                    details={'return_code': result.returncode}
                )
                
        except subprocess.TimeoutExpired:
            return TestResult(
                name="Execution Test",
                type=TestType.FUNCTIONAL,
                passed=False,
                duration=self.timeout,
                output="",
                error=f"Test timed out after {self.timeout} seconds"
            )
        except Exception as e:
            return TestResult(
                name="Execution Test",
                type=TestType.FUNCTIONAL,
                passed=False,
                duration=time.time() - start_time,
                output="",
                error=str(e)
            )
        finally:
            # Clean up
            if test_file.exists():
                test_file.unlink()
    
    def test_imports(self, code: str) -> TestResult:
        """Test if all imports in code are available"""
        start_time = time.time()
        
        import ast
        import importlib
        
        try:
            tree = ast.parse(code)
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
            
            # Test each import
            failed_imports = []
            for module_name in imports:
                try:
                    importlib.import_module(module_name.split('.')[0])
                except ImportError:
                    failed_imports.append(module_name)
            
            duration = time.time() - start_time
            
            if failed_imports:
                return TestResult(
                    name="Import Test",
                    type=TestType.UNIT,
                    passed=False,
                    duration=duration,
                    output="",
                    error=f"Failed imports: {failed_imports}",
                    details={'failed_imports': failed_imports, 'all_imports': imports}
                )
            else:
                return TestResult(
                    name="Import Test",
                    type=TestType.UNIT,
                    passed=True,
                    duration=duration,
                    output=f"All imports successful: {imports}",
                    details={'imports': imports}
                )
                
        except Exception as e:
            return TestResult(
                name="Import Test",
                type=TestType.UNIT,
                passed=False,
                duration=time.time() - start_time,
                output="",
                error=str(e)
            )
    
    def test_functions(self, code: str, function_tests: Dict[str, Dict]) -> List[TestResult]:
        """Test specific functions in the code"""
        results = []
        
        # Execute the code to make functions available
        namespace = {}
        try:
            exec(code, namespace)
        except Exception as e:
            return [TestResult(
                name="Function Setup",
                type=TestType.UNIT,
                passed=False,
                duration=0,
                output="",
                error=f"Failed to execute code: {e}"
            )]
        
        # Test each function
        for func_name, test_data in function_tests.items():
            start_time = time.time()
            
            if func_name not in namespace:
                results.append(TestResult(
                    name=f"Function Test: {func_name}",
                    type=TestType.UNIT,
                    passed=False,
                    duration=time.time() - start_time,
                    output="",
                    error=f"Function '{func_name}' not found"
                ))
                continue
            
            func = namespace[func_name]
            test_cases = test_data.get('test_cases', [])
            
            for i, test_case in enumerate(test_cases):
                case_start = time.time()
                
                try:
                    args = test_case.get('args', [])
                    kwargs = test_case.get('kwargs', {})
                    expected = test_case.get('expected')
                    
                    result = func(*args, **kwargs)
                    
                    if expected is not None and result != expected:
                        results.append(TestResult(
                            name=f"Function Test: {func_name} case {i+1}",
                            type=TestType.UNIT,
                            passed=False,
                            duration=time.time() - case_start,
                            output=f"Result: {result}",
                            error=f"Expected {expected}, got {result}",
                            details={'args': args, 'kwargs': kwargs, 'result': result, 'expected': expected}
                        ))
                    else:
                        results.append(TestResult(
                            name=f"Function Test: {func_name} case {i+1}",
                            type=TestType.UNIT,
                            passed=True,
                            duration=time.time() - case_start,
                            output=f"Result: {result}",
                            details={'args': args, 'kwargs': kwargs, 'result': result}
                        ))
                        
                except Exception as e:
                    results.append(TestResult(
                        name=f"Function Test: {func_name} case {i+1}",
                        type=TestType.UNIT,
                        passed=False,
                        duration=time.time() - case_start,
                        output="",
                        error=str(e),
                        details={'args': args, 'kwargs': kwargs}
                    ))
        
        return results
    
    def test_performance(self, code: str, max_execution_time: float = 1.0) -> TestResult:
        """Test code performance"""
        start_time = time.time()
        
        test_file = self.temp_dir / f"perf_test_{int(time.time())}.py"
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(code)
            
            # Execute and measure time
            exec_start = time.time()
            result = subprocess.run(
                [sys.executable, str(test_file)],
                capture_output=True,
                text=True,
                timeout=max_execution_time * 2,  # Allow some buffer
                cwd=self.temp_dir
            )
            exec_time = time.time() - exec_start
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                if exec_time <= max_execution_time:
                    return TestResult(
                        name="Performance Test",
                        type=TestType.PERFORMANCE,
                        passed=True,
                        duration=duration,
                        output=f"Execution time: {exec_time:.3f}s",
                        details={'execution_time': exec_time, 'max_allowed': max_execution_time}
                    )
                else:
                    return TestResult(
                        name="Performance Test",
                        type=TestType.PERFORMANCE,
                        passed=False,
                        duration=duration,
                        output=f"Execution time: {exec_time:.3f}s",
                        error=f"Execution too slow: {exec_time:.3f}s > {max_execution_time}s",
                        details={'execution_time': exec_time, 'max_allowed': max_execution_time}
                    )
            else:
                return TestResult(
                    name="Performance Test",
                    type=TestType.PERFORMANCE,
                    passed=False,
                    duration=duration,
                    output="",
                    error=f"Code execution failed: {result.stderr}"
                )
                
        except subprocess.TimeoutExpired:
            return TestResult(
                name="Performance Test",
                type=TestType.PERFORMANCE,
                passed=False,
                duration=time.time() - start_time,
                output="",
                error=f"Performance test timed out (>{max_execution_time * 2}s)"
            )
        except Exception as e:
            return TestResult(
                name="Performance Test",
                type=TestType.PERFORMANCE,
                passed=False,
                duration=time.time() - start_time,
                output="",
                error=str(e)
            )
        finally:
            if test_file.exists():
                test_file.unlink()
    
    def run_comprehensive_test(self, code: str, test_config: Dict = None) -> List[TestResult]:
        """Run a comprehensive test suite on the code"""
        test_config = test_config or {}
        results = []
        
        # Basic tests
        results.append(self.test_syntax(code))
        results.append(self.test_imports(code))
        results.append(self.test_execution(code, test_config.get('expected_output')))
        
        # Performance test
        max_time = test_config.get('max_execution_time', 1.0)
        results.append(self.test_performance(code, max_time))
        
        # Function tests
        function_tests = test_config.get('function_tests', {})
        if function_tests:
            results.extend(self.test_functions(code, function_tests))
        
        return results
    
    def cleanup(self):
        """Clean up temporary files"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)


class TestReporter:
    """Generates test reports"""
    
    def __init__(self):
        pass
    
    def generate_summary(self, results: List[TestResult]) -> Dict[str, Any]:
        """Generate test summary"""
        total = len(results)
        passed = sum(1 for r in results if r.passed)
        failed = total - passed
        
        by_type = {}
        for result in results:
            test_type = result.type.value
            if test_type not in by_type:
                by_type[test_type] = {'total': 0, 'passed': 0, 'failed': 0}
            
            by_type[test_type]['total'] += 1
            if result.passed:
                by_type[test_type]['passed'] += 1
            else:
                by_type[test_type]['failed'] += 1
        
        total_duration = sum(r.duration for r in results)
        
        return {
            'total_tests': total,
            'passed': passed,
            'failed': failed,
            'success_rate': (passed / total * 100) if total > 0 else 0,
            'total_duration': total_duration,
            'by_type': by_type,
            'failed_tests': [r.name for r in results if not r.passed]
        }
    
    def generate_detailed_report(self, results: List[TestResult]) -> str:
        """Generate detailed test report"""
        summary = self.generate_summary(results)
        
        report = []
        report.append("=" * 60)
        report.append("DEVIN LOCAL AI - TEST REPORT")
        report.append("=" * 60)
        report.append("")
        
        # Summary
        report.append("SUMMARY:")
        report.append(f"  Total Tests: {summary['total_tests']}")
        report.append(f"  Passed: {summary['passed']}")
        report.append(f"  Failed: {summary['failed']}")
        report.append(f"  Success Rate: {summary['success_rate']:.1f}%")
        report.append(f"  Total Duration: {summary['total_duration']:.3f}s")
        report.append("")
        
        # By type
        report.append("BY TYPE:")
        for test_type, stats in summary['by_type'].items():
            report.append(f"  {test_type.upper()}:")
            report.append(f"    Total: {stats['total']}")
            report.append(f"    Passed: {stats['passed']}")
            report.append(f"    Failed: {stats['failed']}")
        report.append("")
        
        # Detailed results
        report.append("DETAILED RESULTS:")
        for result in results:
            status = "PASS" if result.passed else "FAIL"
            report.append(f"  [{status}] {result.name} ({result.duration:.3f}s)")
            
            if result.output:
                report.append(f"    Output: {result.output}")
            
            if result.error:
                report.append(f"    Error: {result.error}")
            
            report.append("")
        
        return "\n".join(report)
    
    def save_report(self, results: List[TestResult], file_path: Path):
        """Save test report to file"""
        report = self.generate_detailed_report(results)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Also save JSON summary
        summary = self.generate_summary(results)
        json_path = file_path.with_suffix('.json')
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2)


def create_code_tester(config: Dict = None) -> CodeTester:
    """
    Create a code tester with the given configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        CodeTester instance
    """
    return CodeTester(config)
