# Importing the math module for division operation
import math

def calculate(num1, num2, operator):
    """
    This function performs basic arithmetic operations.

    Args:
        num1 (float): The first number.
        num2 (float): The second number.
        operator (str): The mathematical operator. It can be '+', '-', '*', '/'.

    Returns:
        float: The result of the operation.

    Raises:
        ValueError: If the operator is not one of the four basic arithmetic operators.
        ZeroDivisionError: If the user tries to divide by zero.
    """

    # Check if the operator is valid
    if operator not in ['+', '-', '*', '/']:
        raise ValueError("Invalid operator. Please use '+', '-', '*', '/'.")

    try:
        # Perform addition
        if operator == '+':
            return num1 + num2

        # Perform subtraction
        elif operator == '-':
            return num1 - num2

        # Perform multiplication
        elif operator == '*':
            return num1 * num2

        # Perform division
        elif operator == '/':
            # Check for division by zero
            if num2 == 0:
                raise ZeroDivisionError("Cannot divide by zero.")
            return num1 / num2

    except Exception as e:
        print(f"An error occurred: {e}")
        return None


# Example usage of the calculate function
if __name__ == "__main__":
    try:
        result = calculate(10, 5, '+')
        print("Result:", result)

        result = calculate(10, 5, '-')
        print("Result:", result)

        result = calculate(10, 5, '*')
        print("Result:", result)

        result = calculate(10, 5, '/')
        print("Result:", result)

    except ValueError as ve:
        print(f"Error: {ve}")

    except ZeroDivisionError as zde:
        print(f"Error: {zde}")