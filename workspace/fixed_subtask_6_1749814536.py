# Importing the unittest module for testing purposes
import unittest

def calculator(num1, num2, operation):
    """
    A simple calculator function to perform addition, subtraction, multiplication and division.

    Args:
        num1 (float): The first number.
        num2 (float): The second number.
        operation (str): The mathematical operation to be performed. It can be 'add', 'subtract', 'multiply' or 'divide'.

    Returns:
        float: The result of the mathematical operation.

    Raises:
        ValueError: If the operation is not one of the four basic arithmetic operations.
        ZeroDivisionError: If the second number is zero and the operation is division.
    """
    
    # Check if the operation is valid
    if operation not in ['add', 'subtract', 'multiply', 'divide']:
        raise ValueError("Invalid operation. It should be 'add', 'subtract', 'multiply' or 'divide'.")

    try:
        # Perform the mathematical operation based on the input
        if operation == 'add':
            return num1 + num2
        elif operation == 'subtract':
            return num1 - num2
        elif operation == 'multiply':
            return num1 * num2
        else:  # operation is 'divide'
            if num2 == 0:
                raise ZeroDivisionError("Cannot divide by zero.")
            return num1 / num2

    except ZeroDivisionError as e:
        print(f"An error occurred: {e}")
        return None

    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None


class TestCalculator(unittest.TestCase):
    def test_add(self):
        self.assertEqual(calculator(5, 3, 'add'), 8)

    def test_subtract(self):
        self.assertEqual(calculator(10, 4, 'subtract'), 6)

    def test_multiply(self):
        self.assertEqual(calculator(7, 2, 'multiply'), 14)

    def test_divide(self):
        self.assertEqual(calculator(9, 3, 'divide'), 3)

    def test_invalid_operation(self):
        with self.assertRaises(ValueError):
            calculator(5, 3, 'invalid')

    def test_division_by_zero(self):
        with self.assertRaises(ZeroDivisionError):
            calculator(10, 0, 'divide')


if __name__ == '__main__':
    unittest.main()