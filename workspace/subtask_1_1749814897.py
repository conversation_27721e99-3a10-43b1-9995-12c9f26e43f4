# Importing required modules
import os

def create_project_structure():
    """
    Function to create a basic project structure.
    
    This function will create the following directories:
    - todo_list (main directory)
        - data (directory to store data files)
        - src (directory for source code)
            - models (directory for data models)
            - services (directory for business logic)
            - utils (directory for utility functions)
        - tests (directory for unit tests)
    """
    
    # Define the project structure
    project_structure = {
        'todo_list': {
            'data': {},
            'src': {
                'models': {},
                'services': {},
                'utils': {}
            },
            'tests': {}
        }
    }

    try:
        # Create the main directory
        os.mkdir('todo_list')
        
        # Change into the main directory
        os.chdir('todo_list')

        # Create subdirectories
        for dir_name, _ in project_structure['todo_list'].items():
            if dir_name != 'src':
                os.mkdir(dir_name)
            
            else:
                for sub_dir_name, _ in project_structure['todo_list']['src'].items():
                    os.mkdir(f'{dir_name}/{sub_dir_name}')

        # Create the tests directory
        os.mkdir('tests')

    except FileExistsError:
        print("Project structure already exists.")
    
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    create_project_structure()