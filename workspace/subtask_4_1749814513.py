# Import the unittest module for testing
import unittest

def calculate(num1, num2, operation):
    """
    A simple calculator function that performs addition, subtraction, multiplication and division.
    
    Args:
        num1 (float): The first number.
        num2 (float): The second number.
        operation (str): The mathematical operation to be performed. It can be 'add', 'subtract', 'multiply' or 'divide'.
        
    Returns:
        float: The result of the mathematical operation.
    
    Raises:
        ValueError: If the operation is not one of the four basic arithmetic operations.
        ZeroDivisionError: If the second number is zero and the operation is division.
    """
    if operation == 'add':
        return num1 + num2
    elif operation == 'subtract':
        return num1 - num2
    elif operation == 'multiply':
        return num1 * num2
    elif operation == 'divide':
        if num2 != 0:
            return num1 / num2
        else:
            raise ZeroDivisionError("Cannot divide by zero.")
    else:
        raise ValueError("Invalid operation. It can be 'add', 'subtract', 'multiply' or 'divide'.")

class TestCalculator(unittest.TestCase):
    def test_add(self):
        self.assertEqual(calculate(5, 3, 'add'), 8)
        
    def test_subtract(self):
        self.assertEqual(calculate(5, 3, 'subtract'), 2)
        
    def test_multiply(self):
        self.assertEqual(calculate(5, 3, 'multiply'), 15)
        
    def test_divide(self):
        self.assertEqual(calculate(10, 2, 'divide'), 5)
        
    def test_invalid_operation(self):
        with self.assertRaises(ValueError):
            calculate(5, 3, 'invalid')
            
    def test_zero_division(self):
        with self.assertRaises(ZeroDivisionError):
            calculate(5, 0, 'divide')

if __name__ == '__main__':
    unittest.main()