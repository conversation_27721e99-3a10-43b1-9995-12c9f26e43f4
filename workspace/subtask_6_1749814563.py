import unittest

# Define a function to perform calculator operations
def calculator(num1, num2, operation):
    """
    Perform basic arithmetic operations.

    Args:
        num1 (float): The first number.
        num2 (float): The second number.
        operation (str): The mathematical operation to be performed. Can be 'add', 'subtract', 'multiply' or 'divide'.

    Returns:
        float: The result of the operation.

    Raises:
        ValueError: If an invalid operation is provided.
        ZeroDivisionError: If division by zero is attempted.
    """
    if operation == 'add':
        return num1 + num2
    elif operation == 'subtract':
        return num1 - num2
    elif operation == 'multiply':
        return num1 * num2
    elif operation == 'divide':
        if num2 != 0:
            return num1 / num2
        else:
            raise ZeroDivisionError("Cannot divide by zero.")
    else:
        raise ValueError("Invalid operation. Please choose from 'add', 'subtract', 'multiply' or 'divide'.")

# Define a test class for the calculator function
class TestCalculator(unittest.TestCase):
    def test_addition(self):
        self.assertEqual(calculator(10, 5, 'add'), 15)

    def test_subtraction(self):
        self.assertEqual(calculator(10, 5, 'subtract'), 5)

    def test_multiplication(self):
        self.assertEqual(calculator(10, 5, 'multiply'), 50)

    def test_division(self):
        self.assertEqual(calculator(10, 2, 'divide'), 5)

    def test_division_by_zero(self):
        with self.assertRaises(ZeroDivisionError):
            calculator(10, 0, 'divide')

# Run the tests
if __name__ == '__main__':
    unittest.main()