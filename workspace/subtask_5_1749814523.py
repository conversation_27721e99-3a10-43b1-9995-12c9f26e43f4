# Importing necessary modules
import unittest

class Calculator:
    def add(self, a, b):
        """Return the sum of two numbers."""
        return a + b
    
    def subtract(self, a, b):
        """Return the difference between two numbers."""
        if b == 0:
            raise ValueError("Cannot divide by zero.")
        return a - b
    
    def multiply(self, a, b):
        """Return the product of two numbers."""
        return a * b
    
    def divide(self, a, b):
        """Return the quotient of two numbers."""
        if b == 0:
            raise ValueError("Cannot divide by zero.")
        return a / b

class TestCalculator(unittest.TestCase):

    # Test addition
    def test_add(self):
        calculator = Calculator()
        self.assertEqual(calculator.add(5, 3), 8)
    
    # Test subtraction
    def test_subtract(self):
        calculator = Calculator()
        self.assertEqual(calculator.subtract(10, 4), 6)
    
    # Test multiplication
    def test_multiply(self):
        calculator = Calculator()
        self.assertEqual(calculator.multiply(7, 2), 14)
    
    # Test division
    def test_divide(self):
        calculator = Calculator()
        self.assertEqual(calculator.divide(9, 3), 3)

    # Test division by zero
    def test_divide_by_zero(self):
        calculator = Calculator()
        with self.assertRaises(ValueError):
            calculator.divide(5, 0)
    
    # Test addition of negative numbers
    def test_add_negative_numbers(self):
        calculator = Calculator()
        self.assertEqual(calculator.add(-3, -7), -10)

if __name__ == '__main__':
    unittest.main()