
def greet(name):
    """Generate a friendly greeting"""
    return f"Hello, {name}! Welcome to Devin Local AI!"

def greet_multiple(names):
    """Greet multiple people"""
    greetings = []
    for name in names:
        greetings.append(greet(name))
    return greetings

# Test the functions
print(greet("<PERSON>"))
print(greet("<PERSON>"))

names = ["<PERSON>", "<PERSON>"]
for greeting in greet_multiple(names):
    print(greeting)
