# calculator_project.py

"""
Simple Calculator Project Structure
"""

import os

def create_project_structure():
    """
    Create a basic project structure for the calculator.
    
    This function will create two directories, 'src' and 'tests', 
    to hold the source code and test files respectively.
    """

    # Define the directory paths
    PROJECT_ROOT = os.getcwd()
    SRC_DIR = os.path.join(PROJECT_ROOT, 'src')
    TESTS_DIR = os.path.join(PROJECT_ROOT, 'tests')

    try:
        # Create the project root directory if it doesn't exist
        if not os.path.exists(PROJECT_ROOT):
            os.makedirs(PROJECT_ROOT)

        # Create the src and tests directories
        if not os.path.exists(SRC_DIR):
            os.makedirs(SRC_DIR)
        
        if not os.path.exists(TESTS_DIR):
            os.makedirs(TESTS_DIR)

        print("Project structure created successfully.")

    except Exception as e:
        print(f"Error creating project structure: {e}")

if __name__ == "__main__":
    create_project_structure()