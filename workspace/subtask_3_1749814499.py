# Importing math module for division operation
import math

def calculator(num1, num2, operator):
    """
    This function performs basic arithmetic operations like addition, subtraction, 
    multiplication and division.

    Args:
        num1 (float): The first number.
        num2 (float): The second number.
        operator (str): The mathematical operation to be performed. It can be '+', '-', '*', '/'.

    Returns:
        float: The result of the mathematical operation.

    Raises:
        ValueError: If the input numbers are not numeric or if the operator is invalid.
        ZeroDivisionError: If the user tries to divide by zero.
    """

    # Checking if both inputs are numeric
    try:
        num1 = float(num1)
        num2 = float(num2)
    except ValueError as e:
        print("Error: Both inputs must be numbers.")
        return None

    # Performing addition operation
    if operator == '+':
        result = num1 + num2
        print(f"{num1} + {num2} = {result}")
        return result

    # Performing subtraction operation
    elif operator == '-':
        result = num1 - num2
        print(f"{num1} - {num2} = {result}")
        return result

    # Performing multiplication operation
    elif operator == '*':
        result = num1 * num2
        print(f"{num1} * {num2} = {result}")
        return result

    # Performing division operation
    elif operator == '/':
        try:
            if num2 != 0:
                result = num1 / num2
                print(f"{num1} / {num2} = {result}")
                return result
            else:
                raise ZeroDivisionError("Cannot divide by zero.")
        except ZeroDivisionError as e:
            print(e)
            return None

    # If the operator is invalid, print an error message and return None
    else:
        print("Invalid operator. Please use '+', '-', '*', '/'")
        return None


# Example usage of the calculator function
if __name__ == "__main__":
    num1 = 10
    num2 = 5
    operator = '/'
    result = calculator(num1, num2, operator)