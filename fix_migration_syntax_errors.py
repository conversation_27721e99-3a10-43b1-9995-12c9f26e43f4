#!/usr/bin/env python3
"""
Fix Migration Syntax Errors

This script fixes the syntax errors introduced by the migration script,
specifically the malformed f-strings where function calls got corrupted.
"""

import re
import os
from pathlib import Path

def fix_malformed_fstrings(file_path):
    """Fix malformed f-strings in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix patterns like: f"text {func(, parent=self)} more text"
        # Should be: f"text {func()} more text"
        
        # Pattern 1: category.title(, parent=self)
        content = re.sub(
            r'(\w+)\.title\(\s*,\s*parent=self\s*\)',
            r'\1.title()',
            content
        )
        
        # Pattern 2: psutil.cpu_count(, parent=self)
        content = re.sub(
            r'psutil\.cpu_count\(\s*,\s*parent=self\s*\)',
            r'psutil.cpu_count()',
            content
        )
        
        # Pattern 3: Any function call with (, parent=self)
        content = re.sub(
            r'(\w+\.\w+)\(\s*,\s*parent=self\s*\)',
            r'\1()',
            content
        )
        
        # Pattern 4: Function calls in f-strings with malformed syntax
        content = re.sub(
            r'(\w+)\(\s*,\s*parent=self\s*\)',
            r'\1()',
            content
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed syntax errors in: {file_path}")
            return True
        else:
            print(f"ℹ️  No syntax errors found in: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    """Fix all syntax errors in the project"""
    print("🔧 Fixing migration syntax errors...")
    
    # Files that are known to have syntax errors
    problem_files = [
        "src/knowledge_app/ui/settings_menu/settings_menu.py",
        "src/knowledge_app/ui/training_dialog.py"
    ]
    
    fixed_count = 0
    
    for file_path in problem_files:
        if os.path.exists(file_path):
            if fix_malformed_fstrings(file_path):
                fixed_count += 1
        else:
            print(f"⚠️  File not found: {file_path}")
    
    # Also scan all Python files for similar issues
    print("\n🔍 Scanning all Python files for similar issues...")
    
    src_dir = Path("src/knowledge_app")
    if src_dir.exists():
        for py_file in src_dir.rglob("*.py"):
            if str(py_file) not in problem_files:  # Skip already processed files
                if fix_malformed_fstrings(py_file):
                    fixed_count += 1
    
    print(f"\n✅ Fixed syntax errors in {fixed_count} files")
    print("🎉 Migration syntax errors resolved!")

if __name__ == "__main__":
    main()
