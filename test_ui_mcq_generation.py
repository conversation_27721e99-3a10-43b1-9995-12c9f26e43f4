#!/usr/bin/env python3
"""
Test MCQ generation as it would be called from the UI
"""

import sys
import os
import asyncio
import logging

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_ui_style_generation():
    """Test MCQ generation as it would be called from the UI"""
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        from knowledge_app.core.config_manager import get_config
        
        # Get configuration and MCQ manager (same as UI)
        config = get_config()
        mcq_manager = get_mcq_manager(config)
        
        # Enable instant mode (same as UI default)
        mcq_manager.set_instant_mode(True)
        
        logger.info("🧪 Testing UI-style MCQ generation...")
        
        # Test 1: Basic context generation (like when user provides text)
        context = "Magnetism is a fundamental force of nature that arises from the motion of electric charges. Magnetic fields can attract or repel magnetic materials."
        difficulty = "medium"
        
        logger.info("🧪 Test 1: Basic context generation")
        result1 = await mcq_manager.generate_quiz_async(context, difficulty)
        
        logger.info("📝 Result 1:")
        logger.info(f"  Question: '{result1.get('question', 'EMPTY')}'")
        logger.info(f"  Options: {result1.get('options', {})}")
        logger.info(f"  Correct: {result1.get('correct', 'NONE')}")
        
        # Test 2: Parameter-based generation (like when user selects topic)
        quiz_params = {
            'topic': 'physics',
            'difficulty': 'medium',
            'cognitive_level': 'understanding'
        }
        
        logger.info("🧪 Test 2: Parameter-based generation")
        result2 = await mcq_manager.generate_quiz_with_params_async(quiz_params)
        
        logger.info("📝 Result 2:")
        logger.info(f"  Question: '{result2.get('question', 'EMPTY')}'")
        logger.info(f"  Options: {result2.get('options', {})}")
        logger.info(f"  Correct: {result2.get('correct', 'NONE')}")
        
        # Check if both results are good
        result1_good = (result1.get('question') and 
                       result1.get('question').strip() != "" and
                       "The primary equation" not in result1.get('question', '') and
                       len(result1.get('options', {})) == 4)
        
        result2_good = (result2.get('question') and 
                       result2.get('question').strip() != "" and
                       "The primary equation" not in result2.get('question', '') and
                       len(result2.get('options', {})) == 4)
        
        if result1_good and result2_good:
            logger.info("✅ Both UI-style generations successful!")
            return True
        else:
            logger.error("❌ One or both UI-style generations failed")
            logger.error(f"  Result 1 good: {result1_good}")
            logger.error(f"  Result 2 good: {result2_good}")
            return False
            
    except Exception as e:
        logger.error(f"❌ UI-style test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run UI-style MCQ generation test"""
    print("🔧 Testing UI-Style MCQ Generation")
    print("=" * 50)
    
    # Test UI-style generation
    success = asyncio.run(test_ui_style_generation())
    
    # Summary
    print("\n📊 UI-Style Test Results")
    print("=" * 50)
    
    if success:
        print("🎉 SUCCESS! The Knowledge App should now generate proper MCQs!")
        print("✅ No more empty questions")
        print("✅ No more generic 'primary equation' fallbacks")
        print("✅ Proper instant generation working")
        print("\n🚀 The app is ready to use!")
    else:
        print("❌ FAILED! There are still issues with MCQ generation.")
        print("⚠️ Check the logs above for details.")

if __name__ == "__main__":
    main()
