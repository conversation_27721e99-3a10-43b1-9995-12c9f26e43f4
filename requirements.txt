# Core dependencies for Devin Local AI
pyyaml>=6.0
requests>=2.31.0
ollama>=0.1.7
openai>=1.3.0  # For LM Studio compatibility

# Agent framework (choose one)
langgraph>=0.0.40
# crewai>=0.1.0  # Alternative option

# Code execution and parsing
subprocess32>=3.5.4; python_version < '3.0'
psutil>=5.9.0

# File and data handling
pathlib>=1.0.1
json5>=0.9.0

# Optional UI dependencies
streamlit>=1.28.0  # For web UI
gradio>=3.50.0     # Alternative UI
click>=8.1.0       # For CLI

# Development and testing
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# Optional sandbox dependencies
docker>=6.1.0      # For containerized execution
