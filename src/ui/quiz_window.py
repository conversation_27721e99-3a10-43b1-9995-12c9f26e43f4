from PyQt5.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, 
                           QPushButton, QLabel, QRadioButton, 
                           QButtonGroup, QProgressBar)
from PyQt5.QtCore import Qt, QTimer
from ..core.quiz_engine import QuizEngine

class QuizWindow(QMainWindow):
    def __init__(self, quiz_engine: QuizEngine, parent=None):
        super().__init__(parent)
        self.quiz_engine = quiz_engine
        self.setup_ui()
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_timer)
        self.time_remaining = 30  # seconds per question

    def setup_ui(self):
        """Setup the quiz window UI"""
        self.setWindowTitle("Knowledge Quiz")
        self.setMinimumSize(600, 400)

        # Central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Question label
        self.question_label = QLabel()
        self.question_label.setWordWrap(True)
        layout.addWidget(self.question_label)

        # Options
        self.option_group = QButtonGroup()
        self.option_buttons = []
        for i in range(4):
            option = QRadioButton()
            self.option_buttons.append(option)
            self.option_group.addButton(option, i)
            layout.addWidget(option)

        # Timer and progress
        self.timer_label = QLabel("Time: 30")
        layout.addWidget(self.timer_label)
        
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        # Submit button
        self.submit_button = QPushButton("Submit")
        self.submit_button.clicked.connect(self.submit_answer)
        layout.addWidget(self.submit_button)

        # Start the quiz
        self.start_quiz()

    def start_quiz(self):
        """Start a new quiz"""
        self.quiz_engine.start_quiz()
        self.show_question()

    def show_question(self):
        """Display the current question"""
        question = self.quiz_engine.next_question()
        if not question:
            self.show_results()
            return

        self.question_label.setText(question.text)
        for i, option in enumerate(question.options):
            self.option_buttons[i].setText(option)
            self.option_buttons[i].setChecked(False)

        self.time_remaining = 30
        self.timer.start(1000)  # Update every second
        self.update_progress()

    def submit_answer(self):
        """Handle answer submission"""
        selected = self.option_group.checkedId()
        if selected == -1:
            return

        self.timer.stop()
        is_correct = self.quiz_engine.check_answer(selected)
        
        # Visual feedback
        self.option_buttons[selected].setStyleSheet(
            "color: green;" if is_correct else "color: red;"
        )
        
        QTimer.singleShot(1000, self.show_question)  # Show next question after 1 second

    def update_timer(self):
        """Update the timer display"""
        self.time_remaining -= 1
        self.timer_label.setText(f"Time: {self.time_remaining}")
        
        if self.time_remaining <= 0:
            self.timer.stop()
            self.submit_answer()

    def update_progress(self):
        """Update the progress bar"""
        progress = self.quiz_engine.get_progress()
        total = self.quiz_engine.max_questions
        current = progress['questions_answered']
        self.progress_bar.setValue(int((current / total) * 100))

    def show_results(self):
        """Display quiz results"""
        progress = self.quiz_engine.get_progress()
        self.question_label.setText(
            f"Quiz Complete!\n\n"
            f"Score: {progress['score']}\n"
            f"Correct Answers: {progress['correct_answers']}\n"
            f"Total Questions: {progress['questions_answered']}"
        )
        
        # Hide quiz elements
        for button in self.option_buttons:
            button.hide()
        self.submit_button.hide()
        self.timer_label.hide()
        self.progress_bar.hide() 