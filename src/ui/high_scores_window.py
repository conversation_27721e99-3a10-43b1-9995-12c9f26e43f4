from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QLabel)
from PyQt5.QtCore import Qt
from datetime import datetime

class HighScoresWindow(QMainWindow):
    def __init__(self, config, parent=None):
        super().__init__(parent)
        self.config = config
        self.setup_ui()
        self.load_scores()

    def setup_ui(self):
        """Setup the high scores window UI"""
        self.setWindowTitle("High Scores")
        self.setMinimumSize(500, 400)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Title
        title = QLabel("High Scores")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold;")
        layout.addWidget(title)

        # Scores table
        self.scores_table = QTableWidget()
        self.scores_table.setColumnCount(4)
        self.scores_table.setHorizontalHeaderLabels(["Rank", "Score", "Correct Answers", "Date"])
        self.scores_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.scores_table)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)

    def load_scores(self):
        """Load and display high scores"""
        high_scores = self.config.get('high_scores', [])
        self.scores_table.setRowCount(len(high_scores))

        for i, score in enumerate(high_scores):
            # Rank
            self.scores_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            # Score
            self.scores_table.setItem(i, 1, QTableWidgetItem(str(score['score'])))
            # Correct Answers
            self.scores_table.setItem(i, 2, QTableWidgetItem(
                f"{score['correct_answers']}/{score['total_questions']}"
            ))
            # Date
            date = datetime.fromisoformat(score['date'])
            self.scores_table.setItem(i, 3, QTableWidgetItem(
                date.strftime("%Y-%m-%d %H:%M")
            ))

        self.scores_table.resizeColumnsToContents() 