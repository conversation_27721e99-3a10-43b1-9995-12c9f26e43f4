"""
Seductive Main Menu - The Art of UI Seduction
A captivating, animated interface that responds to user touch with elegance.
"""

import logging
import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGridLayout, QGraphicsDropShadowEffect, QGraphicsOpacityEffect
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve,
    QParallelAnimationGroup, QSequentialAnimationGroup, QTimer, QRect
)
from PyQt5.QtGui import (
    QFont, QColor, QPainter, QPen, QBrush, QLinearGradient,
    QRadialGradient, QPalette, QPixmap
)
from PyQt5.QtMultimedia import QSoundEffect, QSound
from PyQt5.QtCore import QUrl

from ..utils.qt_safe_access import (
    SafeQtObjectManager, safe_qt_call, safe_qt_operation, is_qt_object_valid
)

logger = logging.getLogger(__name__)

class RippleButton(QPushButton):
    """Seductive button with ripple effect and dynamic shadows - Qt-safe implementation"""

    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.ripple_animations = []

        # Safe Qt object manager for handling graphics effects
        self.qt_manager = SafeQtObjectManager()

        # Initialize shadow effect safely
        self._init_shadow_effect()

        # Sound effects
        self.setup_sounds()

        # Track hover state to prevent rapid recreation
        self._is_hovered = False
        self._recreation_in_progress = False

    def _init_shadow_effect(self):
        """Initialize shadow effect with safe Qt object management"""
        def create_shadow_effect():
            try:
                effect = QGraphicsDropShadowEffect()
                effect.setBlurRadius(15)
                effect.setColor(QColor(0, 0, 0, 80))
                effect.setOffset(0, 4)
                return effect
            except Exception as e:
                logger.warning(f"Failed to create shadow effect: {e}")
                return None

        def create_shadow_animation(effect):
            try:
                if effect is None:
                    return None
                animation = QPropertyAnimation(effect, b"blurRadius")
                animation.setDuration(200)
                animation.setEasingCurve(QEasingCurve.OutCubic)
                return animation
            except Exception as e:
                logger.warning(f"Failed to create shadow animation: {e}")
                return None

        # Create shadow effect safely
        shadow_effect = safe_qt_operation(
            create_shadow_effect,
            fallback=None,
            error_message="Failed to create shadow effect"
        )

        if shadow_effect:
            # Register with Qt manager
            self.qt_manager.register_object(
                "shadow_effect",
                shadow_effect,
                create_shadow_effect
            )

            # Apply effect safely
            safe_qt_operation(
                lambda: self.setGraphicsEffect(shadow_effect),
                error_message="Failed to set graphics effect"
            )

            # Create animation safely
            shadow_animation = safe_qt_operation(
                lambda: create_shadow_animation(shadow_effect),
                fallback=None,
                error_message="Failed to create shadow animation"
            )

            if shadow_animation:
                self.qt_manager.register_object(
                    "shadow_animation",
                    shadow_animation,
                    lambda: create_shadow_animation(self.qt_manager.get_object("shadow_effect"))
                )

    def setup_sounds(self):
        """Setup UI sound effects"""
        try:
            # Create sound effects (we'll use system sounds for now)
            self.click_sound = QSoundEffect()
            self.hover_sound = QSoundEffect()
            
            # You can add custom sound files here
            # self.click_sound.setSource(QUrl.fromLocalFile("sounds/click.wav"))
            # self.hover_sound.setSource(QUrl.fromLocalFile("sounds/hover.wav"))
            
        except Exception as e:
            logger.warning(f"Could not setup sound effects: {e}")
            self.click_sound = None
            self.hover_sound = None
    
    def mousePressEvent(self, event):
        """Create ripple effect on click"""
        super().mousePressEvent(event)
        
        # Play click sound
        if self.click_sound:
            try:
                self.click_sound.play()
            except:
                pass
        
        # Create ripple effect
        self.create_ripple(event.pos())
    
    def create_ripple(self, position):
        """Create beautiful ripple animation"""
        # Create ripple widget
        ripple = QWidget(self)
        ripple.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 100);
                border-radius: 15px;
            }
        """)
        
        # Start small at click position
        start_size = 30
        end_size = max(self.width(), self.height()) * 1.5
        
        ripple.setGeometry(
            position.x() - start_size//2,
            position.y() - start_size//2,
            start_size, start_size
        )
        ripple.show()
        
        # Animate ripple expansion and fade
        expand_animation = QPropertyAnimation(ripple, b"geometry")
        expand_animation.setDuration(400)
        expand_animation.setEasingCurve(QEasingCurve.OutCubic)
        expand_animation.setStartValue(ripple.geometry())
        expand_animation.setEndValue(QRect(
            int(position.x() - end_size//2),
            int(position.y() - end_size//2),
            int(end_size), int(end_size)
        ))
        
        # Fade out animation
        opacity_effect = QGraphicsOpacityEffect()
        ripple.setGraphicsEffect(opacity_effect)
        
        fade_animation = QPropertyAnimation(opacity_effect, b"opacity")
        fade_animation.setDuration(400)
        fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        fade_animation.setStartValue(1.0)
        fade_animation.setEndValue(0.0)
        
        # Group animations
        ripple_group = QParallelAnimationGroup()
        ripple_group.addAnimation(expand_animation)
        ripple_group.addAnimation(fade_animation)
        
        # Clean up after animation
        ripple_group.finished.connect(lambda: ripple.deleteLater())
        
        ripple_group.start()
        self.ripple_animations.append(ripple_group)
    
    def enterEvent(self, event):
        """Enhanced hover effect with glowing shadow - Qt-safe implementation"""
        super().enterEvent(event)

        # Prevent rapid recreation during hover events
        if self._recreation_in_progress:
            return

        self._is_hovered = True

        # Play hover sound safely
        if hasattr(self, 'hover_sound') and self.hover_sound:
            safe_qt_operation(
                lambda: self.hover_sound.play(),
                error_message="Failed to play hover sound"
            )

        # Apply hover effect safely
        self._apply_hover_effect()
    
    def leaveEvent(self, event):
        """Return to normal shadow - Qt-safe implementation"""
        super().leaveEvent(event)

        # Prevent rapid recreation during hover events
        if self._recreation_in_progress:
            return

        self._is_hovered = False

        # Remove hover effect safely
        self._remove_hover_effect()

    def _apply_hover_effect(self):
        """Apply hover effect safely"""
        if self._recreation_in_progress:
            return

        # Get shadow effect safely
        shadow_effect = self.qt_manager.get_object("shadow_effect", auto_recreate=True)
        if not shadow_effect:
            logger.debug("Shadow effect not available for hover")
            return

        # Recreate animation with current shadow effect
        shadow_animation = self._create_animation_for_effect(shadow_effect)
        if not shadow_animation:
            logger.debug("Could not create animation for hover effect")
            return

        # Apply hover effect safely
        def apply_effect():
            safe_qt_call(shadow_animation, "setStartValue", 15)
            safe_qt_call(shadow_animation, "setEndValue", 25)
            safe_qt_call(shadow_effect, "setColor", QColor(79, 70, 229, 120))
            safe_qt_call(shadow_animation, "start")

        safe_qt_operation(apply_effect, error_message="Failed to apply hover effect")

    def _remove_hover_effect(self):
        """Remove hover effect safely"""
        if self._recreation_in_progress:
            return

        # Get shadow effect safely
        shadow_effect = self.qt_manager.get_object("shadow_effect", auto_recreate=True)
        if not shadow_effect:
            logger.debug("Shadow effect not available for leave")
            return

        # Recreate animation with current shadow effect
        shadow_animation = self._create_animation_for_effect(shadow_effect)
        if not shadow_animation:
            logger.debug("Could not create animation for leave effect")
            return

        # Remove hover effect safely
        def remove_effect():
            safe_qt_call(shadow_animation, "setStartValue", 25)
            safe_qt_call(shadow_animation, "setEndValue", 15)
            safe_qt_call(shadow_effect, "setColor", QColor(0, 0, 0, 80))
            safe_qt_call(shadow_animation, "start")

        safe_qt_operation(remove_effect, error_message="Failed to remove hover effect")

    def _create_animation_for_effect(self, effect):
        """Create a new animation for the given effect"""
        try:
            if not effect or not is_qt_object_valid(effect):
                return None

            animation = QPropertyAnimation(effect, b"blurRadius")
            animation.setDuration(200)
            animation.setEasingCurve(QEasingCurve.OutCubic)
            return animation
        except Exception as e:
            logger.debug(f"Failed to create animation: {e}")
            return None

    def cleanup(self):
        """Clean up Qt objects safely"""
        try:
            # Clean up ripple animations
            for animation in self.ripple_animations:
                if is_qt_object_valid(animation):
                    safe_qt_call(animation, "stop")
            self.ripple_animations.clear()

            # Clean up Qt manager
            self.qt_manager.cleanup()

        except Exception as e:
            logger.warning(f"Error during RippleButton cleanup: {e}")

    def __del__(self):
        """Destructor - ensure cleanup"""
        try:
            self.cleanup()
        except:
            pass  # Ignore errors during destruction

class SeductiveMainMenu(QWidget):
    """The Art of Seduction in UI Design - Main Menu"""
    
    # Signals
    start_quiz_requested = pyqtSignal()
    train_model_requested = pyqtSignal()
    settings_requested = pyqtSignal()
    exit_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.entrance_animations = []

        # Initialize enterprise design system integration
        try:
            from .enterprise_design_system import get_design_system
            from .enterprise_style_manager import get_style_manager
            self.design_system = get_design_system()
            self.style_manager = get_style_manager()
            logger.debug("✅ Enterprise design system integrated with SeductiveMainMenu")
        except ImportError as e:
            logger.warning(f"⚠️ Enterprise design system not available: {e}")
            self.design_system = None
            self.style_manager = None

        self.init_ui()
        self.apply_seductive_styling()
        self.animate_entrance()
        
    def init_ui(self):
        """Initialize the seductive UI"""
        # Main layout with royal spacing
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(50, 50, 50, 50)
        main_layout.setSpacing(40)
        
        # Header with elegance
        self.create_royal_header(main_layout)
        
        # Content with motion
        self.create_seductive_content(main_layout)
        
        # Footer with finesse
        self.create_elegant_footer(main_layout)
        
    def create_royal_header(self, parent_layout):
        """Create header with royal elegance"""
        header_layout = QVBoxLayout()
        header_layout.setSpacing(20)
        
        # Majestic title
        self.title = QLabel("✨ Knowledge Quest ✨")
        self.title.setObjectName("royalTitle")
        self.title.setAlignment(Qt.AlignCenter)
        self.title.setFont(QFont("Segoe UI", 36, QFont.Bold))
        
        # Add glow effect to title
        title_glow = QGraphicsDropShadowEffect()
        title_glow.setBlurRadius(30)
        title_glow.setColor(QColor(79, 70, 229, 150))
        title_glow.setOffset(0, 0)
        self.title.setGraphicsEffect(title_glow)
        
        header_layout.addWidget(self.title)
        
        # Seductive subtitle
        self.subtitle = QLabel("Where Intelligence Meets Elegance")
        self.subtitle.setObjectName("seductiveSubtitle")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setFont(QFont("Segoe UI", 18))
        header_layout.addWidget(self.subtitle)
        
        # Enchanting welcome
        self.welcome = QLabel("Embark on a journey of intellectual seduction...")
        self.welcome.setObjectName("enchantingWelcome")
        self.welcome.setAlignment(Qt.AlignCenter)
        self.welcome.setFont(QFont("Segoe UI", 14))
        header_layout.addWidget(self.welcome)
        
        parent_layout.addLayout(header_layout)
        
    def create_seductive_content(self, parent_layout):
        """Create content with seductive appeal"""
        content_layout = QVBoxLayout()
        content_layout.setSpacing(30)
        
        # Primary seductive action
        self.start_btn = RippleButton("🎯 Begin Your Quest")
        self.start_btn.setObjectName("primarySeductive")
        self.start_btn.setFont(QFont("Segoe UI", 20, QFont.Bold))
        self.start_btn.setMinimumHeight(90)
        self.start_btn.clicked.connect(self._on_start_quiz)
        content_layout.addWidget(self.start_btn)
        
        # Secondary seductive actions
        actions_layout = QGridLayout()
        actions_layout.setSpacing(20)
        
        # Train AI Model with allure
        self.train_btn = RippleButton("🧠 Train AI Model")
        self.train_btn.setObjectName("secondarySeductive")
        self.train_btn.setFont(QFont("Segoe UI", 16, QFont.Bold))
        self.train_btn.setMinimumHeight(70)
        self.train_btn.clicked.connect(self._on_train_model)
        actions_layout.addWidget(self.train_btn, 0, 0)
        
        # Settings with sophistication
        self.settings_btn = RippleButton("⚙️ Refine Experience")
        self.settings_btn.setObjectName("tertiarySeductive")
        self.settings_btn.setFont(QFont("Segoe UI", 16, QFont.Bold))
        self.settings_btn.setMinimumHeight(70)
        self.settings_btn.clicked.connect(self._on_settings)
        actions_layout.addWidget(self.settings_btn, 0, 1)
        
        content_layout.addLayout(actions_layout)
        parent_layout.addLayout(content_layout)
        
    def create_elegant_footer(self, parent_layout):
        """Create footer with elegant touches"""
        footer_layout = QHBoxLayout()
        
        # Sophisticated storage info
        self.storage_label = QLabel("💎 Storage: 0 MB of Pure Knowledge")
        self.storage_label.setObjectName("elegantStorage")
        self.storage_label.setFont(QFont("Segoe UI", 12))
        footer_layout.addWidget(self.storage_label)
        
        # Royal spacer
        footer_layout.addStretch()
        
        # Graceful exit
        self.exit_btn = RippleButton("Farewell")
        self.exit_btn.setObjectName("gracefulExit")
        self.exit_btn.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.exit_btn.setMaximumWidth(120)
        self.exit_btn.clicked.connect(self._on_exit)
        footer_layout.addWidget(self.exit_btn)
        
        parent_layout.addLayout(footer_layout)

    def apply_seductive_styling(self):
        """Apply the art of seductive styling with enterprise integration"""
        try:
            # Get colors from enterprise design system if available
            if self.design_system:
                bg_primary = self.design_system.color('bg_primary')
                bg_secondary = self.design_system.color('bg_secondary')
                surface = self.design_system.color('surface')
                primary = self.design_system.color('primary')
                primary_hover = self.design_system.color('primary_hover')
                text_primary = self.design_system.color('text_primary')
                text_secondary = self.design_system.color('text_secondary')
            else:
                # Fallback to seductive colors
                bg_primary = '#1a1a2e'
                bg_secondary = '#0f172a'
                surface = '#16213e'
                primary = '#4f46e5'
                primary_hover = '#7c3aed'
                text_primary = '#f8fafc'
                text_secondary = '#e2e8f0'

            style = f"""
            /* Main Container - Royal Foundation with Enterprise Integration */
            SeductiveMainMenu {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {bg_primary},
                    stop: 0.5 {surface},
                    stop: 1 {bg_secondary});
                color: {text_primary};
                border: none;
            }}

            /* Royal Title - Majestic Presence with Enterprise Colors */
            #royalTitle {{
                color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 {primary},
                    stop: 0.5 {primary_hover},
                    stop: 1 #ec4899);
                margin: 20px;
                font-weight: 700;
                /* text-shadow removed - not supported in Qt */
            }}

            /* Seductive Subtitle */
            #seductiveSubtitle {{
                color: {text_secondary};
                margin: 10px;
                font-weight: 500;
            }}

            /* Enchanting Welcome */
            #enchantingWelcome {{
                color: {text_secondary};
                margin: 10px;
                opacity: 0.8;
            }}

            /* Primary Seductive Button with Enterprise Colors */
            #primarySeductive {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {primary},
                    stop: 0.5 {primary_hover},
                    stop: 1 #4338ca);
                color: white;
                border: none;
                border-radius: 15px;
                padding: 25px;
                margin: 10px;
                font-weight: 600;
                /* box-shadow and transition removed - not supported in Qt */
            }}

            #primarySeductive:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {primary_hover},
                    stop: 0.5 {primary},
                    stop: 1 #6366f1);
                /* transform and box-shadow removed - not supported in Qt */
            }}

            /* Secondary Seductive Buttons */
            #secondarySeductive {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34d399,
                    stop: 0.5 #10b981,
                    stop: 1 #059669);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
                font-weight: 600;
            }}

            #secondarySeductive:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6ee7b7,
                    stop: 0.5 #34d399,
                    stop: 1 #10b981);
                /* transform removed - not supported in Qt */
            }}

            /* Tertiary Seductive Buttons */
            #tertiarySeductive {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #60a5fa,
                    stop: 0.5 #3b82f6,
                    stop: 1 #2563eb);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
                font-weight: 600;
            }}

            #tertiarySeductive:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #93c5fd,
                    stop: 0.5 #60a5fa,
                    stop: 1 #3b82f6);
                /* transform removed - not supported in Qt */
            }}

            /* Elegant Storage */
            #elegantStorage {{
                color: #64748b;
            }}

            /* Graceful Exit */
            #gracefulExit {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f87171,
                    stop: 0.5 #ef4444,
                    stop: 1 #dc2626);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
            }}

            #gracefulExit:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #fca5a5,
                    stop: 0.5 #f87171,
                    stop: 1 #ef4444);
            }}
            """

            self.setStyleSheet(style)
            logger.info("✅ Seductive styling applied with enterprise integration")

        except Exception as e:
            logger.error(f"❌ Error applying seductive styling: {e}")
            # Apply fallback styling
            self._apply_fallback_seductive_styling()

    def _apply_fallback_seductive_styling(self):
        """Apply fallback seductive styling when enterprise integration fails"""
        try:
            fallback_style = """
                SeductiveMainMenu {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #1a1a2e,
                        stop: 0.5 #16213e,
                        stop: 1 #0f172a);
                    color: #f8fafc;
                }
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #4f46e5,
                        stop: 1 #7c3aed);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 20px;
                    margin: 10px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #7c3aed,
                        stop: 1 #4f46e5);
                    /* transform removed - not supported in Qt */
                }
            """
            self.setStyleSheet(fallback_style)
            logger.info("✅ Fallback seductive styling applied")

        except Exception as e:
            logger.error(f"❌ Fallback seductive styling failed: {e}")

    def refresh_styling(self):
        """Refresh styling when theme changes"""
        try:
            self.apply_seductive_styling()
            logger.debug("✅ SeductiveMainMenu styling refreshed")
        except Exception as e:
            logger.warning(f"⚠️ Error refreshing SeductiveMainMenu styling: {e}")

    def animate_entrance(self):
        """Animate the royal entrance of elements"""
        # Start with elements hidden
        widgets = [self.title, self.subtitle, self.welcome,
                  self.start_btn, self.train_btn, self.settings_btn]

        for widget in widgets:
            effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(effect)
            effect.setOpacity(0)

        # Animate entrance with staggered timing
        delay = 0
        for widget in widgets:
            QTimer.singleShot(delay, lambda w=widget: self.animate_widget_entrance(w))
            delay += 200  # 200ms stagger

    def animate_widget_entrance(self, widget):
        """Animate individual widget entrance"""
        effect = widget.graphicsEffect()

        # Fade in animation
        fade_animation = QPropertyAnimation(effect, b"opacity")
        fade_animation.setDuration(600)
        fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        fade_animation.setStartValue(0.0)
        fade_animation.setEndValue(1.0)

        fade_animation.start()
        self.entrance_animations.append(fade_animation)

    def _on_start_quiz(self):
        """Handle start quiz with elegance"""
        logger.info("🎯 Quest begins...")
        self.start_quiz_requested.emit()
        if self.parent and hasattr(self.parent, 'show_quiz_setup'):
            self.parent.show_quiz_setup()

    def _on_train_model(self):
        """Handle train AI model with sophistication"""
        logger.info("🧠 Training AI model...")
        self.train_model_requested.emit()

        # Try multiple ways to open the training dialog
        try:
            if self.parent and hasattr(self.parent, 'show_training_dialog'):
                logger.info("Opening training dialog via parent.show_training_dialog()")
                self.parent.show_training_dialog()
            elif hasattr(self.parent, 'training_integration') and self.parent.training_integration:
                logger.info("Opening training dialog via training integration")
                self.parent.training_integration.show_training_dialog()
            else:
                logger.warning("No training dialog method found on parent")
                # Try to import and create the dialog directly
                try:
                    from ..training_dialog import AITrainingDialog
                    dialog = AITrainingDialog(self.parent or self)
                    dialog.show()
                    logger.info("Created training dialog directly")
                except ImportError as e:
                    logger.error(f"Could not import training dialog: {e}")
                    # Show a message to the user
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.information(
                        self,
                        "Training",
                        "Training functionality is being prepared. Please check back soon!"
                    )
        except Exception as e:
            logger.error(f"Error opening training dialog: {e}")
            # Show error to user
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open training dialog: {e}"
            )

    def _on_settings(self):
        """Handle settings with refinement"""
        logger.info("⚙️ Refining experience...")
        self.settings_requested.emit()
        if self.parent and hasattr(self.parent, 'show_settings'):
            self.parent.show_settings()

    def _on_exit(self):
        """Handle exit with grace"""
        logger.info("👋 Farewell...")
        self.exit_requested.emit()
        if self.parent and hasattr(self.parent, 'close'):
            self.parent.close()
