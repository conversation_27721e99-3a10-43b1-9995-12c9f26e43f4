from PyQt5.QtWidgets import (
    Q<PERSON>ialog, QVBoxLayout, Q<PERSON>abel, QListWidget, QPushButton,
    QHBoxLayout, QMessageBox, QListWidgetItem, QWidget
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont
import os
import shutil
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class UploadedBooksDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setWindowTitle("Uploaded Books")
        self.setMinimumSize(600, 400)
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
            }
            QListWidget {
                background-color: #34495e;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 8px;
                margin: 2px;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #3498db;
            }
            QListWidget::item:selected {
                background-color: #2980b9;
            }
            QPushButton {
                background-color: #8075FF;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #9085FF;
            }
            QPushButton:disabled {
                background-color: #7f8c8d;
            }
            QPushButton#deleteBtn {
                background-color: #e74c3c;
            }
            QPushButton#deleteBtn:hover {
                background-color: #c0392b;
            }
            QPushButton#refreshBtn {
                background-color: #27ae60;
            }
            QPushButton#refreshBtn:hover {
                background-color: #2ecc71;
            }
        """)

        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # Header with refresh button
        header_layout = QHBoxLayout()
        self.title_label = QLabel("Your Uploaded Books")
        header_layout.addWidget(self.title_label)
        
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.setObjectName("refreshBtn")
        self.refresh_button.clicked.connect(self.refresh_books)
        header_layout.addWidget(self.refresh_button)
        self.layout.addLayout(header_layout)

        # Books count label
        self.count_label = QLabel("0 books")
        self.count_label.setStyleSheet("font-size: 12px; color: #bdc3c7;")
        self.layout.addWidget(self.count_label)

        # Books list
        self.books_list = QListWidget()
        self.books_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.books_list.itemSelectionChanged.connect(self._update_button_states)
        self.layout.addWidget(self.books_list)

        # Buttons layout
        buttons_layout = QHBoxLayout()
        
        # Open button
        self.open_button = QPushButton("Open")
        self.open_button.setEnabled(False)
        self.open_button.clicked.connect(self._open_selected)
        buttons_layout.addWidget(self.open_button)

        # Delete button
        self.delete_button = QPushButton("Delete")
        self.delete_button.setObjectName("deleteBtn")
        self.delete_button.setEnabled(False)
        self.delete_button.clicked.connect(self._delete_selected)
        buttons_layout.addWidget(self.delete_button)

        # Close button
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.close)
        buttons_layout.addWidget(self.close_button)

        self.layout.addLayout(buttons_layout)

    def get_books_dir(self):
        """Get the books directory path"""
        try:
            # Try to get path from config first
            if (self.parent and hasattr(self.parent, 'parent') and 
                hasattr(self.parent.parent, 'config') and 
                hasattr(self.parent.parent.config, '_config')):
                
                books_dir = Path(self.parent.parent.config._config.get('paths', {}).get('uploaded_books', ''))
                if books_dir and books_dir.is_absolute():
                    books_dir.mkdir(parents=True, exist_ok=True)
                    logger.info(f"Using configured books directory: {books_dir}")
                    return str(books_dir)
            
            # Fallback to base_path/data/uploaded_books
            if self.parent and hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'base_path'):
                base_path = Path(self.parent.parent.base_path)
            else:
                base_path = Path.cwd()
            
            books_dir = base_path / 'data' / 'uploaded_books'
            books_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Using fallback books directory: {books_dir}")
            return str(books_dir)
            
        except Exception as e:
            logger.error(f"Error getting books directory: {e}")
            fallback_dir = Path.cwd() / 'data' / 'uploaded_books'
            fallback_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Using emergency fallback books directory: {fallback_dir}")
            return str(fallback_dir)

    def refresh_books(self):
        """Refresh the books list"""
        try:
            books_dir = self.get_books_dir()
            books = [f for f in os.listdir(books_dir) if f.lower().endswith(('.txt', '.pdf', '.doc', '.docx'))]
            self.set_books(books)
        except Exception as e:
            logger.error(f"Error refreshing books: {e}")
            QMessageBox.critical(self, "Error", f"Failed to refresh books list: {str(e)}")

    def set_books(self, books):
        """Set the list of books in the dialog"""
        try:
            self.books_list.clear()
            if not books:
                item = QListWidgetItem("No books uploaded yet")
                item.setFlags(item.flags() & ~Qt.ItemIsEnabled)
                self.books_list.addItem(item)
                self.count_label.setText("0 books")
            else:
                # Sort books alphabetically
                books.sort(key=str.lower)
                for book in books:
                    self.books_list.addItem(book)
                self.count_label.setText(f"{len(books)} book{'s' if len(books) != 1 else ''}")
            self._update_button_states()
        except Exception as e:
            logger.error(f"Error setting books: {e}")
            QMessageBox.critical(self, "Error", f"Failed to display books: {str(e)}")

    def _update_button_states(self):
        """Update button states based on selection"""
        has_selection = len(self.books_list.selectedItems()) > 0
        self.open_button.setEnabled(has_selection and len(self.books_list.selectedItems()) == 1)
        self.delete_button.setEnabled(has_selection)

    def _open_selected(self):
        """Open the selected book"""
        try:
            selected = self.books_list.selectedItems()[0].text()
            book_path = os.path.join(self.get_books_dir(), selected)
            
            if not os.path.exists(book_path):
                QMessageBox.warning(self, "File Not Found", "The selected book file no longer exists.")
                self.refresh_books()
                return
                
            import platform
            if platform.system() == 'Windows':
                os.startfile(book_path)
            elif platform.system() == 'Darwin':  # macOS
                import subprocess
                subprocess.run(['open', book_path])
            else:  # Linux
                import subprocess
                subprocess.run(['xdg-open', book_path])
                
        except Exception as e:
            logger.error(f"Error opening book: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open book: {str(e)}")

    def _delete_selected(self):
        """Delete selected books"""
        selected_items = self.books_list.selectedItems()
        if not selected_items:
            return
            
        confirm_msg = "Are you sure you want to delete the selected book(s)?" if len(selected_items) == 1 \
            else f"Are you sure you want to delete {len(selected_items)} books?"
            
        confirm = QMessageBox.question(
            self,
            "Confirm Delete",
            confirm_msg,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            try:
                books_dir = self.get_books_dir()
                deleted_count = 0
                
                for item in selected_items:
                    book_path = os.path.join(books_dir, item.text())
                    if os.path.exists(book_path):
                        os.remove(book_path)
                        deleted_count += 1
                        
                # Refresh the list
                self.refresh_books()
                
                QMessageBox.information(
                    self,
                    "Success",
                    f"{deleted_count} book{'s' if deleted_count != 1 else ''} deleted successfully."
                )
                
            except Exception as e:
                logger.error(f"Error deleting books: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete books: {str(e)}")

    def showEvent(self, event):
        """Refresh books list when dialog is shown"""
        super().showEvent(event)
        self.refresh_books() 