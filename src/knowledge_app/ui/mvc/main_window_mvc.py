"""
Enterprise Main Window MVC Implementation

This module refactors the massive 1671-line MainWindow God Object into a clean,
professional MVC architecture with proper separation of concerns.

Components:
- MainWindowModel: Application state and data management
- MainWindowView: Professional UI layout and display
- MainWindowController: Navigation and user interaction handling
"""

from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QStackedWidget, QStatusBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QCloseEvent

from .base_mvc import BaseModel, BaseView, BaseController
from ..enterprise_design_system import get_design_system, Theme
from ..enterprise_style_manager import get_style_manager, set_app_theme
import logging

logger = logging.getLogger(__name__)

class MainWindowModel(BaseModel):
    """
    Main window data model managing application state and navigation.
    
    Responsibilities:
    - Track current screen/view
    - Manage application settings
    - Handle navigation state
    - Store user preferences
    - Manage application lifecycle
    """
    
    # Additional signals for main window
    navigation_changed = pyqtSignal(str, str)  # (from_screen, to_screen)
    theme_changed = pyqtSignal(str)            # theme_name
    settings_updated = pyqtSignal(dict)        # settings_dict
    
    def __init__(self):
        super().__init__()
        
        # Initialize application state
        self._initialize_app_state()
        
        # Set up validators
        self._setup_validators()
    
    def _initialize_app_state(self) -> None:
        """Initialize the application state data"""
        self.set_data('current_screen', 'main_menu', validate=False)
        self.set_data('previous_screen', None, validate=False)
        self.set_data('theme', 'dark', validate=False)
        self.set_data('font_size', 16, validate=False)
        self.set_data('window_title', 'Knowledge App - Enterprise Edition', validate=False)
        self.set_data('status_message', 'Ready', validate=False)
        self.set_data('is_fullscreen', False, validate=False)
        self.set_data('window_geometry', {
            'width': 1200,
            'height': 800,
            'x': 100,
            'y': 100
        }, validate=False)
        
        # Application settings
        self.set_data('settings', {
            'auto_save': True,
            'notifications': True,
            'sound_enabled': True,
            'language': 'en',
            'update_interval': 5000
        }, validate=False)
    
    def _setup_validators(self) -> None:
        """Set up data validators"""
        # Screen validation - updated to match actual screen names
        valid_screens = ['main_menu', 'quiz_setup', 'quiz_screen', 'settings', 'training']
        self.add_validator('current_screen', lambda x: x in valid_screens)
        
        # Theme validation
        valid_themes = ['dark', 'light']
        self.add_validator('theme', lambda x: x in valid_themes)
        
        # Font size validation
        self.add_validator('font_size', lambda x: isinstance(x, int) and 8 <= x <= 32)
        
        # Window geometry validation
        def validate_geometry(geo):
            if not isinstance(geo, dict):
                return False
            required_keys = ['width', 'height', 'x', 'y']
            return all(key in geo and isinstance(geo[key], int) for key in required_keys)
        
        self.add_validator('window_geometry', validate_geometry)
    
    def navigate_to_screen(self, screen_name: str) -> bool:
        """Navigate to a specific screen"""
        try:
            current_screen = self.get_data('current_screen')
            
            if current_screen == screen_name:
                logger.debug(f"Already on screen: {screen_name}")
                return True
            
            # Validate screen name
            if not self.set_data('current_screen', screen_name):
                logger.error(f"Invalid screen name: {screen_name}")
                return False
            
            # Update previous screen
            self.set_data('previous_screen', current_screen)
            
            # Emit navigation signal
            self.navigation_changed.emit(current_screen or 'unknown', screen_name)
            
            logger.info(f"Navigation: {current_screen} -> {screen_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error navigating to screen {screen_name}: {e}")
            return False
    
    def go_back(self) -> bool:
        """Navigate back to the previous screen"""
        previous_screen = self.get_data('previous_screen')
        if previous_screen:
            return self.navigate_to_screen(previous_screen)
        else:
            # Default back to main menu
            return self.navigate_to_screen('main_menu')
    
    def set_theme(self, theme_name: str) -> bool:
        """Set the application theme"""
        try:
            if self.set_data('theme', theme_name):
                self.theme_changed.emit(theme_name)
                logger.info(f"Theme changed to: {theme_name}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error setting theme {theme_name}: {e}")
            return False
    
    def update_settings(self, settings_dict: Dict[str, Any]) -> bool:
        """Update application settings"""
        try:
            current_settings = self.get_data('settings', {})
            updated_settings = {**current_settings, **settings_dict}
            
            if self.set_data('settings', updated_settings):
                self.settings_updated.emit(settings_dict)
                logger.info(f"Settings updated: {list(settings_dict.keys())}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error updating settings: {e}")
            return False
    
    def set_status_message(self, message: str) -> None:
        """Set the status bar message"""
        self.set_data('status_message', message)
    
    def set_window_geometry(self, width: int, height: int, x: int = None, y: int = None) -> bool:
        """Set window geometry"""
        try:
            current_geo = self.get_data('window_geometry', {})
            new_geo = {
                'width': width,
                'height': height,
                'x': x if x is not None else current_geo.get('x', 100),
                'y': y if y is not None else current_geo.get('y', 100)
            }
            
            return self.set_data('window_geometry', new_geo)
            
        except Exception as e:
            logger.error(f"Error setting window geometry: {e}")
            return False

class MainWindowView(QWidget):
    """
    Professional main window view with enterprise-grade UI design.

    Responsibilities:
    - Create main window layout
    - Manage screen stack
    - Display status information
    - Handle window events
    - Apply professional styling

    Note: Changed from QMainWindow to QWidget to avoid nested main window issues
    when used as central widget in EnterpriseMainWindow.
    """

    # Signals for communicating with controllers
    user_action = pyqtSignal(str, dict)  # (action_name, action_data)
    view_ready = pyqtSignal()
    view_closing = pyqtSignal()

    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)

        # MVC connections
        self._model: Optional[MainWindowModel] = None
        self._controller: Optional['MainWindowController'] = None
        self._is_initialized = False
        
        # UI components
        self.central_widget: Optional[QWidget] = None
        self.screen_stack: Optional[QStackedWidget] = None
        self.status_bar: Optional[QStatusBar] = None
        
        # Screen references (will be injected)
        self.screens: Dict[str, QWidget] = {}
        
        # Design system
        self.ds = get_design_system()
        self.style_manager = get_style_manager()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_status_display)

    def set_model(self, model: MainWindowModel) -> None:
        """Connect this view to a model"""
        if self._model:
            # Disconnect from old model
            try:
                self._model.data_changed.disconnect(self.on_model_data_changed)
                self._model.error_occurred.disconnect(self.on_model_error)
            except Exception as e:
                logger.warning(f"Error disconnecting from old model: {e}")

        self._model = model

        # Connect to new model
        if model:
            model.data_changed.connect(self.on_model_data_changed)
            model.error_occurred.connect(self.on_model_error)
            logger.info(f"✅ View connected to model: {type(model).__name__}")

            # DEFER screen sync until after view is fully initialized
            # This prevents trying to navigate before screens are added
            if self._is_initialized:
                current_screen = model.get_data('current_screen')
                if current_screen:
                    logger.info(f"🔄 Syncing view with current screen: {current_screen}")
                    self.on_model_data_changed('current_screen', current_screen)
            else:
                logger.debug("🔄 Deferring screen sync until view is initialized")

    def set_controller(self, controller: 'MainWindowController') -> None:
        """Connect this view to a controller"""
        self._controller = controller
        if controller:
            self.user_action.connect(controller.handle_user_action)
            logger.debug(f"View connected to controller: {type(controller).__name__}")

    def initialize(self) -> None:
        """Initialize the view (called after model and controller are set)"""
        if not self._is_initialized:
            logger.info("🔄 Initializing MainWindowView...")

            try:
                # Set up the UI first - this creates the screen stack
                logger.info("🔄 Setting up UI...")
                self.setup_ui()
                logger.info("✅ UI setup completed")

                # Verify screen stack was created - with fallback recovery
                logger.info("🔄 Verifying screen stack...")
                logger.debug(f"🔍 At start of verification - screen_stack: {getattr(self, 'screen_stack', 'NOT_SET')}")
                logger.debug(f"🔍 Object id: {id(self)}")

                # CRITICAL FIX: Check if screen stack exists and is valid with explicit None checks
                screen_stack_exists = hasattr(self, 'screen_stack')
                screen_stack_not_none = screen_stack_exists and self.screen_stack is not None
                screen_stack_functional = screen_stack_not_none and hasattr(self.screen_stack, 'addWidget')

                logger.debug(f"🔍 Screen stack validation:")
                logger.debug(f"  - exists: {screen_stack_exists}")
                logger.debug(f"  - not None: {screen_stack_not_none}")
                logger.debug(f"  - functional: {screen_stack_functional}")

                if not screen_stack_functional:
                    logger.warning("⚠️ Screen stack is invalid, attempting recovery...")
                    logger.warning(f"⚠️ Screen stack state: exists={screen_stack_exists}, not_none={screen_stack_not_none}, functional={screen_stack_functional}")

                    # Try to recover by creating a basic screen stack
                    try:
                        from PyQt5.QtWidgets import QStackedWidget
                        self.screen_stack = QStackedWidget()
                        logger.info("🔄 Created new QStackedWidget for recovery")

                        # Add to layout if we have one
                        if hasattr(self, 'layout') and self.layout() is not None:
                            self.layout().addWidget(self.screen_stack)
                            logger.info("✅ Added screen stack to existing layout")
                        else:
                            logger.warning("⚠️ No layout available, screen stack created but not added to layout")

                        # Verify recovery was successful
                        recovery_successful = (
                            hasattr(self, 'screen_stack') and
                            self.screen_stack is not None and
                            hasattr(self.screen_stack, 'addWidget')
                        )

                        if recovery_successful:
                            logger.info("✅ Screen stack recovery successful")
                        else:
                            logger.error("❌ Screen stack recovery verification failed")
                            raise RuntimeError("Screen stack recovery verification failed")

                    except Exception as recovery_error:
                        logger.error(f"❌ Failed to recover screen stack: {recovery_error}")
                        logger.error(f"❌ Recovery error type: {type(recovery_error).__name__}")
                        import traceback
                        logger.error(f"❌ Recovery traceback: {traceback.format_exc()}")
                        raise RuntimeError("Screen stack initialization failed and recovery failed") from recovery_error

                logger.info(f"✅ Screen stack verified: {type(self.screen_stack).__name__}")

                # Check screen stack before connect_signals
                logger.debug(f"🔍 Before connect_signals - screen_stack: {getattr(self, 'screen_stack', 'NOT_SET')}")

                logger.info("🔄 Connecting signals...")
                self.connect_signals()
                logger.info("✅ Signals connected")

                # Check screen stack after connect_signals
                logger.debug(f"🔍 After connect_signals - screen_stack: {getattr(self, 'screen_stack', 'NOT_SET')}")

                logger.info("🔄 Applying styling...")
                self.apply_styling()
                logger.info("✅ Styling applied")

                # Check screen stack after apply_styling
                logger.debug(f"🔍 After apply_styling - screen_stack: {getattr(self, 'screen_stack', 'NOT_SET')}")

                self._is_initialized = True
                logger.info("✅ View marked as initialized")

                # CRITICAL FIX: Defer screen synchronization until screens are actually registered
                # This prevents trying to navigate before screens are added by EnterpriseMainWindow
                if self._model:
                    current_screen = self._model.get_data('current_screen')
                    if current_screen:
                        logger.info(f"🔄 Deferring screen sync until screens are registered: {current_screen}")
                        # Use a timer to defer the sync slightly to allow screen registration
                        QTimer.singleShot(100, lambda: self._deferred_screen_sync(current_screen))

                self.view_ready.emit()
                logger.info(f"✅ View initialized successfully: {type(self).__name__}")

            except Exception as e:
                logger.error(f"❌ Failed to initialize view: {e}")
                import traceback
                logger.error(f"❌ Initialization traceback: {traceback.format_exc()}")
                raise RuntimeError("Screen stack initialization failed") from e

    def _deferred_screen_sync(self, screen_name: str) -> None:
        """Perform deferred screen synchronization after screens are registered"""
        try:
            logger.info(f"🔄 Attempting deferred screen sync to: {screen_name}")

            # Check if screens are now available
            if screen_name in self.screens:
                logger.info(f"✅ Screen found, syncing to: {screen_name}")
                self.on_model_data_changed('current_screen', screen_name)
            else:
                available_screens = list(self.screens.keys())
                logger.warning(f"⚠️ Screen still not found: {screen_name}. Available: {available_screens}")

                # If main_menu is available and we're not already trying to show it, show it as fallback
                if 'main_menu' in self.screens and screen_name != 'main_menu':
                    logger.info("🔄 Falling back to main_menu screen")
                    self.on_model_data_changed('current_screen', 'main_menu')
                elif available_screens:
                    # Show the first available screen as last resort
                    fallback_screen = available_screens[0]
                    logger.info(f"🔄 Falling back to first available screen: {fallback_screen}")
                    self.on_model_data_changed('current_screen', fallback_screen)
                else:
                    logger.error("❌ No screens available for fallback")

        except Exception as e:
            logger.error(f"❌ Error in deferred screen sync: {e}")
            import traceback
            logger.error(f"❌ Deferred sync traceback: {traceback.format_exc()}")

    def connect_signals(self) -> None:
        """Connect internal signals (can be overridden by subclasses)"""
        pass

    def on_model_error(self, error_type: str, error_message: str) -> None:
        """Handle model errors (can be overridden by subclasses)"""
        logger.error(f"View received model error: {error_type} - {error_message}")

    def emit_user_action(self, action_name: str, action_data: Dict[str, Any] = None) -> None:
        """Emit a user action to the controller"""
        data = action_data or {}
        self.user_action.emit(action_name, data)
        logger.debug(f"View emitted action: {action_name}")
    
    def setup_ui(self) -> None:
        """Set up the professional main window UI"""
        try:
            logger.info("🔄 Starting UI setup...")
            logger.debug(f"🔍 Object id in setup_ui: {id(self)}")

            # Set minimum size for the widget
            self.setMinimumSize(1000, 700)
            logger.debug("✅ Minimum size set")

            # Create main layout for this widget
            layout = QVBoxLayout(self)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(0)
            logger.debug("✅ Main layout created")

            # Create professional screen stack with enhanced transitions
            try:
                logger.debug("🔄 Attempting to import ProfessionalStackedWidget...")
                from ..seductive_transitions import ProfessionalStackedWidget, TransitionType
                logger.debug("✅ Imported ProfessionalStackedWidget")

                logger.debug("🔄 Creating ProfessionalStackedWidget instance...")
                self.screen_stack = ProfessionalStackedWidget()
                logger.debug(f"✅ ProfessionalStackedWidget created: {self.screen_stack}")
                logger.debug(f"✅ Screen stack type: {type(self.screen_stack)}")
                logger.debug(f"✅ Screen stack is valid: {self.screen_stack is not None}")

                # Configure professional transitions
                logger.debug("🔄 Configuring screen stack transitions...")
                self.screen_stack.set_transition_type(TransitionType.SLIDE_LEFT)
                self.screen_stack.set_transition_duration(350)  # Professional timing
                logger.debug("✅ Screen stack configured")

                logger.debug("🔄 Adding screen stack to layout...")
                layout.addWidget(self.screen_stack)
                logger.debug("✅ Screen stack added to layout")

            except Exception as e:
                logger.error(f"❌ Failed to create screen stack: {e}")
                import traceback
                logger.error(f"❌ Screen stack creation traceback: {traceback.format_exc()}")

                # Create fallback basic stacked widget
                logger.info("🔄 Creating fallback basic stacked widget...")
                from PyQt5.QtWidgets import QStackedWidget
                self.screen_stack = QStackedWidget()
                layout.addWidget(self.screen_stack)
                logger.info(f"✅ Fallback basic stacked widget created: {self.screen_stack}")

            # Verify screen stack was created
            logger.debug("🔄 Final verification of screen stack...")
            logger.debug(f"🔍 hasattr(self, 'screen_stack'): {hasattr(self, 'screen_stack')}")
            if hasattr(self, 'screen_stack'):
                logger.debug(f"🔍 self.screen_stack: {self.screen_stack}")
                logger.debug(f"🔍 self.screen_stack is not None: {self.screen_stack is not None}")
                logger.debug(f"🔍 type(self.screen_stack): {type(self.screen_stack)}")
                logger.debug(f"🔍 bool(self.screen_stack): {bool(self.screen_stack)}")

            # More explicit verification - check for None specifically
            if not hasattr(self, 'screen_stack'):
                logger.error("❌ Screen stack attribute not found")
                raise RuntimeError("Failed to create screen stack - attribute missing")

            if self.screen_stack is None:
                logger.error("❌ Screen stack is None")
                raise RuntimeError("Failed to create screen stack - is None")

            # Additional check to see if the widget is valid
            try:
                # Try to call a basic method to verify the widget is functional
                widget_count = self.screen_stack.count()
                logger.debug(f"✅ Screen stack is functional - widget count: {widget_count}")
            except Exception as e:
                logger.error(f"❌ Screen stack is not functional: {e}")
                raise RuntimeError(f"Screen stack is not functional: {e}")

            logger.info("✅ Screen stack verified and ready")

            # Create status bar (will be added to parent main window)
            try:
                self.status_bar = QStatusBar()
                logger.debug("✅ Status bar created")
            except Exception as e:
                logger.error(f"❌ Failed to create status bar: {e}")

            # Initialize professional toast notification system
            try:
                from ..seductive_transitions import ProfessionalToastNotification
                self.toast_system = ProfessionalToastNotification(self)
                logger.debug("✅ Toast system initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize toast system: {e}")
                self.toast_system = None

            # Initialize professional error manager
            try:
                from ..professional_error_manager import ProfessionalErrorManager
                self.error_manager = ProfessionalErrorManager(self)
                if self.toast_system:
                    self.error_manager.set_toast_system(self.toast_system)
                logger.debug("✅ Error manager initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize error manager: {e}")
                self.error_manager = None

            # Initialize accessibility manager (singleton)
            try:
                from ..accessibility_manager import get_accessibility_manager
                self.accessibility_manager = get_accessibility_manager(self)
                logger.debug("✅ Accessibility manager initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize accessibility manager: {e}")
                self.accessibility_manager = None

            # Register recovery handlers
            try:
                self._register_error_recovery_handlers()
                logger.debug("✅ Recovery handlers registered")
            except Exception as e:
                logger.error(f"❌ Failed to register recovery handlers: {e}")

            # Set up accessibility for main window
            try:
                self._setup_accessibility()
                logger.debug("✅ Accessibility setup completed")
            except Exception as e:
                logger.error(f"❌ Failed to setup accessibility: {e}")

            # Start update timer
            try:
                self.update_timer.start(5000)  # Update every 5 seconds
                logger.debug("✅ Update timer started")
            except Exception as e:
                logger.error(f"❌ Failed to start update timer: {e}")

            logger.info("✅ UI setup completed successfully")

            # Final verification before exiting setup_ui
            if not (hasattr(self, 'screen_stack') and self.screen_stack is not None):
                logger.error("❌ Screen stack lost during setup_ui!")
            else:
                logger.debug("✅ Screen stack verified at end of setup_ui")

        except Exception as e:
            logger.error(f"❌ Critical error in setup_ui: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            raise
    
    def apply_styling(self) -> None:
        """Apply professional, seductive styling to the main window"""
        try:
            logger.info("🎨 Applying professional styling to MainWindowView...")

            # Apply global application styles first
            self.style_manager.apply_global_styles()

            # Create enhanced main window styling with seductive elements
            enhanced_style = f"""
                /* Main Window - Professional Foundation */
                MainWindowView {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {self.ds.color('bg_primary')},
                        stop: 0.5 {self.ds.color('surface')},
                        stop: 1 {self.ds.color('bg_secondary')});
                    color: {self.ds.color('text_primary')};
                    border: none;
                }}

                /* Professional Screen Stack */
                QStackedWidget {{
                    background: transparent;
                    border: none;
                }}

                /* Enhanced Status Bar */
                QStatusBar {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {self.ds.color('surface')},
                        stop: 1 {self.ds.color('bg_secondary')});
                    color: {self.ds.color('text_secondary')};
                    border-top: 1px solid {self.ds.color('border')};
                    padding: {self.ds.spacing('sm')}px {self.ds.spacing('md')}px;
                    font-size: {self.ds.font_size('sm')}px;
                    font-weight: {self.ds._typography['font_weight_medium']};
                }}

                /* Professional Scrollbars */
                QScrollBar:vertical {{
                    background: {self.ds.color('bg_secondary')};
                    width: 12px;
                    border-radius: 6px;
                    margin: 0;
                }}

                QScrollBar::handle:vertical {{
                    background: {self.ds.color('primary')};
                    border-radius: 6px;
                    min-height: 20px;
                }}

                QScrollBar::handle:vertical:hover {{
                    background: {self.ds.color('primary_hover')};
                }}

                QScrollBar:horizontal {{
                    background: {self.ds.color('bg_secondary')};
                    height: 12px;
                    border-radius: 6px;
                    margin: 0;
                }}

                QScrollBar::handle:horizontal {{
                    background: {self.ds.color('primary')};
                    border-radius: 6px;
                    min-width: 20px;
                }}

                QScrollBar::handle:horizontal:hover {{
                    background: {self.ds.color('primary_hover')};
                }}
            """

            self.setStyleSheet(enhanced_style)

            # Apply professional styling to status bar if it exists
            if self.status_bar is not None:
                status_style = f"""
                    QStatusBar {{
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 {self.ds.color('surface')},
                            stop: 1 {self.ds.color('bg_secondary')});
                        color: {self.ds.color('text_secondary')};
                        border-top: 2px solid {self.ds.color('primary')};
                        padding: {self.ds.spacing('sm')}px {self.ds.spacing('md')}px;
                        font-size: {self.ds.font_size('sm')}px;
                        font-weight: {self.ds._typography['font_weight_medium']};
                    }}
                """
                self.status_bar.setStyleSheet(status_style)

            logger.info("✅ Professional styling applied successfully")

        except Exception as e:
            logger.error(f"❌ Error applying professional styling: {e}")
            self._apply_fallback_styling()

    def _apply_fallback_styling(self) -> None:
        """Apply fallback styling when professional styling fails"""
        try:
            fallback_style = f"""
                MainWindowView {{
                    background: {self.ds.color('bg_primary')};
                    color: {self.ds.color('text_primary')};
                }}
                QStatusBar {{
                    background: {self.ds.color('surface')};
                    color: {self.ds.color('text_secondary')};
                    border-top: 1px solid {self.ds.color('border')};
                    padding: 8px;
                }}
            """
            self.setStyleSheet(fallback_style)
            logger.info("✅ Fallback styling applied")

        except Exception as e:
            logger.error(f"❌ Fallback styling failed: {e}")

    def _apply_screen_styling(self, screen_name: str, screen_widget: QWidget) -> None:
        """Apply professional styling to individual screens"""
        try:
            # Apply styling based on screen type
            if hasattr(screen_widget, 'apply_seductive_styling'):
                # SeductiveMainMenu and similar screens
                screen_widget.apply_seductive_styling()
                logger.debug(f"✅ Applied seductive styling to {screen_name}")
            elif hasattr(screen_widget, 'apply_enterprise_styling'):
                # Enterprise MVC screens
                screen_widget.apply_enterprise_styling()
                logger.debug(f"✅ Applied enterprise styling to {screen_name}")
            elif hasattr(screen_widget, 'apply_professional_styling'):
                # Professional screens
                screen_widget.apply_professional_styling()
                logger.debug(f"✅ Applied professional styling to {screen_name}")
            else:
                # Apply basic professional styling
                self._apply_basic_screen_styling(screen_widget)
                logger.debug(f"✅ Applied basic styling to {screen_name}")

        except Exception as e:
            logger.warning(f"⚠️ Error applying styling to screen {screen_name}: {e}")

    def _apply_basic_screen_styling(self, screen_widget: QWidget) -> None:
        """Apply basic professional styling to screens without specific styling methods"""
        try:
            basic_style = f"""
                QWidget {{
                    background: {self.ds.color('bg_primary')};
                    color: {self.ds.color('text_primary')};
                }}
                QPushButton {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {self.ds.color('primary')},
                        stop: 1 {self.ds.color('primary_hover')});
                    color: {self.ds.color('text_on_primary')};
                    border: none;
                    border-radius: {self.ds.radius('md')}px;
                    padding: {self.ds.spacing('sm')}px {self.ds.spacing('md')}px;
                    font-size: {self.ds.font_size('base')}px;
                    font-weight: {self.ds._typography['font_weight_semibold']};
                    min-height: {self.ds._dimensions['button_height_md']}px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {self.ds.color('primary_hover')},
                        stop: 1 {self.ds.color('primary')});
                    transform: translateY(-1px);
                }}
                QPushButton:pressed {{
                    background: {self.ds.color('primary_active')};
                    transform: translateY(0px);
                }}
                QLabel {{
                    color: {self.ds.color('text_primary')};
                    font-size: {self.ds.font_size('base')}px;
                }}
            """
            screen_widget.setStyleSheet(basic_style)

        except Exception as e:
            logger.warning(f"⚠️ Error applying basic screen styling: {e}")

    def _create_default_screens(self) -> None:
        """Create default screens if none exist"""
        try:
            logger.info("🔄 Creating default screens")

            # Import screen classes with error handling and fallbacks
            screen_classes = {}

            # Try to import SeductiveMainMenu first for better visual appeal
            try:
                from ..seductive_main_menu import SeductiveMainMenu
                screen_classes['main_menu'] = SeductiveMainMenu
                logger.debug("✅ Using SeductiveMainMenu for main_menu (preferred for visual appeal)")
            except ImportError:
                try:
                    from ..mvc.main_menu_mvc import MainMenuMVC
                    screen_classes['main_menu'] = MainMenuMVC
                    logger.debug("✅ Using MainMenuMVC for main_menu (fallback)")
                except ImportError as e:
                    logger.error(f"❌ Failed to import main menu class: {e}")

            # Try to import QuizScreenMVC first, fallback to ProfessionalQuizScreen
            try:
                from ..mvc.quiz_screen_mvc import QuizScreenMVC
                screen_classes['quiz_screen'] = QuizScreenMVC
                logger.debug("✅ Using QuizScreenMVC for quiz_screen")
            except ImportError:
                try:
                    from ..professional_quiz_screen import ProfessionalQuizScreen
                    screen_classes['quiz_screen'] = ProfessionalQuizScreen
                    logger.debug("✅ Using ProfessionalQuizScreen for quiz_screen (fallback)")
                except ImportError as e:
                    logger.error(f"❌ Failed to import quiz screen class: {e}")

            # Import settings screen
            try:
                from ..professional_settings_screen import ProfessionalSettingsScreen
                screen_classes['settings'] = ProfessionalSettingsScreen
                logger.debug("✅ Using ProfessionalSettingsScreen for settings")
            except ImportError as e:
                logger.error(f"❌ Failed to import settings screen class: {e}")

            # Create screens from available classes
            for screen_name, screen_class in screen_classes.items():
                try:
                    screen_widget = screen_class(self)
                    self.add_screen(screen_name, screen_widget)
                    logger.debug(f"✅ Created default screen: {screen_name}")
                except Exception as e:
                    logger.error(f"❌ Failed to create default screen {screen_name}: {e}")

            # Log final screen registry
            logger.info(f"📋 Default screens created: {list(screen_classes.keys())}")

        except Exception as e:
            logger.error(f"❌ Failed to create default screens: {e}")

    def add_screen(self, screen_name: str, screen_widget: QWidget) -> None:
        """Add a screen to the stack with comprehensive validation"""
        try:
            # CRITICAL FIX: Use explicit None checks for screen stack validation
            screen_stack_exists = hasattr(self, 'screen_stack')
            screen_stack_not_none = screen_stack_exists and self.screen_stack is not None
            screen_stack_functional = screen_stack_not_none and hasattr(self.screen_stack, 'addWidget')

            if not screen_stack_functional:
                logger.error(f"❌ Cannot add screen {screen_name}: screen stack not initialized")
                return

            if screen_widget is None:
                logger.error(f"❌ Cannot add screen {screen_name}: screen widget is None")
                return

            if screen_name not in self.screens:
                self.screens[screen_name] = screen_widget
                self.screen_stack.addWidget(screen_widget)

                # Apply professional styling to the screen
                self._apply_screen_styling(screen_name, screen_widget)

                logger.debug(f"✅ Added screen: {screen_name}")
            else:
                logger.warning(f"⚠️ Screen already exists: {screen_name}")

        except Exception as e:
            logger.error(f"❌ Error adding screen {screen_name}: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
    
    def show_screen(self, screen_name: str) -> bool:
        """Show a specific screen with comprehensive error handling"""
        try:
            # CRITICAL FIX: Use explicit None checks for screen stack validation
            screen_stack_exists = hasattr(self, 'screen_stack')
            screen_stack_not_none = screen_stack_exists and self.screen_stack is not None
            screen_stack_functional = screen_stack_not_none and hasattr(self.screen_stack, 'setCurrentWidget')

            if not screen_stack_functional:
                logger.error("❌ Screen stack not initialized - attempting to initialize")
                self.setup_ui()  # Try to initialize UI

                # Re-validate after setup attempt
                screen_stack_exists = hasattr(self, 'screen_stack')
                screen_stack_not_none = screen_stack_exists and self.screen_stack is not None
                screen_stack_functional = screen_stack_not_none and hasattr(self.screen_stack, 'setCurrentWidget')

                if not screen_stack_functional:
                    logger.error("❌ Failed to initialize screen stack")
                    return False

            # Check if screen exists
            if screen_name not in self.screens:
                available_screens = list(self.screens.keys())
                logger.error(f"❌ Screen not found: {screen_name}. Available: {available_screens}")

                # Try to create default screens if none exist
                if not self.screens:
                    logger.info("🔄 No screens registered, attempting to create default screens")
                    self._create_default_screens()

                if screen_name not in self.screens:
                    return False

            # Get screen widget and show it
            screen_widget = self.screens[screen_name]
            if screen_widget is None:
                logger.error(f"❌ Screen widget is None for: {screen_name}")
                return False

            self.screen_stack.setCurrentWidget(screen_widget)
            logger.info(f"✅ Successfully showing screen: {screen_name}")

            # Update status bar
            if self.status_bar is not None:
                self.status_bar.showMessage(f"Current screen: {screen_name}")

            return True

        except Exception as e:
            logger.error(f"❌ Error showing screen {screen_name}: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False

    def show_screen_with_transition(self, screen_name: str, transition_type=None) -> bool:
        """
        Show a specific screen with professional transition.

        Args:
            screen_name: Name of the screen to show
            transition_type: Optional specific transition type
        """
        try:
            if not self.screen_stack:
                logger.error("❌ Screen stack not initialized")
                return False

            if screen_name in self.screens:
                screen_widget = self.screens[screen_name]

                # Use professional transition if specified
                if transition_type:
                    self.screen_stack.setCurrentWidget(screen_widget, transition_type)
                else:
                    self.screen_stack.setCurrentWidget(screen_widget)

                logger.info(f"✅ Successfully showing screen: {screen_name} with transition")

                # Update status bar
                if self.status_bar:
                    self.status_bar.showMessage(f"Current screen: {screen_name}")

                return True
            else:
                available_screens = list(self.screens.keys())
                logger.error(f"❌ Screen not found: {screen_name}. Available: {available_screens}")
                return False

        except Exception as e:
            logger.error(f"❌ Error showing screen {screen_name}: {e}")
            return False

    def show_success_toast(self, message: str, duration: int = 3000):
        """Show success toast notification"""
        if hasattr(self, 'toast_system'):
            self.toast_system.show_success(message, duration)

    def show_error_toast(self, message: str, duration: int = 5000):
        """Show error toast notification"""
        if hasattr(self, 'toast_system'):
            self.toast_system.show_error(message, duration)

    def show_info_toast(self, message: str, duration: int = 3000):
        """Show info toast notification"""
        if hasattr(self, 'toast_system'):
            self.toast_system.show_info(message, duration)

    def show_warning_toast(self, message: str, duration: int = 4000):
        """Show warning toast notification"""
        if hasattr(self, 'toast_system'):
            self.toast_system.show_warning(message, duration)

    def handle_error(self, error: Exception, context: str = "", severity: str = "error"):
        """Handle error with professional error manager"""
        if hasattr(self, 'error_manager'):
            self.error_manager.handle_error(error, context, severity)
        else:
            # Fallback to logging
            logger.error(f"Error in {context}: {error}")

    def handle_network_error(self, error: Exception, context: str = ""):
        """Handle network error specifically"""
        if hasattr(self, 'error_manager'):
            self.error_manager.handle_network_error(error, context)
        else:
            logger.error(f"Network error in {context}: {error}")

    def handle_model_error(self, error: Exception, model_name: str = ""):
        """Handle AI model error specifically"""
        if hasattr(self, 'error_manager'):
            self.error_manager.handle_model_error(error, model_name)
        else:
            logger.error(f"Model error with {model_name}: {error}")

    def _register_error_recovery_handlers(self):
        """Register error recovery action handlers"""
        if not hasattr(self, 'error_manager'):
            return

        # Register common recovery actions
        self.error_manager.register_recovery_handler('restart_app', self._handle_restart_app)
        self.error_manager.register_recovery_handler('enable_offline_mode', self._handle_enable_offline_mode)
        self.error_manager.register_recovery_handler('retry_operation', self._handle_retry_operation)
        self.error_manager.register_recovery_handler('select_file', self._handle_select_file)

    def _handle_restart_app(self, data: dict) -> bool:
        """Handle restart application action"""
        try:
            # Show confirmation and restart
            self.show_info_toast("Application will restart to resolve the issue...")
            # In a real implementation, you'd trigger app restart
            logger.info("Application restart requested")
            return True
        except Exception as e:
            logger.error(f"Error handling restart: {e}")
            return False

    def _handle_enable_offline_mode(self, data: dict) -> bool:
        """Handle enable offline mode action"""
        try:
            # Enable offline mode
            self.show_info_toast("Offline mode enabled")
            logger.info("Offline mode enabled via error recovery")
            return True
        except Exception as e:
            logger.error(f"Error enabling offline mode: {e}")
            return False

    def _handle_retry_operation(self, data: dict) -> bool:
        """Handle retry operation action"""
        try:
            context = data.get('context', 'operation')
            self.show_info_toast(f"Retrying {context}...")
            logger.info(f"Retrying operation: {context}")
            return True
        except Exception as e:
            logger.error(f"Error retrying operation: {e}")
            return False

    def _handle_select_file(self, data: dict) -> bool:
        """Handle select file action"""
        try:
            # In a real implementation, you'd open file dialog
            self.show_info_toast("Please select a different file...")
            logger.info("File selection requested via error recovery")
            return True
        except Exception as e:
            logger.error(f"Error handling file selection: {e}")
            return False

    def _setup_accessibility(self):
        """Set up comprehensive accessibility for the main window"""
        try:
            # Set up main window accessibility
            self.accessibility_manager.setup_widget_accessibility(
                self,
                accessible_name="Knowledge App Main Window",
                accessible_description="Main application window for AI-powered learning"
            )

            # Connect accessibility signals
            self.accessibility_manager.accessibility_changed.connect(self._on_accessibility_changed)
            self.accessibility_manager.theme_changed.connect(self._on_accessibility_theme_changed)

            # Set up keyboard shortcuts
            self._setup_keyboard_shortcuts()

            logger.info("♿ Accessibility features initialized")

        except Exception as e:
            logger.error(f"Error setting up accessibility: {e}")

    def _setup_keyboard_shortcuts(self):
        """Set up application-wide keyboard shortcuts"""
        try:
            # Navigation shortcuts
            from PyQt5.QtWidgets import QShortcut
            from PyQt5.QtGui import QKeySequence

            # Main navigation shortcuts
            QShortcut(QKeySequence("Ctrl+1"), self, lambda: self.show_screen("main_menu"))
            QShortcut(QKeySequence("Ctrl+2"), self, lambda: self.show_screen("quiz_setup"))
            QShortcut(QKeySequence("Ctrl+3"), self, lambda: self.show_screen("settings"))
            QShortcut(QKeySequence("Ctrl+4"), self, lambda: self.show_screen("training"))

            # Accessibility shortcuts
            QShortcut(QKeySequence("Ctrl+Alt+H"), self, self._toggle_high_contrast)
            QShortcut(QKeySequence("Ctrl+Alt+F"), self, self._toggle_focus_indicators)
            QShortcut(QKeySequence("Ctrl+Alt+T"), self, self._toggle_large_text)

            # Help shortcut
            QShortcut(QKeySequence("F1"), self, self._show_accessibility_help)

            logger.info("⌨️ Keyboard shortcuts configured")

        except Exception as e:
            logger.error(f"Error setting up keyboard shortcuts: {e}")

    def _toggle_high_contrast(self):
        """Toggle high-contrast theme"""
        if hasattr(self, 'accessibility_manager'):
            from ..accessibility_manager import AccessibilityFeature
            self.accessibility_manager.toggle_feature(AccessibilityFeature.HIGH_CONTRAST)

            # Show feedback
            enabled = self.accessibility_manager.is_feature_enabled(AccessibilityFeature.HIGH_CONTRAST)
            message = "High-contrast theme enabled" if enabled else "High-contrast theme disabled"
            self.show_info_toast(message)

    def _toggle_focus_indicators(self):
        """Toggle enhanced focus indicators"""
        if hasattr(self, 'accessibility_manager'):
            from ..accessibility_manager import AccessibilityFeature
            self.accessibility_manager.toggle_feature(AccessibilityFeature.FOCUS_INDICATORS)

            # Show feedback
            enabled = self.accessibility_manager.is_feature_enabled(AccessibilityFeature.FOCUS_INDICATORS)
            message = "Enhanced focus indicators enabled" if enabled else "Enhanced focus indicators disabled"
            self.show_info_toast(message)

    def _toggle_large_text(self):
        """Toggle large text mode"""
        if hasattr(self, 'accessibility_manager'):
            from ..accessibility_manager import AccessibilityFeature
            self.accessibility_manager.toggle_feature(AccessibilityFeature.LARGE_TEXT)

            # Show feedback
            enabled = self.accessibility_manager.is_feature_enabled(AccessibilityFeature.LARGE_TEXT)
            message = "Large text mode enabled" if enabled else "Large text mode disabled"
            self.show_info_toast(message)

    def _show_accessibility_help(self):
        """Show accessibility help information"""
        help_message = """
        🔧 Accessibility Features:

        Keyboard Shortcuts:
        • Ctrl+1: Main Menu
        • Ctrl+2: Quiz Setup
        • Ctrl+3: Settings
        • Ctrl+4: Training
        • Ctrl+Alt+H: Toggle High Contrast
        • Ctrl+Alt+F: Toggle Focus Indicators
        • Ctrl+Alt+T: Toggle Large Text
        • F1: Show this help

        Navigation:
        • Tab: Move to next element
        • Shift+Tab: Move to previous element
        • Enter/Space: Activate buttons
        • Arrow keys: Navigate within components
        """

        self.show_info_toast("Accessibility help displayed in console")
        logger.info(help_message)

    def _on_accessibility_changed(self, feature: str, enabled: bool):
        """Handle accessibility feature changes"""
        logger.info(f"♿ Accessibility feature {'enabled' if enabled else 'disabled'}: {feature}")

        # Update UI based on accessibility changes
        if feature == "high_contrast":
            self._apply_accessibility_theme()

    def _on_accessibility_theme_changed(self, theme_name: str):
        """Handle accessibility theme changes"""
        logger.info(f"🎨 Accessibility theme changed: {theme_name}")
        self._apply_accessibility_theme()

    def _apply_accessibility_theme(self):
        """Apply accessibility theme changes"""
        try:
            # Force style refresh
            if hasattr(self, 'style_manager'):
                self.style_manager.refresh_styles()

            # Update all screens
            for screen_name, screen_widget in self.screens.items():
                if hasattr(screen_widget, 'refresh_styling'):
                    screen_widget.refresh_styling()

        except Exception as e:
            logger.error(f"Error applying accessibility theme: {e}")

    def setup_screen_accessibility(self, screen_name: str, screen_widget):
        """Set up accessibility for a specific screen"""
        if hasattr(self, 'accessibility_manager'):
            self.accessibility_manager.setup_screen_accessibility(screen_widget, screen_name)

    def get_accessibility_report(self) -> dict:
        """Get accessibility compliance report"""
        if hasattr(self, 'accessibility_manager'):
            return self.accessibility_manager.get_accessibility_report()
        return {}

    def on_model_data_changed(self, key: str, value: Any) -> None:
        """Handle model data changes"""
        logger.debug(f"🔄 Model data changed: {key} = {value}")

        if key == 'current_screen':
            logger.info(f"📱 Navigating to screen: {value}")
            success = self.show_screen(value)
            if not success:
                logger.error(f"❌ Failed to show screen: {value}")
                # Try to show main_menu as fallback
                if value != 'main_menu':
                    logger.info("🔄 Falling back to main_menu")
                    self.show_screen('main_menu')
        elif key == 'theme':
            self._apply_theme(value)
        elif key == 'window_title':
            # Since this is now a QWidget, we can't set window title directly
            # The parent main window should handle this
            logger.debug(f"Window title change requested: {value}")
        elif key == 'status_message':
            if self.status_bar:
                self.status_bar.showMessage(value)
        elif key == 'window_geometry':
            self._apply_window_geometry(value)
    
    def _apply_theme(self, theme_name: str) -> None:
        """Apply theme to the main window"""
        try:
            # Convert theme name to Theme enum
            theme = Theme.DARK if theme_name.lower() == 'dark' else Theme.LIGHT
            
            # Set global theme
            set_app_theme(theme)
            
            # Reapply styling
            self.apply_styling()
            
            logger.debug(f"Applied theme: {theme_name}")
            
        except Exception as e:
            logger.error(f"Error applying theme {theme_name}: {e}")
    
    def _apply_window_geometry(self, geometry: Dict[str, int]) -> None:
        """Apply window geometry"""
        try:
            # Since this is now a QWidget, we can only resize, not move
            # The parent main window should handle positioning
            self.resize(geometry['width'], geometry['height'])
            logger.debug(f"Applied geometry to view: {geometry['width']}x{geometry['height']}")

        except Exception as e:
            logger.error(f"Error applying window geometry: {e}")
    
    def _update_status_display(self) -> None:
        """Update status bar display with system information"""
        try:
            # This could show memory usage, model status, etc.
            import psutil
            memory_percent = psutil.virtual_memory().percent
            status_msg = f"Memory: {memory_percent:.1f}% | Ready"
            
            if self._model:
                current_status = self._model.get_data('status_message', 'Ready')
                if current_status != 'Ready':
                    status_msg = current_status
            
            self.status_bar.showMessage(status_msg)
            
        except Exception as e:
            logger.error(f"Error updating status display: {e}")
    
    def closeEvent(self, event: QCloseEvent) -> None:
        """Handle window close event with proper cleanup"""
        try:
            # Cleanup all screens to prevent memory leaks
            for screen_name, screen_widget in self.screens.items():
                if hasattr(screen_widget, 'cleanup'):
                    try:
                        screen_widget.cleanup()
                        logger.debug(f"✅ Cleaned up screen: {screen_name}")
                    except Exception as e:
                        logger.error(f"❌ Error cleaning up screen {screen_name}: {e}")

            # Cleanup style manager
            if hasattr(self, 'style_manager'):
                try:
                    if hasattr(self.style_manager, 'cleanup'):
                        self.style_manager.cleanup()
                    logger.debug("✅ Cleaned up style manager")
                except Exception as e:
                    logger.error(f"❌ Error cleaning up style manager: {e}")

            # Cleanup accessibility manager
            if hasattr(self, 'accessibility_manager'):
                try:
                    if hasattr(self.accessibility_manager, 'cleanup'):
                        self.accessibility_manager.cleanup()
                    logger.debug("✅ Cleaned up accessibility manager")
                except Exception as e:
                    logger.error(f"❌ Error cleaning up accessibility manager: {e}")

            logger.info("🧹 Main window cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error during main window cleanup: {e}")

        # Emit user action for controller to handle
        self.emit_user_action('close_application', {})

        # Let the controller decide whether to accept or ignore
        event.ignore()  # Controller will call event.accept() if appropriate

class MainWindowController(BaseController):
    """
    Main window controller managing navigation and application flow.
    
    Responsibilities:
    - Handle navigation requests
    - Manage application lifecycle
    - Coordinate between screens
    - Handle user actions
    - Manage settings changes
    """
    
    def __init__(self):
        super().__init__()
        
        # Register action handlers
        self.register_action_handler('navigate_to', self._handle_navigate_to)
        self.register_action_handler('go_back', self._handle_go_back)
        self.register_action_handler('change_theme', self._handle_change_theme)
        self.register_action_handler('update_settings', self._handle_update_settings)
        self.register_action_handler('close_application', self._handle_close_application)
        self.register_action_handler('show_screen', self._handle_show_screen)
    
    def set_model(self, model: MainWindowModel) -> None:
        """Connect to main window model with specific signals"""
        super().set_model(model)
        if model:
            model.navigation_changed.connect(self._on_navigation_changed)
            model.theme_changed.connect(self._on_theme_changed)
            model.settings_updated.connect(self._on_settings_updated)
    
    def _handle_navigate_to(self, action_data: Dict[str, Any]) -> bool:
        """Handle navigation request"""
        try:
            screen_name = action_data.get('screen')
            if not screen_name:
                logger.error("No screen specified for navigation")
                return False
            
            return self._model.navigate_to_screen(screen_name)
            
        except Exception as e:
            logger.error(f"Error handling navigation: {e}")
            return False
    
    def _handle_go_back(self, action_data: Dict[str, Any]) -> bool:
        """Handle go back request"""
        try:
            return self._model.go_back()
            
        except Exception as e:
            logger.error(f"Error handling go back: {e}")
            return False
    
    def _handle_change_theme(self, action_data: Dict[str, Any]) -> bool:
        """Handle theme change request"""
        try:
            theme_name = action_data.get('theme')
            if not theme_name:
                logger.error("No theme specified")
                return False
            
            return self._model.set_theme(theme_name)
            
        except Exception as e:
            logger.error(f"Error handling theme change: {e}")
            return False
    
    def _handle_update_settings(self, action_data: Dict[str, Any]) -> bool:
        """Handle settings update request"""
        try:
            settings = action_data.get('settings', {})
            return self._model.update_settings(settings)
            
        except Exception as e:
            logger.error(f"Error handling settings update: {e}")
            return False
    
    def _handle_show_screen(self, action_data: Dict[str, Any]) -> bool:
        """Handle direct screen show request"""
        try:
            screen_name = action_data.get('screen')
            if not screen_name:
                return False
            
            return self._model.navigate_to_screen(screen_name)
            
        except Exception as e:
            logger.error(f"Error handling show screen: {e}")
            return False
    
    def _handle_close_application(self, action_data: Dict[str, Any]) -> bool:
        """Handle application close request"""
        try:
            logger.info("Application close requested")
            
            # Perform cleanup operations
            self._cleanup_application()
            
            # Accept the close event
            if self._view and hasattr(self._view, 'close'):
                self._view.close()
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling application close: {e}")
            return False
    
    def _cleanup_application(self) -> None:
        """Perform application cleanup"""
        try:
            logger.info("Performing application cleanup...")
            
            # Save current state
            if self._model:
                # Save window geometry, current screen, etc.
                logger.debug("Saving application state...")
            
            # Cleanup resources
            # This would cleanup models, managers, etc.
            
            logger.info("Application cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during application cleanup: {e}")
    
    def _on_navigation_changed(self, from_screen: str, to_screen: str) -> None:
        """Handle navigation change from model"""
        logger.debug(f"Navigation changed: {from_screen} -> {to_screen}")
        
        # Could trigger additional actions based on navigation
        # For example, updating breadcrumbs, analytics, etc.
    
    def _on_theme_changed(self, theme_name: str) -> None:
        """Handle theme change from model"""
        logger.debug(f"Theme changed to: {theme_name}")
        
        # Could trigger additional theme-related actions
    
    def _on_settings_updated(self, settings: Dict[str, Any]) -> None:
        """Handle settings update from model"""
        logger.debug(f"Settings updated: {list(settings.keys())}")

        # Could trigger actions based on specific settings changes

    def handle_user_action(self, action_name: str, action_data: Dict[str, Any]) -> bool:
        """Handle user actions from the view (override to return bool)"""
        try:
            handler = self._action_handlers.get(action_name)
            if handler:
                logger.debug(f"Handling user action: {action_name}")
                result = handler(action_data)
                success = result if isinstance(result, bool) else True
                self.action_completed.emit(action_name, success, "")
                return success
            else:
                logger.warning(f"No handler registered for action: {action_name}")
                self.action_completed.emit(action_name, False, f"Unknown action: {action_name}")
                return False

        except Exception as e:
            logger.error(f"Error handling action {action_name}: {e}")
            self.action_completed.emit(action_name, False, str(e))
            return False
