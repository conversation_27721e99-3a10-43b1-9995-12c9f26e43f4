"""
Enterprise Main Window Factory

This module creates and assembles the enterprise main window using the MVC pattern,
replacing the massive 1671-line God Object with a clean, professional architecture.

Features:
- MVC architecture assembly
- Screen integration and management
- Professional styling application
- Dependency injection support
- Clean separation of concerns
"""

from typing import Dict, Any, Optional
from PyQt5.QtWidgets import Q<PERSON>ainWindow, QWidget
from PyQt5.QtCore import pyqtSignal

from .mvc.main_window_mvc import MainWindowModel, MainWindowView, MainWindowController
from .mvc.base_mvc import MVCTriad
from .enterprise_design_system import get_design_system, Theme
from .enterprise_style_manager import get_style_manager
import logging

logger = logging.getLogger(__name__)

class EnterpriseMainWindow(QMainWindow):
    """
    Enterprise-grade main window that replaces the 1671-line God Object.
    
    This class serves as a facade that assembles and coordinates the MVC components
    while providing a clean interface for the rest of the application.
    """
    
    # Signals for external communication
    screen_changed = pyqtSignal(str)  # screen_name
    theme_changed = pyqtSignal(str)   # theme_name
    closing = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)

        # MVC components
        self.model: Optional[MainWindowModel] = None
        self.view: Optional[MainWindowView] = None
        self.controller: Optional[MainWindowController] = None
        self.mvc_triad: Optional[MVCTriad] = None

        # Screen registry
        self.screen_registry: Dict[str, QWidget] = {}

        # Design system
        self.ds = get_design_system()
        self.style_manager = get_style_manager()

        # MCQ manager (for backward compatibility with quiz setup screen)
        self.mcq_manager = None

        # Quiz parameters (for synchronous question generation)
        self.current_topic = 'general knowledge'
        self.current_mode = 'Casual'
        self.current_submode = 'Multiple Choice'

        # Initialize the enterprise main window
        self._initialize_enterprise_window()
    
    def _initialize_enterprise_window(self) -> None:
        """Initialize the enterprise main window with MVC architecture"""
        try:
            logger.info("🏗️ Initializing Enterprise Main Window...")
            
            # Create MVC components
            self.model = MainWindowModel()
            self.view = MainWindowView(self)
            self.controller = MainWindowController()
            
            # Create MVC triad
            self.mvc_triad = MVCTriad(self.model, self.view, self.controller)

            # Connect external signals
            self._connect_external_signals()

            # Set up the main window
            self._setup_main_window()

            # CRITICAL: Initialize the view FIRST to create screen stack
            if self.view:
                self.view.initialize()
                logger.info("✅ View initialized with screen stack")

                # CRITICAL FIX: Verify screen stack was created with explicit None checks
                screen_stack_exists = hasattr(self.view, 'screen_stack')
                screen_stack_not_none = screen_stack_exists and self.view.screen_stack is not None
                screen_stack_functional = screen_stack_not_none and hasattr(self.view.screen_stack, 'addWidget')

                logger.debug(f"🔍 Enterprise screen stack validation:")
                logger.debug(f"  - exists: {screen_stack_exists}")
                logger.debug(f"  - not None: {screen_stack_not_none}")
                logger.debug(f"  - functional: {screen_stack_functional}")

                if not screen_stack_functional:
                    logger.error("❌ Screen stack not created during view initialization")
                    logger.error(f"❌ Screen stack state: exists={screen_stack_exists}, not_none={screen_stack_not_none}, functional={screen_stack_functional}")
                    if screen_stack_exists:
                        logger.error(f"❌ screen_stack value: {self.view.screen_stack}")
                        logger.error(f"❌ screen_stack type: {type(self.view.screen_stack) if self.view.screen_stack is not None else 'None'}")

                    # Attempt recovery by re-initializing the view
                    logger.warning("⚠️ Attempting to recover by re-initializing view...")
                    try:
                        self.view.setup_ui()  # Try to re-setup the UI

                        # Re-check after recovery attempt
                        recovery_successful = (
                            hasattr(self.view, 'screen_stack') and
                            self.view.screen_stack is not None and
                            hasattr(self.view.screen_stack, 'addWidget')
                        )

                        if recovery_successful:
                            logger.info("✅ Screen stack recovery successful")
                        else:
                            logger.error("❌ Screen stack recovery failed")
                            raise RuntimeError("Screen stack initialization failed and recovery failed")

                    except Exception as recovery_error:
                        logger.error(f"❌ Screen stack recovery failed: {recovery_error}")
                        raise RuntimeError("Screen stack initialization failed and recovery failed") from recovery_error
                else:
                    logger.info(f"✅ Screen stack verified: {type(self.view.screen_stack).__name__}")

            # CRITICAL: Initialize default screens AFTER view and screen stack are ready
            # This ensures screens can be properly added to the stack
            self._initialize_default_screens()

            # CRITICAL: Ensure initial screen is shown
            self._ensure_initial_screen_display()
            
            logger.info("✅ Enterprise Main Window initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Enterprise Main Window: {e}")
            raise
    
    def _connect_external_signals(self) -> None:
        """Connect internal MVC signals to external signals"""
        if self.model:
            self.model.navigation_changed.connect(
                lambda from_screen, to_screen: self.screen_changed.emit(to_screen)
            )
            self.model.theme_changed.connect(self.theme_changed.emit)
    
    def _setup_main_window(self) -> None:
        """Set up main window properties"""
        # Use the view as the central widget
        self.setCentralWidget(self.view)

        # Set up status bar from the view
        if self.view and self.view.status_bar:
            self.setStatusBar(self.view.status_bar)

        # Apply initial styling - Temporarily disabled to prevent conflicts
        # self.style_manager.apply_global_styles()

        # Apply minimal main window styling only
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #0F172A;
                color: #F8FAFC;
            }}
        """)

        # Set initial window properties from model
        if self.model:
            geometry = self.model.get_data('window_geometry', {})
            if geometry:
                self.resize(geometry.get('width', 1200), geometry.get('height', 800))

            title = self.model.get_data('window_title', 'Knowledge App - Enterprise Edition')
            self.setWindowTitle(title)
    
    def _initialize_default_screens(self) -> None:
        """Initialize default screens with backward compatibility and fallbacks"""
        try:
            logger.info("🔄 Initializing default screens for Enterprise Main Window...")

            # Import screen classes with fallbacks to match MainWindowView expectations
            screen_classes = {}

            # Main menu screen - prefer SeductiveMainMenu for better visual appeal
            try:
                from .seductive_main_menu import SeductiveMainMenu
                screen_classes['main_menu'] = SeductiveMainMenu
                logger.debug("✅ Using SeductiveMainMenu for main_menu (preferred for visual appeal)")
            except ImportError:
                try:
                    from .mvc.main_menu_mvc import MainMenuMVC
                    screen_classes['main_menu'] = MainMenuMVC
                    logger.debug("✅ Using MainMenuMVC for main_menu (fallback)")
                except ImportError as e:
                    logger.error(f"❌ Failed to import main menu class: {e}")

            # Quiz screen - try MVC first, fallback to ProfessionalQuizScreen
            try:
                from .mvc.quiz_screen_mvc import QuizScreenMVC
                screen_classes['quiz_screen'] = QuizScreenMVC
                logger.debug("✅ Using QuizScreenMVC for quiz_screen")
            except ImportError:
                try:
                    from .professional_quiz_screen import ProfessionalQuizScreen
                    screen_classes['quiz_screen'] = ProfessionalQuizScreen
                    logger.debug("✅ Using ProfessionalQuizScreen for quiz_screen (fallback)")
                except ImportError as e:
                    logger.error(f"❌ Failed to import quiz screen class: {e}")

            # Quiz setup screen
            try:
                from .quiz_setup_screen import QuizSetupScreen
                screen_classes['quiz_setup'] = QuizSetupScreen
                logger.debug("✅ Using QuizSetupScreen for quiz_setup")
            except ImportError as e:
                logger.error(f"❌ Failed to import quiz setup screen class: {e}")

            # Settings screen
            try:
                from .professional_settings_screen import ProfessionalSettingsScreen
                screen_classes['settings'] = ProfessionalSettingsScreen
                logger.debug("✅ Using ProfessionalSettingsScreen for settings")
            except ImportError as e:
                logger.error(f"❌ Failed to import settings screen class: {e}")

            # Create and register screens
            for screen_name, screen_class in screen_classes.items():
                try:
                    # Special handling for QuizScreenMVC which has different constructor signature
                    if screen_name == 'quiz_screen' and hasattr(screen_class, '__name__') and 'QuizScreenMVC' in screen_class.__name__:
                        # Create a question service adapter for the QuizScreenMVC
                        question_service = self._create_question_service_adapter()
                        screen_widget = screen_class(question_service=question_service, parent=self)
                        logger.debug(f"✅ Created QuizScreenMVC with question service adapter and parent: {self}")
                    else:
                        # Other screens expect parent as first parameter
                        screen_widget = screen_class(self)

                    self.add_screen(screen_name, screen_widget)
                    logger.debug(f"✅ Created screen: {screen_name}")
                except Exception as e:
                    logger.error(f"❌ Failed to create screen {screen_name}: {e}")
                    import traceback
                    logger.error(f"❌ Traceback: {traceback.format_exc()}")

            # Log final screen registry
            logger.info(f"📋 Enterprise screens created: {list(screen_classes.keys())}")

            # Set initial screen
            logger.info("🎯 Setting initial screen to main_menu")
            success = self.navigate_to('main_menu')
            if not success:
                logger.error("❌ Failed to navigate to main_menu")

        except Exception as e:
            logger.error(f"❌ Failed to initialize default screens: {e}")

    def _ensure_initial_screen_display(self) -> None:
        """Ensure the initial screen is properly displayed"""
        try:
            # First, process any pending screens
            self._process_pending_screens()

            if self.model and self.view:
                current_screen = self.model.get_data('current_screen')
                logger.info(f"🔍 Current screen in model: {current_screen}")

                # Force the view to sync with the model
                if current_screen and current_screen in self.view.screens:
                    logger.info(f"🔄 Forcing view sync for screen: {current_screen}")
                    self.view.show_screen(current_screen)
                else:
                    logger.warning(f"⚠️ Screen not found in view: {current_screen}")
                    available_screens = list(self.view.screens.keys()) if self.view.screens else []
                    logger.info(f"📋 Available screens: {available_screens}")

        except Exception as e:
            logger.error(f"❌ Error ensuring initial screen display: {e}")
    
    def add_screen(self, screen_name: str, screen_widget: QWidget) -> None:
        """Add a screen to the main window with comprehensive error handling"""
        try:
            # Register the screen first
            self.screen_registry[screen_name] = screen_widget
            logger.debug(f"📝 Added {screen_name} to screen registry")

            # Add to view if available and properly initialized
            if self.view:
                # CRITICAL FIX: Use explicit None checks for screen stack validation
                screen_stack_exists = hasattr(self.view, 'screen_stack')
                screen_stack_not_none = screen_stack_exists and self.view.screen_stack is not None
                screen_stack_functional = screen_stack_not_none and hasattr(self.view.screen_stack, 'addWidget')

                if screen_stack_functional:
                    self.view.add_screen(screen_name, screen_widget)
                    logger.debug(f"✅ Added {screen_name} to view screen stack")
                else:
                    logger.warning(f"⚠️ View screen stack not ready for {screen_name}, will add later")
                    logger.debug(f"⚠️ Screen stack state: exists={screen_stack_exists}, not_none={screen_stack_not_none}, functional={screen_stack_functional}")
                    # Store for later addition when view is ready
                    if not hasattr(self, '_pending_screens'):
                        self._pending_screens = {}
                    self._pending_screens[screen_name] = screen_widget
            else:
                logger.warning(f"⚠️ View not available for {screen_name}, storing in registry only")

            logger.debug(f"✅ Successfully processed screen: {screen_name}")

        except Exception as e:
            logger.error(f"❌ Error adding screen {screen_name}: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")

    def _process_pending_screens(self) -> None:
        """Process any screens that were pending due to view not being ready"""
        try:
            if hasattr(self, '_pending_screens') and self._pending_screens:
                logger.info(f"🔄 Processing {len(self._pending_screens)} pending screens...")

                # Copy the pending screens to avoid modification during iteration
                pending_screens = dict(self._pending_screens)

                for screen_name, screen_widget in pending_screens.items():
                    try:
                        # Try to add the screen again
                        if self.view and hasattr(self.view, 'screen_stack') and self.view.screen_stack is not None:
                            self.view.add_screen(screen_name, screen_widget)
                            logger.debug(f"✅ Added pending screen: {screen_name}")
                            # Remove from pending list
                            del self._pending_screens[screen_name]
                        else:
                            logger.debug(f"⚠️ View still not ready for pending screen: {screen_name}")
                    except Exception as e:
                        logger.error(f"❌ Error processing pending screen {screen_name}: {e}")

                # Log final status
                remaining_pending = len(self._pending_screens) if hasattr(self, '_pending_screens') else 0
                if remaining_pending == 0:
                    logger.info("✅ All pending screens processed successfully")
                else:
                    logger.warning(f"⚠️ {remaining_pending} screens still pending")

        except Exception as e:
            logger.error(f"❌ Error processing pending screens: {e}")
    
    def navigate_to(self, screen_name: str) -> bool:
        """Navigate to a specific screen"""
        try:
            if self.controller:
                return self.controller.handle_user_action('navigate_to', {'screen': screen_name})
            return False
            
        except Exception as e:
            logger.error(f"Error navigating to {screen_name}: {e}")
            return False
    
    def go_back(self) -> bool:
        """Navigate back to the previous screen"""
        try:
            if self.controller:
                return self.controller.handle_user_action('go_back', {})
            return False
            
        except Exception as e:
            logger.error(f"Error going back: {e}")
            return False
    
    def set_theme(self, theme_name: str) -> bool:
        """Set the application theme"""
        try:
            if self.controller:
                return self.controller.handle_user_action('change_theme', {'theme': theme_name})
            return False
            
        except Exception as e:
            logger.error(f"Error setting theme {theme_name}: {e}")
            return False
    
    def update_settings(self, settings: Dict[str, Any]) -> bool:
        """Update application settings"""
        try:
            if self.controller:
                return self.controller.handle_user_action('update_settings', {'settings': settings})
            return False
            
        except Exception as e:
            logger.error(f"Error updating settings: {e}")
            return False
    
    def get_current_screen(self) -> Optional[str]:
        """Get the current screen name"""
        if self.model:
            return self.model.get_data('current_screen')
        return None
    
    def get_screen_widget(self, screen_name: str) -> Optional[QWidget]:
        """Get a screen widget by name"""
        return self.screen_registry.get(screen_name)
    
    def set_status_message(self, message: str) -> None:
        """Set the status bar message"""
        if self.model:
            self.model.set_status_message(message)
    
    # Backward compatibility methods for existing code
    def show_main_menu(self) -> None:
        """Show the main menu screen (backward compatibility)"""
        self.navigate_to('main_menu')
    
    def show_quiz_setup(self) -> None:
        """Show the quiz setup screen (backward compatibility)"""
        self.navigate_to('quiz_setup')
    
    def show_settings(self) -> None:
        """Show the settings screen (backward compatibility)"""
        self.navigate_to('settings')
    
    def show_quiz_screen(self) -> None:
        """Show the quiz screen (backward compatibility)"""
        self.navigate_to('quiz')
    
    def start_new_quiz_from_setup(self, topic: str, mode: str, question_type: str, difficulty: str = "medium") -> None:
        """Start a new quiz (backward compatibility)"""
        try:
            # Set cognitive level internally based on difficulty
            cognitive_level = self._determine_cognitive_level(difficulty)

            logger.info(f"🚀 Starting quiz: Topic='{topic}', Mode='{mode}', Type='{question_type}', Cognitive='{cognitive_level}', Difficulty='{difficulty}'")

            # Store quiz parameters for synchronous generation
            self.current_topic = topic
            self.current_mode = mode
            self.current_submode = question_type
            self.current_cognitive_level = cognitive_level
            self.current_difficulty = difficulty

            # Navigate to quiz screen (using correct screen name)
            success = self.navigate_to('quiz_screen')
            if not success:
                logger.error("Failed to navigate to quiz_screen")
                return

            # Get quiz screen and start quiz
            quiz_screen = self.get_screen_widget('quiz_screen')
            if quiz_screen and hasattr(quiz_screen, 'start_quiz'):
                quiz_screen.start_quiz(topic, mode, question_type)
                logger.info("✅ Quiz started successfully")
            else:
                logger.error("Quiz screen not available or doesn't support start_quiz")

        except Exception as e:
            logger.error(f"Error starting quiz: {e}")

    def _determine_cognitive_level(self, difficulty: str) -> str:
        """Determine appropriate cognitive level based on difficulty"""
        cognitive_mapping = {
            "easy": "understanding",
            "medium": "applying",
            "hard": "analyzing",
            "expert": "evaluating"
        }
        return cognitive_mapping.get(difficulty.lower(), "understanding")

    def start_new_quiz_from_setup_advanced(self, topic: str, mode: str, question_type: str,
                                          cognitive_level: str = "understanding", use_advanced: bool = True) -> None:
        """Start a new quiz with advanced parameters from the setup screen"""
        try:
            logger.info(f"🧠 Starting advanced quiz: Topic='{topic}', Mode='{mode}', Type='{question_type}', Cognitive='{cognitive_level}', Advanced={use_advanced}")

            # Store quiz parameters for synchronous generation (including advanced ones)
            self.current_topic = topic
            self.current_mode = mode
            self.current_submode = question_type
            self.current_cognitive_level = cognitive_level
            self.current_use_advanced = use_advanced

            # Navigate to quiz screen (using correct screen name)
            success = self.navigate_to('quiz_screen')
            if not success:
                logger.error("Failed to navigate to quiz_screen")
                return

            # Get quiz screen and start quiz with advanced parameters
            quiz_screen = self.get_screen_widget('quiz_screen')
            if quiz_screen and hasattr(quiz_screen, 'start_quiz_advanced'):
                quiz_screen.start_quiz_advanced(topic, mode, question_type, cognitive_level, use_advanced)
                logger.info("✅ Advanced quiz started successfully")
            elif quiz_screen and hasattr(quiz_screen, 'start_quiz'):
                # Fallback to regular start_quiz
                quiz_screen.start_quiz(topic, mode, question_type)
                logger.info("✅ Quiz started successfully (fallback to regular mode)")
            else:
                logger.error("Quiz screen not available or doesn't support quiz starting")

        except Exception as e:
            logger.error(f"Error starting advanced quiz: {e}")

    def get_next_question_sync(self) -> dict:
        """
        Synchronously get the next question for quiz controller.
        This method returns a question immediately without async operations.
        """
        try:
            print("DEBUG_RUNTIME: EnterpriseMainWindow.get_next_question_sync called!")
            logger.info("🔄 Getting next question synchronously for quiz controller (Enterprise)")

            # Use existing quiz parameters (including advanced ones)
            topic = getattr(self, 'current_topic', 'general knowledge')
            mode = getattr(self, 'current_mode', 'Casual')
            question_type = getattr(self, 'current_submode', 'Multiple Choice')
            cognitive_level = getattr(self, 'current_cognitive_level', 'understanding')
            use_advanced = getattr(self, 'current_use_advanced', True)

            # Create quiz parameters dictionary (NO MORE INSTRUCTION-BASED CONTEXT!)
            quiz_params = {
                'topic': topic,
                'difficulty': "hard" if mode == "Serious" else "medium",
                'mode': mode,
                'question_type': question_type,
                'cognitive_level': cognitive_level
            }

            logger.info(f"📝 Sync question params: Topic='{topic}', Mode='{mode}', Type='{question_type}'")

            # Try to use MCQ manager for instant generation
            try:
                mcq_manager = self._get_mcq_manager()
                if mcq_manager and mcq_manager.is_instant_available():
                    logger.info("⚡ Using MCQ manager instant generation for sync question")

                    # Use asyncio to run the async method synchronously
                    import asyncio
                    try:
                        # Try to get existing event loop
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If loop is running, we can't use run_until_complete
                            # Fall back to intelligent fallback
                            logger.info("⚡ Event loop running, using intelligent fallback")
                            return self._generate_fallback_question(topic, question_type)
                        else:
                            # Loop exists but not running, use it
                            result = loop.run_until_complete(
                                mcq_manager.generate_quiz_with_params_async(quiz_params)
                            )
                    except RuntimeError:
                        # No event loop, create one
                        result = asyncio.run(
                            mcq_manager.generate_quiz_async(quiz_params)
                        )

                    if result and result.get('question'):
                        logger.info("✅ MCQ manager generated sync question successfully")
                        return self._format_question_for_display(result)
                    else:
                        logger.warning("⚠️ MCQ manager returned empty result, using fallback")

            except Exception as e:
                logger.warning(f"⚠️ MCQ manager sync generation failed: {e}")

            # Fall back to intelligent fallback question
            logger.info("🔄 Using intelligent fallback for sync question")
            return self._generate_fallback_question(topic, question_type)

        except Exception as e:
            logger.error(f"❌ Sync question generation failed: {e}")
            # Emergency fallback
            return self._generate_emergency_fallback_question()

    def _create_question_service_adapter(self):
        """Create a question service adapter that wraps the enterprise main window's MCQ functionality"""
        try:
            from .adapters.question_service_adapter import QuestionServiceAdapter
            adapter = QuestionServiceAdapter(self)
            logger.info("✅ Question service adapter created for QuizScreenMVC")
            return adapter
        except Exception as e:
            logger.error(f"❌ Failed to create question service adapter: {e}")
            return None



    def _format_question_for_display(self, mcq_result: dict) -> dict:
        """Format MCQ manager result for quiz display"""
        try:
            # Handle different option formats from MCQ manager
            options_raw = mcq_result.get('options', {})

            # Convert options to list format for display
            if isinstance(options_raw, dict):
                # Dictionary format: {"A": "content", "B": "content", ...}
                options_list = []
                for key in ['A', 'B', 'C', 'D']:
                    if key in options_raw:
                        options_list.append(options_raw[key])
                    else:
                        break  # Stop if we don't have this option
            elif isinstance(options_raw, list):
                # Already in list format
                options_list = options_raw
            else:
                # Fallback
                options_list = ['Option A', 'Option B', 'Option C', 'Option D']

            # MCQ manager returns different format, standardize it
            formatted = {
                "question": mcq_result.get('question', 'Sample question'),
                "options": options_list,
                "correct_answer": mcq_result.get('correct', mcq_result.get('correct_answer', 'A')),
                "explanation": mcq_result.get('explanation', 'No explanation available'),
                "topic": getattr(self, 'current_topic', 'general knowledge'),
                "question_type": getattr(self, 'current_submode', 'Multiple Choice'),
                "generation_method": "mcq_manager_instant",
                "difficulty": mcq_result.get('difficulty', 'medium')
            }

            # Ensure correct_answer format (should be letter like 'A', 'B', etc.)
            correct_answer = formatted['correct_answer']
            if correct_answer not in ['A', 'B', 'C', 'D']:
                # Try to convert from option text to letter
                options = formatted['options']
                if correct_answer in options:
                    formatted['correct_answer'] = chr(ord('A') + options.index(correct_answer))
                else:
                    formatted['correct_answer'] = 'A'  # Default fallback

            # Add correct_option_letter for compatibility
            formatted['correct_option_letter'] = formatted['correct_answer']

            logger.info(f"✅ Formatted question: {formatted['question'][:50]}... with {len(formatted['options'])} options")
            return formatted

        except Exception as e:
            logger.error(f"❌ Failed to format question: {e}")
            return self._generate_emergency_fallback_question()

    def _generate_fallback_question(self, topic: str, question_type: str) -> dict:
        """Generate an intelligent fallback question based on topic"""
        try:
            # Topic-specific fallback questions
            fallback_questions = {
                'physics': {
                    'question': 'What is the speed of light in vacuum?',
                    'options': ['299,792,458 m/s', '300,000,000 m/s', '299,000,000 m/s', '301,000,000 m/s'],
                    'correct_answer': 'A',
                    'explanation': 'The speed of light in vacuum is exactly 299,792,458 meters per second.'
                },
                'mathematics': {
                    'question': 'What is the value of π (pi) to 3 decimal places?',
                    'options': ['3.141', '3.142', '3.143', '3.140'],
                    'correct_answer': 'B',
                    'explanation': 'π (pi) is approximately 3.142 when rounded to 3 decimal places.'
                },
                'chemistry': {
                    'question': 'What is the chemical symbol for gold?',
                    'options': ['Go', 'Au', 'Ag', 'Gd'],
                    'correct_answer': 'B',
                    'explanation': 'Au is the chemical symbol for gold, derived from the Latin word "aurum".'
                },
                'biology': {
                    'question': 'How many chambers does a human heart have?',
                    'options': ['2', '3', '4', '5'],
                    'correct_answer': 'C',
                    'explanation': 'The human heart has 4 chambers: 2 atria and 2 ventricles.'
                },
                'history': {
                    'question': 'In which year did World War II end?',
                    'options': ['1944', '1945', '1946', '1947'],
                    'correct_answer': 'B',
                    'explanation': 'World War II ended in 1945 with the surrender of Japan in September.'
                }
            }

            # Get topic-specific question or use general fallback
            topic_lower = topic.lower()
            question_data = fallback_questions.get(topic_lower, {
                'question': 'What is the most important principle in learning?',
                'options': [
                    'Consistent practice and review',
                    'Memorizing without understanding',
                    'Avoiding challenging topics',
                    'Learning only when motivated'
                ],
                'correct_answer': 'A',
                'explanation': 'Consistent practice and review is fundamental to effective learning and retention.'
            })

            # Format the question
            formatted = {
                "question": question_data['question'],
                "options": question_data['options'],
                "correct_answer": question_data['correct_answer'],
                "correct_option_letter": question_data['correct_answer'],
                "explanation": question_data['explanation'],
                "topic": topic,
                "question_type": question_type,
                "generation_method": "intelligent_fallback",
                "difficulty": "medium"
            }

            return formatted

        except Exception as e:
            logger.error(f"❌ Failed to generate fallback question: {e}")
            return self._generate_emergency_fallback_question()

    def _generate_emergency_fallback_question(self) -> dict:
        """Generate an emergency fallback question when all else fails"""
        return {
            "question": "What is the most important principle in learning?",
            "options": [
                "Consistent practice and review",
                "Memorizing without understanding",
                "Avoiding challenging topics",
                "Learning only when motivated"
            ],
            "correct_answer": "A",
            "correct_option_letter": "A",
            "explanation": "Consistent practice and review is fundamental to effective learning and retention.",
            "topic": "Learning",
            "question_type": "Multiple Choice",
            "generation_method": "emergency_fallback",
            "difficulty": "medium"
        }

    def show_training_dialog(self) -> None:
        """Show the AI training dialog"""
        try:
            logger.info("🧠 Opening AI training dialog...")

            # Import the training dialog
            from .training_dialog import AITrainingDialog

            # Create and show the training dialog
            training_dialog = AITrainingDialog(self)

            # Connect signals for training events (if needed)
            if hasattr(training_dialog, 'training_started'):
                training_dialog.training_started.connect(self._on_training_started)
            if hasattr(training_dialog, 'training_completed'):
                training_dialog.training_completed.connect(self._on_training_completed)
            if hasattr(training_dialog, 'training_cancelled'):
                training_dialog.training_cancelled.connect(self._on_training_cancelled)

            # Show the dialog
            training_dialog.show()
            logger.info("✅ Training dialog opened successfully")

        except Exception as e:
            logger.error(f"❌ Failed to show training dialog: {e}")
            # Show error to user
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "Training Error",
                f"Failed to open training dialog: {e}"
            )

    def _on_training_started(self, config: dict) -> None:
        """Handle training started event"""
        try:
            logger.info(f"🚀 Training started with config: {config}")
            if hasattr(self, 'statusBar') and self.statusBar():
                self.statusBar().showMessage("AI model training started...")
        except Exception as e:
            logger.error(f"Error handling training start: {e}")

    def _on_training_completed(self, success: bool, message: str) -> None:
        """Handle training completion"""
        try:
            if success:
                logger.info(f"✅ Training completed successfully: {message}")
                if hasattr(self, 'statusBar') and self.statusBar():
                    self.statusBar().showMessage("AI model training completed successfully!")
            else:
                logger.error(f"❌ Training failed: {message}")
                if hasattr(self, 'statusBar') and self.statusBar():
                    self.statusBar().showMessage("AI model training failed")
        except Exception as e:
            logger.error(f"Error handling training completion: {e}")

    def _on_training_cancelled(self) -> None:
        """Handle training cancellation"""
        try:
            logger.info("⏹️ Training cancelled by user")
            if hasattr(self, 'statusBar') and self.statusBar():
                self.statusBar().showMessage("AI model training cancelled")
        except Exception as e:
            logger.error(f"Error handling training cancellation: {e}")

    def _get_mcq_manager(self):
        """
        Lazily initialize and return MCQ manager (backward compatibility method).

        This method provides compatibility with the quiz setup screen and other
        components that expect the main window to have this method.
        """
        if self.mcq_manager is None:
            try:
                logger.info("🔄 Initializing MCQ Manager for Enterprise Main Window...")

                # Try to get MCQ manager from DI container first
                try:
                    from ..core.enterprise_di_container import get_container
                    from ..core.mcq_manager import MCQManager

                    container = get_container()
                    if container.is_registered(MCQManager):
                        self.mcq_manager = container.resolve(MCQManager)
                        logger.info("✅ MCQ manager resolved from DI container")
                    else:
                        raise ValueError("MCQ manager not registered in DI container")

                except Exception as di_error:
                    logger.warning(f"Could not get MCQ manager from DI container: {di_error}")

                    # Fallback to direct initialization
                    from ..core.mcq_manager import get_mcq_manager
                    from ..core.config_manager import get_config

                    config = get_config()
                    self.mcq_manager = get_mcq_manager(config)
                    logger.info("✅ MCQ manager initialized directly")

                # Configure MCQ manager
                if self.mcq_manager:
                    # Enable instant mode by default for enterprise main window
                    self.mcq_manager.set_instant_mode(True)
                    logger.info("⚡ MCQ manager configured with instant mode enabled")

            except Exception as e:
                logger.error(f"❌ Failed to initialize MCQ manager: {e}")
                raise RuntimeError("MCQ manager initialization failed")

        return self.mcq_manager

    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            logger.info("🧹 Cleaning up Enterprise Main Window...")
            
            # Cleanup MVC triad
            if self.mvc_triad:
                self.mvc_triad.cleanup()
            
            # Cleanup screens
            for screen_name, screen_widget in self.screen_registry.items():
                if hasattr(screen_widget, 'cleanup'):
                    try:
                        screen_widget.cleanup()
                    except Exception as e:
                        logger.error(f"Error cleaning up screen {screen_name}: {e}")
            
            logger.info("✅ Enterprise Main Window cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def closeEvent(self, event) -> None:
        """Handle window close event"""
        try:
            logger.info("🚪 Enterprise Main Window close requested")
            
            # Emit closing signal
            self.closing.emit()
            
            # Perform cleanup
            self.cleanup()
            
            # Accept the event
            event.accept()
            
        except Exception as e:
            logger.error(f"Error during close event: {e}")
            event.accept()  # Still close even on error

def create_enterprise_main_window(parent: Optional[QWidget] = None) -> EnterpriseMainWindow:
    """
    Factory function to create an enterprise main window.
    
    This function provides a clean interface for creating the main window
    with all MVC components properly initialized and connected.
    
    Args:
        parent: Optional parent widget
        
    Returns:
        EnterpriseMainWindow: Fully initialized enterprise main window
    """
    try:
        logger.info("🏭 Creating Enterprise Main Window...")
        
        # Create the main window
        main_window = EnterpriseMainWindow(parent)
        
        logger.info("✅ Enterprise Main Window created successfully")
        return main_window
        
    except Exception as e:
        logger.error(f"❌ Failed to create Enterprise Main Window: {e}")
        raise

# Convenience function for backward compatibility
def create_main_window(parent: Optional[QWidget] = None) -> EnterpriseMainWindow:
    """Create main window (backward compatibility alias)"""
    return create_enterprise_main_window(parent)
