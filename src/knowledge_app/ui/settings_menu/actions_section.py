from PyQt5.QtWidgets import QVBoxLayout, QPushButton

def create_actions_section(settings_menu):
    layout = QVBoxLayout()
    apply_btn = QPushButton("Apply Settings")
    layout.addWidget(apply_btn)
    clear_btn = QPushButton("Clear Learning History")
    layout.addWidget(clear_btn)
    back_btn = QPushButton("Back to Main Menu")
    back_btn.clicked.connect(settings_menu._on_back_to_menu)
    layout.addWidget(back_btn)
    return layout 