from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QSpinBox, QComboBox, 
                            QPushButton, QLabel, QApplication)
from PyQt5.QtCore import Qt, QEvent

class BlockingSpinBox(QSpinBox):
    def __init__(self):
        super().__init__()
        self.setFocusPolicy(Qt.StrongFocus)
        self.installEventFilter(self)
        self.setKeyboardTracking(False)
        
    def eventFilter(self, obj, event):
        if event.type() == QEvent.Wheel:
            return True  # Block wheel events
        if event.type() == QEvent.MouseMove:
            return True  # Block mouse move
        if event.type() == QEvent.MouseButtonPress and not self.hasFocus():
            self.setFocus()  # First click only focuses
            return True
        return super().eventFilter(obj, event)
        
    def mousePressEvent(self, event):
        if self.hasFocus():
            super().mousePressEvent(event)
            
class BlockingComboBox(QComboBox):
    def __init__(self):
        super().__init__()
        self.setFocusPolicy(Qt.StrongFocus)
        self.installEventFilter(self)
        self._popup_visible = False
        
    def eventFilter(self, obj, event):
        if event.type() == QEvent.MouseMove:
            return True  # Block mouse move
        if event.type() == QEvent.MouseButtonPress and not self.hasFocus():
            self.setFocus()  # First click only focuses
            return True
        return super().eventFilter(obj, event)
        
    def mousePressEvent(self, event):
        if self.hasFocus():
            if not self._popup_visible:
                self.showPopup()
                self._popup_visible = True
            else:
                self.hidePopup()
                self._popup_visible = False

    def hidePopup(self):
        super().hidePopup()
        self._popup_visible = False

class TestControls(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Instructions
        instructions = QLabel(
            "Test Controls\n\n"
            "1. Click once to focus (border will highlight)\n"
            "2. Click again to change value\n"
            "3. Click Update button to verify changes"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Simple spinbox
        self.spin = BlockingSpinBox()
        self.spin.setRange(0, 100)
        self.spin.setValue(50)
        layout.addWidget(self.spin)
        
        # Simple combobox
        self.combo = BlockingComboBox()
        self.combo.addItems(["Option 1", "Option 2", "Option 3"])
        layout.addWidget(self.combo)
        
        # Value display
        self.value_label = QLabel("Current values: Spin=50, Combo=Option 1")
        layout.addWidget(self.value_label)
        
        # Update button
        update_btn = QPushButton("Update Values Display")
        update_btn.clicked.connect(self.update_values)
        layout.addWidget(update_btn)
        
        # Style everything
        self.setStyleSheet("""
            QWidget {
                background-color: #1a1b1e;
                color: white;
            }
            QSpinBox, QComboBox {
                background-color: #2b2c31;
                border: 1px solid #3f3f3f;
                padding: 5px;
                min-height: 25px;
            }
            QSpinBox:focus, QComboBox:focus {
                border: 2px solid #ff5252;
            }
            QPushButton {
                background-color: #8075FF;
                border: none;
                padding: 10px;
                margin: 5px 0;
            }
            QPushButton:pressed {
                background-color: #6258CC;
            }
            QLabel {
                margin: 10px 0;
            }
        """)
        
        self.setMinimumSize(400, 300)
        self.setWindowTitle("Test Controls")
        
    def update_values(self):
        spin_val = self.spin.value()
        combo_val = self.combo.currentText()
        self.value_label.setText(f"Current values: Spin={spin_val}, Combo={combo_val}")

def show_test_controls():
    """Helper function to show test controls window"""
    test_window = TestControls()
    test_window.show()
    return test_window 