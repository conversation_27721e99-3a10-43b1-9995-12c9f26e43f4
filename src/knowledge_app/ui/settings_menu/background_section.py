from PyQt5.QtWidgets import QVBoxLayout, QPushButton, Q<PERSON>abe<PERSON>

def create_background_section(settings_menu):
    layout = QVBoxLayout()
    upload_btn = QPushButton("Upload Images")
    layout.addWidget(upload_btn)
    upload_btn.clicked.connect(settings_menu._upload_background_images)
    manage_btn = QPushButton("Manage Images")
    layout.addWidget(manage_btn)
    manage_btn.clicked.connect(settings_menu._manage_background_images)
    storage_label = QLabel("0.00 MB used / 500 MB limit")
    layout.addWidget(storage_label)
    return layout 