from PyQt5.QtWidgets import QVBoxLayout, QPushButton

def create_books_section(settings_menu):
    layout = QVBoxLayout()
    upload_btn = QPushButton("Upload Books")
    layout.addWidget(upload_btn)
    upload_btn.clicked.connect(settings_menu._upload_books)
    manage_btn = QPushButton("Manage Books")
    layout.addWidget(manage_btn)
    manage_btn.clicked.connect(settings_menu._manage_books)
    return layout 