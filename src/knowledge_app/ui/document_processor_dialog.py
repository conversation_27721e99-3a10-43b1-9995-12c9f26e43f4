"""
Multimodal Document Processor UI

Advanced UI for processing complex documents with text, images, and diagrams
into structured AI training data.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QProgressBar, QTextEdit, QGroupBox, QCheckBox,
    QComboBox, QSpinBox, QTabWidget, QWidget, QScrollArea,
    QGridLayout, QFrame, QMessageBox
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette

from ..core.multimodal_processor import MultimodalDocumentProcessor, ProcessedDocument
from ..core.document_exporter import DocumentExporter

logger = logging.getLogger(__name__)

class DocumentProcessingWorker(QThread):
    """Worker thread for document processing"""
    
    progress = pyqtSignal(str)  # Progress message
    finished = pyqtSignal(object)  # ProcessedDocument
    error = pyqtSignal(str)  # Error message
    
    def __init__(self, document_path: str, config: Dict):
        super().__init__()
        self.document_path = document_path
        self.config = config
        
    def run(self):
        """Run the document processing"""
        try:
            self.progress.emit("🚀 Initializing multimodal processor...")
            processor = MultimodalDocumentProcessor(self.config)
            
            self.progress.emit("📄 Converting document to images...")
            processed_doc = processor.process_document(self.document_path)
            
            self.progress.emit("✅ Document processing completed!")
            self.finished.emit(processed_doc)
            
        except Exception as e:
            logger.error(f"Document processing error: {e}")
            self.error.emit(str(e))

class DocumentProcessorDialog(QDialog):
    """Advanced multimodal document processing dialog"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔥 Multimodal Document Processor - AI Training Data Generator")
        self.setMinimumSize(900, 700)
        self.setModal(True)
        
        # State
        self.processed_document: Optional[ProcessedDocument] = None
        self.processing_worker: Optional[DocumentProcessingWorker] = None
        self.exporter = DocumentExporter()
        
        self._setup_ui()
        self._setup_styles()
        
        logger.info("🔥 Multimodal Document Processor UI initialized")
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🔥 MULTIMODAL DOCUMENT PROCESSOR")
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("""
            font-size: 24px; 
            font-weight: bold; 
            color: #FF6B35; 
            margin: 20px;
            padding: 10px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                                      stop:0 #FF6B35, stop:1 #F7931E);
            -webkit-background-clip: text;
            border-radius: 10px;
        """)
        layout.addWidget(header_label)
        
        # Description
        desc_label = QLabel(
            "Transform complex textbooks with text, images, and diagrams into structured AI training data.\n"
            "Uses state-of-the-art multimodal AI for document layout analysis and content extraction."
        )
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 20px;")
        layout.addWidget(desc_label)
        
        # Create tabs
        tab_widget = QTabWidget()
        
        # Input tab
        input_tab = self._create_input_tab()
        tab_widget.addTab(input_tab, "📄 Document Input")
        
        # Processing tab
        processing_tab = self._create_processing_tab()
        tab_widget.addTab(processing_tab, "⚙️ Processing")
        
        # Results tab
        results_tab = self._create_results_tab()
        tab_widget.addTab(results_tab, "📊 Results & Export")
        
        layout.addWidget(tab_widget)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.process_button = QPushButton("🚀 Start Processing")
        self.process_button.setStyleSheet("""
            QPushButton {
                background-color: #FF6B35;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                font-size: 14px;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover {
                background-color: #E55A2B;
            }
            QPushButton:disabled {
                background-color: #CCC;
            }
        """)
        self.process_button.clicked.connect(self._start_processing)
        self.process_button.setEnabled(False)
        
        self.cancel_button = QPushButton("❌ Cancel")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #666;
                color: white;
                padding: 15px 30px;
                font-size: 14px;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.process_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
    
    def _create_input_tab(self) -> QWidget:
        """Create the document input tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # File selection
        file_group = QGroupBox("📁 Document Selection")
        file_layout = QVBoxLayout(file_group)
        
        self.file_path_label = QLabel("No document selected")
        self.file_path_label.setStyleSheet("color: #666; padding: 10px; border: 2px dashed #CCC; border-radius: 5px;")
        
        select_button = QPushButton("📄 Select Document (PDF, PNG, JPG)")
        select_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 12px;
                font-size: 13px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
        """)
        select_button.clicked.connect(self._select_document)
        
        file_layout.addWidget(self.file_path_label)
        file_layout.addWidget(select_button)
        layout.addWidget(file_group)
        
        # Processing options
        options_group = QGroupBox("⚙️ Processing Options")
        options_layout = QGridLayout(options_group)
        
        # DPI setting
        options_layout.addWidget(QLabel("Image DPI:"), 0, 0)
        self.dpi_spinbox = QSpinBox()
        self.dpi_spinbox.setRange(150, 600)
        self.dpi_spinbox.setValue(300)
        self.dpi_spinbox.setSuffix(" DPI")
        options_layout.addWidget(self.dpi_spinbox, 0, 1)
        
        # AI features
        self.ai_captioning_checkbox = QCheckBox("🤖 AI Image Captioning")
        self.ai_captioning_checkbox.setChecked(True)
        self.ai_captioning_checkbox.setToolTip("Generate AI captions for extracted images")
        options_layout.addWidget(self.ai_captioning_checkbox, 1, 0, 1, 2)
        
        self.layout_analysis_checkbox = QCheckBox("🔍 Advanced Layout Analysis")
        self.layout_analysis_checkbox.setChecked(True)
        self.layout_analysis_checkbox.setToolTip("Use AI for document layout analysis")
        options_layout.addWidget(self.layout_analysis_checkbox, 2, 0, 1, 2)
        
        layout.addWidget(options_group)
        
        # Export format selection
        export_group = QGroupBox("📤 Export Formats")
        export_layout = QVBoxLayout(export_group)
        
        self.xml_checkbox = QCheckBox("📄 XML (Structured Document)")
        self.xml_checkbox.setChecked(True)
        
        self.csv_checkbox = QCheckBox("📊 CSV (Training Data)")
        self.csv_checkbox.setChecked(True)
        
        self.json_checkbox = QCheckBox("🔧 JSON (Machine Readable)")
        self.json_checkbox.setChecked(True)
        
        self.vqa_checkbox = QCheckBox("🤖 VQA Dataset (Visual Q&A)")
        self.vqa_checkbox.setChecked(True)
        
        export_layout.addWidget(self.xml_checkbox)
        export_layout.addWidget(self.csv_checkbox)
        export_layout.addWidget(self.json_checkbox)
        export_layout.addWidget(self.vqa_checkbox)
        
        layout.addWidget(export_group)
        
        return widget
    
    def _create_processing_tab(self) -> QWidget:
        """Create the processing progress tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Progress log
        log_group = QGroupBox("📋 Processing Log")
        log_layout = QVBoxLayout(log_group)
        
        self.progress_log = QTextEdit()
        self.progress_log.setReadOnly(True)
        self.progress_log.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #00FF00;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                border: 1px solid #333;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        log_layout.addWidget(self.progress_log)
        
        layout.addWidget(log_group)
        
        return widget
    
    def _create_results_tab(self) -> QWidget:
        """Create the results and export tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Results summary
        self.results_group = QGroupBox("📊 Processing Results")
        results_layout = QVBoxLayout(self.results_group)
        
        self.results_label = QLabel("No document processed yet")
        self.results_label.setStyleSheet("color: #666; padding: 20px; text-align: center;")
        results_layout.addWidget(self.results_label)
        
        self.results_group.setVisible(False)
        layout.addWidget(self.results_group)
        
        # Export buttons
        self.export_group = QGroupBox("📤 Export Options")
        export_layout = QGridLayout(self.export_group)
        
        self.export_xml_button = QPushButton("📄 Export XML")
        self.export_csv_button = QPushButton("📊 Export CSV")
        self.export_json_button = QPushButton("🔧 Export JSON")
        self.export_all_button = QPushButton("🎉 Export All Formats")
        
        for i, button in enumerate([self.export_xml_button, self.export_csv_button, 
                                   self.export_json_button, self.export_all_button]):
            button.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    padding: 10px;
                    font-size: 12px;
                    border-radius: 5px;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
                QPushButton:disabled {
                    background-color: #CCC;
                }
            """)
            button.setEnabled(False)
            export_layout.addWidget(button, i // 2, i % 2)
        
        # Connect export buttons
        self.export_xml_button.clicked.connect(lambda: self._export_format("xml"))
        self.export_csv_button.clicked.connect(lambda: self._export_format("csv"))
        self.export_json_button.clicked.connect(lambda: self._export_format("json"))
        self.export_all_button.clicked.connect(self._export_all_formats)
        
        self.export_group.setVisible(False)
        layout.addWidget(self.export_group)
        
        return widget
    
    def _setup_styles(self):
        """Setup additional styles"""
        self.setStyleSheet("""
            QDialog {
                background-color: #F5F5F5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #DDD;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def _select_document(self):
        """Select a document file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Document",
            "",
            "Documents (*.pdf *.png *.jpg *.jpeg *.tiff *.bmp);;PDF Files (*.pdf);;Image Files (*.png *.jpg *.jpeg *.tiff *.bmp)"
        )
        
        if file_path:
            self.file_path_label.setText(f"Selected: {Path(file_path).name}")
            self.file_path_label.setStyleSheet("color: #4CAF50; padding: 10px; border: 2px solid #4CAF50; border-radius: 5px;")
            self.process_button.setEnabled(True)
            self.selected_file = file_path
            
            logger.info(f"📄 Document selected: {file_path}")
    
    def _start_processing(self):
        """Start document processing"""
        if not hasattr(self, 'selected_file'):
            return
        
        # Prepare configuration
        config = {
            'dpi': self.dpi_spinbox.value(),
            'ai_captioning': self.ai_captioning_checkbox.isChecked(),
            'layout_analysis': self.layout_analysis_checkbox.isChecked(),
            'output_dir': 'data/processed_documents'
        }
        
        # Start processing
        self.processing_worker = DocumentProcessingWorker(self.selected_file, config)
        self.processing_worker.progress.connect(self._update_progress)
        self.processing_worker.finished.connect(self._processing_finished)
        self.processing_worker.error.connect(self._processing_error)
        
        self.processing_worker.start()
        
        # Update UI
        self.process_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        self._log_message("🚀 Starting multimodal document processing...")
    
    def _update_progress(self, message: str):
        """Update progress display"""
        self._log_message(message)
    
    def _processing_finished(self, processed_doc: ProcessedDocument):
        """Handle processing completion"""
        self.processed_document = processed_doc
        
        # Update UI
        self.progress_bar.setVisible(False)
        self.process_button.setEnabled(True)
        
        # Show results
        self._show_results()
        
        self._log_message("✅ Document processing completed successfully!")
        
        # Show success message
        QMessageBox.information(
            self,
            "Processing Complete",
            f"Document processed successfully!\n\n"
            f"📄 Pages: {processed_doc.total_pages}\n"
            f"🔍 Regions: {len(processed_doc.regions)}\n"
            f"🖼️ Images: {len(processed_doc.images)}\n"
            f"⏱️ Time: {processed_doc.processing_time:.2f}s"
        )
    
    def _processing_error(self, error_message: str):
        """Handle processing error"""
        self.progress_bar.setVisible(False)
        self.process_button.setEnabled(True)
        
        self._log_message(f"❌ Error: {error_message}")
        
        QMessageBox.critical(
            self,
            "Processing Error",
            f"An error occurred during processing:\n\n{error_message}"
        )
    
    def _show_results(self):
        """Show processing results"""
        if not self.processed_document:
            return
        
        doc = self.processed_document
        
        # Update results display
        results_text = f"""
        📊 PROCESSING RESULTS
        
        📄 Document: {doc.title}
        📑 Total Pages: {doc.total_pages}
        🔍 Regions Detected: {len(doc.regions)}
        🖼️ Images Extracted: {len(doc.images)}
        ⏱️ Processing Time: {doc.processing_time:.2f} seconds
        
        🤖 AI Features Used:
        • Layout Analysis: ✅
        • Image Captioning: ✅
        • Content Structuring: ✅
        """
        
        self.results_label.setText(results_text)
        self.results_label.setStyleSheet("color: #333; padding: 20px; background: #E8F5E8; border-radius: 8px;")
        
        # Show results and export groups
        self.results_group.setVisible(True)
        self.export_group.setVisible(True)
        
        # Enable export buttons
        for button in [self.export_xml_button, self.export_csv_button, 
                      self.export_json_button, self.export_all_button]:
            button.setEnabled(True)
    
    def _export_format(self, format_type: str):
        """Export to specific format"""
        if not self.processed_document:
            return
        
        try:
            if format_type == "xml":
                path = self.exporter.export_to_xml(self.processed_document)
            elif format_type == "csv":
                path = self.exporter.export_to_csv(self.processed_document)
            elif format_type == "json":
                path = self.exporter.export_to_json(self.processed_document)
            
            self._log_message(f"✅ Exported {format_type.upper()} to: {path}")
            
            QMessageBox.information(
                self,
                "Export Complete",
                f"{format_type.upper()} export completed!\n\nSaved to: {path}"
            )
            
        except Exception as e:
            self._log_message(f"❌ Export error: {e}")
            QMessageBox.critical(self, "Export Error", f"Export failed: {e}")
    
    def _export_all_formats(self):
        """Export to all formats"""
        if not self.processed_document:
            return
        
        try:
            export_paths = self.exporter.export_all_formats(self.processed_document)
            
            self._log_message("🎉 Exported to all formats successfully!")
            
            paths_text = "\n".join([f"• {fmt}: {path}" for fmt, path in export_paths.items()])
            
            QMessageBox.information(
                self,
                "Export Complete",
                f"All formats exported successfully!\n\n{paths_text}"
            )
            
        except Exception as e:
            self._log_message(f"❌ Export error: {e}")
            QMessageBox.critical(self, "Export Error", f"Export failed: {e}")
    
    def _log_message(self, message: str):
        """Add message to progress log"""
        timestamp = QTimer()
        self.progress_log.append(f"[{timestamp.remainingTime()}] {message}")
        self.progress_log.ensureCursorVisible()
