"""
FIRE Progress Widget - Real-time Training Progress with Advanced Estimation

This widget provides a sophisticated real-time training progress display with:
- Live probabilistic time estimates with confidence intervals
- Dynamic accuracy predictions with uncertainty bands
- Real-time hardware monitoring
- Interactive confidence level selection
- Professional progress visualization
"""

import time
import logging
from typing import Optional, Dict, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar,
    QGroupBox, QGridLayout, QComboBox, QPushButton, QTextEdit,
    QFrame, QSizePolicy
)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPalette, QColor
import numpy as np

# Try to import pyqtgraph, fall back to basic widgets if not available
try:
    import pyqtgraph as pg
    PYQTGRAPH_AVAILABLE = True
except ImportError:
    PYQTGRAPH_AVAILABLE = False

from ..core.fire_v21_estimator import FIREEstimator, TrainingMetrics, ProbabilisticEstimate
from .enterprise_design_system import EnterpriseDesignSystem
from .enterprise_style_manager import EnterpriseStyleManager

logger = logging.getLogger(__name__)


# SIMPLIFIED: Let Windows handle DPI scaling, we just need basic font scaling
def get_base_font_size():
    """Get base font size that works well with Windows DPI scaling"""
    return 10  # Windows will scale this automatically


def get_system_scale_factor():
    """Get system DPI scale factor for responsive UI scaling"""
    try:
        from PyQt5.QtWidgets import QApplication

        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            if screen:
                # Use device pixel ratio for more accurate scaling
                device_pixel_ratio = screen.devicePixelRatio()
                logical_dpi = screen.logicalDotsPerInch()

                # Calculate scale factor using multiple methods
                dpi_scale = logical_dpi / 96.0
                pixel_ratio_scale = device_pixel_ratio

                # Use the more conservative scaling approach
                scale_factor = max(dpi_scale, pixel_ratio_scale)

                # Clamp to reasonable bounds (0.75x to 3.0x)
                return max(0.75, min(3.0, scale_factor))
    except Exception:
        pass
    return 1.0


class FIREProgressWidget(QWidget):
    """Advanced real-time training progress widget with FIRE estimation"""
    
    # Signals
    training_cancelled = pyqtSignal()
    confidence_level_changed = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.fire_estimator = None
        self.current_estimate = None
        self.training_active = False
        self.start_time = None

        # SIMPLIFIED: Basic font size for consistent appearance
        self.base_font_size = get_base_font_size()

        # Set balanced size policy
        from PyQt5.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # Simple minimum size - let Windows handle DPI scaling
        self.setMinimumSize(400, 300)

        # Data for plotting
        self.time_history = []
        self.accuracy_history = []
        self.loss_history = []
        self.confidence_history = []

        # UI update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_displays)
        self.update_timer.setInterval(1000)  # Update every second

        self._init_ui()
        self._setup_styling()
        self._reset_to_idle_state()  # Set initial idle state
        
    def _init_ui(self):
        """Initialize the essential, focused cockpit interface"""
        # Main vertical layout
        layout = QVBoxLayout(self)
        layout.setSpacing(12)
        layout.setContentsMargins(16, 16, 16, 16)

        # Title - clean and focused
        self.title_label = QLabel("🔥 FIRE Training Monitor")
        title_font = QFont("Segoe UI", self.base_font_size + 4, QFont.Bold)
        self.title_label.setFont(title_font)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setObjectName("titleLabel")
        layout.addWidget(self.title_label)

        # Essential status & progress section
        self.progress_section = self._create_essential_progress_section()
        layout.addWidget(self.progress_section)

        # Contextual FIRE estimation section (hidden by default)
        self.fire_section = self._create_fire_estimation_section()
        self.fire_section.hide()  # Hidden until training starts
        layout.addWidget(self.fire_section)

        # Spacer to push everything up
        layout.addStretch()

        # Command bar footer (will be handled by parent dialog)
        # No controls section - following Edict of Unified Control
        
    def _create_essential_progress_section(self) -> QGroupBox:
        """Create the essential status & progress section - clean and focused"""
        group = QGroupBox("📊 Status & Progress")
        group.setObjectName("progressGroup")

        layout = QVBoxLayout(group)
        layout.setSpacing(12)

        # Single, elegant status line - THE COCKPIT DISPLAY
        self.status_line = self._create_status_line()
        layout.addWidget(self.status_line)

        # Single primary progress bar (hidden until training starts)
        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        self.overall_progress.setTextVisible(True)
        self.overall_progress.setFormat("Overall Progress: %p%")
        self.overall_progress.setObjectName("overallProgress")
        self.overall_progress.hide()  # Hidden until training starts
        layout.addWidget(self.overall_progress)

        return group

    def _create_status_line(self) -> QWidget:
        """Create the single, powerful status line - the cockpit's command center"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # Status (Large and prominent)
        self.status_label = QLabel("Ready")
        status_font = QFont("Segoe UI", self.base_font_size + 2, QFont.Bold)
        self.status_label.setFont(status_font)
        self.status_label.setObjectName("primaryStatus")
        layout.addWidget(self.status_label)

        # Separator
        layout.addWidget(QLabel("|"))

        # Essential metrics (only when training)
        self.epoch_label = QLabel("Epoch: --")
        self.loss_label = QLabel("Loss: --")
        self.eta_label = QLabel("ETA: --")

        metric_font = QFont("Segoe UI", self.base_font_size)
        for label in [self.epoch_label, self.loss_label, self.eta_label]:
            label.setFont(metric_font)
            label.setObjectName("essentialMetric")
            label.hide()  # Hidden until training starts
            layout.addWidget(label)

        layout.addStretch()
        return container
        
    def _create_fire_estimation_section(self) -> QGroupBox:
        """Create the contextual FIRE estimation section - appears only when training"""
        group = QGroupBox("🔥 FIRE Estimation & Prediction")
        group.setObjectName("fireGroup")

        layout = QVBoxLayout(group)
        layout.setSpacing(12)

        # Confidence level selector
        conf_container = QWidget()
        conf_layout = QHBoxLayout(conf_container)
        conf_layout.setContentsMargins(0, 0, 0, 0)

        conf_label = QLabel("Confidence Level:")
        conf_font = QFont("Segoe UI", self.base_font_size)
        conf_label.setFont(conf_font)
        conf_layout.addWidget(conf_label)

        self.confidence_combo = QComboBox()
        self.confidence_combo.addItems(["95%", "99%", "Mean Estimate"])
        self.confidence_combo.setCurrentText("95%")
        self.confidence_combo.currentTextChanged.connect(self._on_confidence_changed)
        self.confidence_combo.setObjectName("confidenceCombo")
        conf_layout.addWidget(self.confidence_combo)
        conf_layout.addStretch()

        layout.addWidget(conf_container)

        # Essential estimation metrics in clean grid
        metrics_container = QWidget()
        metrics_layout = QGridLayout(metrics_container)
        metrics_layout.setSpacing(12)

        # Time estimates
        self.time_remaining_label = QLabel("Remaining: --")
        self.time_elapsed_label = QLabel("Elapsed: --")

        # Accuracy predictions
        self.current_accuracy_label = QLabel("Current: --%")
        self.predicted_accuracy_label = QLabel("Predicted: --%")

        metric_font = QFont("Segoe UI", self.base_font_size)
        for label in [self.time_remaining_label, self.time_elapsed_label,
                     self.current_accuracy_label, self.predicted_accuracy_label]:
            label.setFont(metric_font)
            label.setObjectName("fireMetric")

        metrics_layout.addWidget(QLabel("⏳"), 0, 0)
        metrics_layout.addWidget(self.time_remaining_label, 0, 1)
        metrics_layout.addWidget(QLabel("⏰"), 0, 2)
        metrics_layout.addWidget(self.time_elapsed_label, 0, 3)

        metrics_layout.addWidget(QLabel("📈"), 1, 0)
        metrics_layout.addWidget(self.current_accuracy_label, 1, 1)
        metrics_layout.addWidget(QLabel("🔮"), 1, 2)
        metrics_layout.addWidget(self.predicted_accuracy_label, 1, 3)

        layout.addWidget(metrics_container)
        return group

    def get_analytics_widgets(self):
        """Return the analytics widgets for the Analytics tab"""
        if PYQTGRAPH_AVAILABLE:
            # Create charts if not already created
            if not hasattr(self, 'loss_plot'):
                self.loss_plot = pg.PlotWidget(title="Training Loss")
                self.loss_plot.setLabel('left', 'Loss')
                self.loss_plot.setLabel('bottom', 'Time (minutes)')
                self.loss_curve = self.loss_plot.plot(pen='r', name='Loss')

                self.accuracy_plot = pg.PlotWidget(title="Accuracy Progress")
                self.accuracy_plot.setLabel('left', 'Accuracy (%)')
                self.accuracy_plot.setLabel('bottom', 'Time (minutes)')
                self.accuracy_curve = self.accuracy_plot.plot(pen='g', name='Accuracy')

            return [self.loss_plot, self.accuracy_plot]
        else:
            # Fallback message
            if not hasattr(self, 'analytics_message'):
                self.analytics_message = QLabel(
                    "📈 Analytics charts require pyqtgraph\n\n"
                    "Install with: pip install pyqtgraph\n"
                    "Then restart the application to see real-time charts."
                )
                self.analytics_message.setAlignment(Qt.AlignCenter)
                self.analytics_message.setObjectName("analyticsMessage")
            return [self.analytics_message]

    def _reset_to_idle_state(self):
        """Reset to clean idle state - following Principle of Essentialism"""
        # Hide progress bar (no progress to show when idle)
        self.overall_progress.hide()
        self.overall_progress.setValue(0)

        # Reset to clean idle status
        self.status_label.setText("Ready")

        # Hide essential metrics (no data when idle)
        self.epoch_label.hide()
        self.loss_label.hide()
        self.eta_label.hide()

        # Hide the entire FIRE section (contextual - only appears when training)
        self.fire_section.hide()

        # Clear chart data
        if PYQTGRAPH_AVAILABLE and hasattr(self, 'loss_curve'):
            self.loss_curve.clear()
            self.accuracy_curve.clear()

    def _setup_styling(self):
        """Setup essential, focused styling - clean cockpit aesthetics"""
        self.setStyleSheet("""
            /* Main widget - clean and focused */
            FIREProgressWidget {
                background-color: #1e1e2e;
                color: #cdd6f4;
                border-radius: 8px;
                padding: 16px;
            }

            /* Title - clean and prominent */
            #titleLabel {
                color: #f38ba8;
                font-weight: bold;
                padding: 12px;
                border-radius: 8px;
                background-color: rgba(243, 139, 168, 0.15);
                border: 2px solid rgba(243, 139, 168, 0.4);
            }

            /* Group boxes - essential sections only */
            QGroupBox {
                font-weight: 600;
                color: #cdd6f4;
                border: 2px solid #45475a;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 12px;
                background-color: #313244;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 4px 12px;
                background-color: #1e1e2e;
                border-radius: 6px;
                color: #f9e2af;
                font-weight: bold;
            }

            /* Progress section styling */
            #progressGroup {
                border-color: #89b4fa;
            }
            #progressGroup::title {
                color: #89b4fa;
            }

            /* FIRE section styling */
            #fireGroup {
                border-color: #f38ba8;
            }
            #fireGroup::title {
                color: #f38ba8;
            }

            /* Primary status - the cockpit's main display */
            #primaryStatus {
                color: #f9e2af;
                font-weight: bold;
                padding: 12px 16px;
                background-color: #2a2a3e;
                border-radius: 8px;
                border: 2px solid #f9e2af;
            }

            /* Essential metrics - clean and focused */
            #essentialMetric {
                color: #cdd6f4;
                font-weight: 500;
                padding: 8px 12px;
                background-color: #2a2a3e;
                border-radius: 6px;
                border: 1px solid #45475a;
            }

            /* FIRE metrics */
            #fireMetric {
                color: #f38ba8;
                font-weight: 500;
                padding: 6px 10px;
                background-color: #2a2a3e;
                border-radius: 4px;
                border: 1px solid #f38ba8;
            }

            /* Progress bar - appears only when training */
            #overallProgress {
                border: 2px solid #45475a;
                border-radius: 8px;
                text-align: center;
                background-color: #181825;
                color: #cdd6f4;
                font-weight: 600;
                padding: 3px;
                min-height: 28px;
            }

            #overallProgress::chunk {
                background-color: #89b4fa;
                border-radius: 6px;
            }

            /* Confidence combo */
            #confidenceCombo {
                background-color: #313244;
                border: 2px solid #45475a;
                border-radius: 8px;
                padding: 6px 12px;
                color: #cdd6f4;
                font-weight: 500;
                min-height: 30px;
            }

            #confidenceCombo:hover {
                border-color: #74c7ec;
                background-color: #3a3a4e;
            }

            /* Analytics message */
            #analyticsMessage {
                color: #cdd6f4;
                background-color: #313244;
                border: 2px solid #45475a;
                border-radius: 8px;
                padding: 20px;
                font-size: 14px;
            }
        """)
        
    def start_training_session(self, fire_estimator: FIREEstimator, config: Dict[str, Any]):
        """Start training session - reveal essential elements following Principle of Essentialism"""
        self.fire_estimator = fire_estimator
        self.training_active = True
        self.start_time = time.time()

        # Extract training configuration
        self.total_epochs = config.get('epochs', 3)
        self.total_steps = config.get('total_steps', 7362)
        self.total_batches = self.total_steps // self.total_epochs if self.total_epochs > 0 else 1000

        # Clear previous data
        self.time_history.clear()
        self.accuracy_history.clear()
        self.loss_history.clear()
        self.confidence_history.clear()

        # Register for FIRE updates
        self.fire_estimator.register_update_callback(self._on_fire_update)
        self.current_estimate = self.fire_estimator.start_training_session(config)

        # REVEAL ESSENTIAL ELEMENTS (following Edict of Contextual Relevance)
        self.status_label.setText("Training Active 🔥")

        # Show essential metrics now that we have data
        self.epoch_label.show()
        self.loss_label.show()
        self.eta_label.show()

        # Show progress bar now that there's progress to track
        self.overall_progress.show()

        # Show FIRE section now that training is active
        self.fire_section.show()

        # Start UI updates
        self.update_timer.start()
        
    def update_training_metrics(self, metrics: TrainingMetrics):
        """Update with new training metrics"""
        if not self.training_active or not self.fire_estimator:
            return
            
        # Update FIRE estimator
        self.current_estimate = self.fire_estimator.update_real_time(metrics)
        
        # Store data for charts
        elapsed_minutes = metrics.time_elapsed / 60
        self.time_history.append(elapsed_minutes)
        self.accuracy_history.append(metrics.accuracy * 100)
        self.loss_history.append(metrics.loss)
        
        # Update basic progress displays
        self._update_basic_progress(metrics)
        
    def _update_basic_progress(self, metrics: TrainingMetrics):
        """Update essential progress displays - focused cockpit information"""
        if not hasattr(self, 'total_epochs'):
            self.total_epochs = 3

        # Update single progress bar
        if self.total_epochs > 0:
            epoch_fraction = metrics.epoch / self.total_epochs
            batch_fraction = (metrics.batch % 100) / 100
            overall_progress = int((epoch_fraction + batch_fraction / self.total_epochs) * 100)
            self.overall_progress.setValue(min(100, max(0, overall_progress)))

        # Update essential status line - THE COCKPIT DISPLAY
        self.epoch_label.setText(f"Epoch: {metrics.epoch}/{self.total_epochs}")
        self.loss_label.setText(f"Loss: {metrics.loss:.3f}")

        # Calculate and show ETA
        if self.current_estimate:
            eta_hours = self.current_estimate.mean_hours
            if eta_hours < 1:
                eta_text = f"ETA: {int(eta_hours * 60)}m"
            else:
                eta_text = f"ETA: ~{eta_hours:.1f}h"
        else:
            eta_text = "ETA: --"
        self.eta_label.setText(eta_text)

        # Update FIRE section metrics
        elapsed = time.time() - self.start_time if self.start_time else 0
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        self.time_elapsed_label.setText(f"Elapsed: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}")

        self.current_accuracy_label.setText(f"Current: {metrics.accuracy:.1%}")

        self.update()
        
    def _on_fire_update(self, estimate: ProbabilisticEstimate):
        """Handle FIRE estimator updates"""
        self.current_estimate = estimate
        
    def _update_displays(self):
        """Update essential displays - focused and clean"""
        if not self.training_active:
            self._reset_to_idle_state()
            return

        if not self.current_estimate:
            return

        # Update FIRE estimation section
        confidence_level = self._get_selected_confidence_level()

        if confidence_level == "mean":
            remaining_hours = self.current_estimate.mean_hours
            time_text = f"Remaining: {remaining_hours:.1f}h"
        else:
            conf_level = int(confidence_level.rstrip('%'))
            remaining_hours = self.current_estimate.confidence_intervals.get(conf_level, self.current_estimate.mean_hours)
            time_text = f"Remaining: {remaining_hours:.1f}h ({confidence_level})"

        self.time_remaining_label.setText(time_text)

        # Update accuracy prediction
        if confidence_level == "mean":
            pred_accuracy = self.current_estimate.accuracy_estimate
            acc_text = f"Predicted: {pred_accuracy:.1%}"
        else:
            conf_level = int(confidence_level.rstrip('%'))
            pred_accuracy = self.current_estimate.accuracy_confidence.get(conf_level, self.current_estimate.accuracy_estimate)
            acc_text = f"Predicted: {pred_accuracy:.1%}"

        self.predicted_accuracy_label.setText(acc_text)

        # Update charts (if they exist in analytics tab)
        self._update_charts()
        
    def _update_charts(self):
        """Update analytics charts (in Analytics tab)"""
        if len(self.time_history) < 2:
            return

        if PYQTGRAPH_AVAILABLE and hasattr(self, 'loss_curve'):
            # Update charts in Analytics tab
            self.loss_curve.setData(self.time_history, self.loss_history)
            self.accuracy_curve.setData(self.time_history, self.accuracy_history)
        
    def _get_selected_confidence_level(self) -> str:
        """Get currently selected confidence level"""
        return self.confidence_combo.currentText().lower().replace(" estimate", "")
        
    def _on_confidence_changed(self, text: str):
        """Handle confidence level change"""
        if "95%" in text:
            self.confidence_level_changed.emit(95)
        elif "99%" in text:
            self.confidence_level_changed.emit(99)
        else:
            self.confidence_level_changed.emit(0)  # Mean
            
    def stop_training_session(self, completed=True):
        """Stop training session - return to essential idle state"""
        self.training_active = False
        self.update_timer.stop()

        if completed:
            self.status_label.setText("Training Completed ✅")
        else:
            self.status_label.setText("Training Cancelled ❌")

        # Graceful cleanup with proper error handling
        if self.fire_estimator:
            try:
                # Create final metrics for FIRE estimator with correct parameter names
                final_metrics = TrainingMetrics(
                    epoch=100,  # Placeholder
                    batch=1000,  # Placeholder
                    loss=0.1,   # Placeholder
                    accuracy=0.9,  # Placeholder
                    learning_rate=0.001,
                    gpu_utilization=80.0,
                    gpu_memory_usage=70.0,  # Fixed: was 'memory_usage', now 'gpu_memory_usage'
                    time_elapsed=time.time() - self.start_time if self.start_time else 0
                )

                config = {}
                self.fire_estimator.finish_training_session(final_metrics, config)
                logger.info(f"Training session finished gracefully (completed={completed})")

            except Exception as e:
                logger.error(f"Error during training session cleanup: {e}")
                # Continue with cleanup even if FIRE estimator fails

        # Clear references to prevent memory leaks
        self.current_estimate = None

        # Return to clean idle state after brief status display
        QTimer.singleShot(3000, self._reset_to_idle_state)

    def cancel_training_session(self):
        """Cancel the current training session"""
        self.stop_training_session(completed=False)

    def force_idle_state(self):
        """Force the widget into idle state immediately (for debugging/cleanup)"""
        self.training_active = False
        self.update_timer.stop()
        self.current_estimate = None
        self.fire_estimator = None
        self._reset_to_idle_state()

    def hideEvent(self, event):
        """Handle widget hide event - stop updates when not visible"""
        if not self.training_active:
            self.update_timer.stop()
        super().hideEvent(event)

    def showEvent(self, event):
        """Handle widget show event - resume updates if training is active"""
        if self.training_active:
            self.update_timer.start()
        super().showEvent(event)
