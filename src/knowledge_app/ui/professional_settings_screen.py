"""
Professional Settings Screen for the Knowledge App - Commercial Grade Design
Modern, organized settings interface with professional styling and logical grouping
"""

from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout,
    QFrame, QCheckBox, QComboBox, QSlider, QSpinBox, QLineEdit,
    QGroupBox, QTabWidget, QScrollArea, QGraphicsDropShadowEffect,
    QSpacerItem, QSizePolicy, QFileDialog, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QSettings, QDateTime
from PyQt5.QtGui import QFont, QColor

from .enterprise_design_system import EnterpriseDesignSystem
from .enterprise_style_manager import EnterpriseStyleManager

# Compatibility layer for ProfessionalStyles migration
class ProfessionalStylesCompat:
    """Compatibility layer to ease migration from ProfessionalStyles to Enterprise Design System"""

    # Static properties for backward compatibility
    SPACING = {'XS': 4, 'SM': 8, 'MD': 16, 'LG': 24, 'XL': 32}
    COLORS = {
        'SURFACE_COLOR': '#2b2b2b', 'BORDER_COLOR': '#555555', 'BACKGROUND_SECONDARY': '#1e1e1e',
        'TEXT_SECONDARY': '#cccccc', 'PRIMARY_COLOR': '#2196F3', 'SURFACE_HOVER': '#3b3b3b',
        'TEXT_PRIMARY': '#ffffff', 'SURFACE_ELEVATED': '#3b3b3b', 'PRIMARY_HOVER': '#1976D2',
        'SUCCESS_COLOR': '#4CAF50', 'WARNING_COLOR': '#FF9800', 'ERROR_COLOR': '#F44336'
    }
    FONT_SIZES = {'BODY_MEDIUM': 14, 'HEADING_SMALL': 16}
    DIMENSIONS = {'CARD_BORDER_RADIUS': 12, 'INPUT_BORDER_RADIUS': 8}

    @staticmethod
    def get_dialog_style(): return "border: 1px solid #555; border-radius: 12px; padding: 16px;"
    @staticmethod
    def get_checkbox_style(): return "color: white; font-size: 14px;"
    @staticmethod
    def get_combobox_style(): return "background: #2b2b2b; color: white; border: 1px solid #555; border-radius: 8px; padding: 8px;"
    @staticmethod
    def get_input_style(): return "background: #2b2b2b; color: white; border: 1px solid #555; border-radius: 8px; padding: 8px;"
    @staticmethod
    def get_warning_button_style(): return "background: #FF9800; color: white; border: none; border-radius: 8px; padding: 8px 16px;"
    @staticmethod
    def get_secondary_button_style(): return "background: #555; color: white; border: none; border-radius: 8px; padding: 8px 16px;"
    @staticmethod
    def get_primary_button_style(): return "background: #2196F3; color: white; border: none; border-radius: 8px; padding: 8px 16px;"
    @staticmethod
    def get_success_button_style(): return "background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 8px 16px;"

# Use compatibility layer
ProfessionalStyles = ProfessionalStylesCompat
import logging
import os

logger = logging.getLogger(__name__)

class ProfessionalSettingsScreen(QWidget):
    """Professional settings screen with organized categories and modern design"""

    # Signals
    settings_changed = pyqtSignal(dict)
    theme_changed = pyqtSignal(str)
    font_size_changed = pyqtSignal(int)  # New signal for font size changes

    # Font size mapping from text to actual pixel values
    FONT_SIZE_MAPPING = {
        'Small': 12,
        'Medium': 16,
        'Large': 20,
        'Extra Large': 24
    }
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.settings = QSettings('KnowledgeApp', 'Settings')
        
        self.init_ui()
        self.load_settings()
        self.apply_professional_styling()
        
    def init_ui(self):
        """Initialize the professional settings interface"""
        # Main layout with professional spacing
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(
            ProfessionalStyles.SPACING['XL'],
            ProfessionalStyles.SPACING['LG'],
            ProfessionalStyles.SPACING['XL'],
            ProfessionalStyles.SPACING['LG']
        )
        main_layout.setSpacing(ProfessionalStyles.SPACING['LG'])
        self.setLayout(main_layout)
        
        # Create header
        self.create_header_section(main_layout)
        
        # Create tabbed interface for organized settings
        self.create_tabbed_interface(main_layout)
        
        # Create action buttons
        self.create_actions_section(main_layout)
        
    def create_header_section(self, parent_layout):
        """Create the header section"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(ProfessionalStyles.SPACING['SM'])
        
        # Title
        title_label = QLabel("Settings")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Configure your Knowledge App preferences")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
        
    def create_tabbed_interface(self, parent_layout):
        """Create the tabbed settings interface"""
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("settingsTabWidget")
        
        # General Settings Tab
        self.create_general_tab()
        
        # Quiz Settings Tab
        self.create_quiz_tab()
        
        # AI Model Settings Tab
        self.create_ai_tab()
        
        # Appearance Settings Tab
        self.create_appearance_tab()
        
        # Advanced Settings Tab
        self.create_advanced_tab()
        
        parent_layout.addWidget(self.tab_widget)
        
    def create_general_tab(self):
        """Create the general settings tab"""
        general_widget = QWidget()
        general_layout = QVBoxLayout(general_widget)
        general_layout.setSpacing(ProfessionalStyles.SPACING['LG'])
        
        # Application Settings Group
        app_group = self.create_settings_group("Application Settings")
        app_layout = QVBoxLayout(app_group)
        
        # Auto-save settings
        self.auto_save_checkbox = QCheckBox("Auto-save settings")
        self.auto_save_checkbox.setChecked(True)
        app_layout.addWidget(self.auto_save_checkbox)
        
        # Check for updates
        self.check_updates_checkbox = QCheckBox("Check for updates on startup")
        self.check_updates_checkbox.setChecked(True)
        app_layout.addWidget(self.check_updates_checkbox)
        
        # Default quiz mode
        mode_layout = QHBoxLayout()
        mode_label = QLabel("Default Quiz Mode:")
        self.default_mode_combo = QComboBox()
        self.default_mode_combo.addItems(["Casual", "Serious"])
        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.default_mode_combo)
        mode_layout.addStretch()
        app_layout.addLayout(mode_layout)
        
        general_layout.addWidget(app_group)
        
        # Storage Settings Group
        storage_group = self.create_settings_group("Storage Settings")
        storage_layout = QVBoxLayout(storage_group)
        
        # Data directory
        data_layout = QHBoxLayout()
        data_label = QLabel("Data Directory:")
        self.data_dir_edit = QLineEdit()
        self.data_dir_edit.setPlaceholderText("Select data directory...")
        self.browse_data_btn = QPushButton("Browse")
        self.browse_data_btn.clicked.connect(self.browse_data_directory)
        
        data_layout.addWidget(data_label)
        data_layout.addWidget(self.data_dir_edit)
        data_layout.addWidget(self.browse_data_btn)
        storage_layout.addLayout(data_layout)
        
        # Clear cache button
        self.clear_cache_btn = QPushButton("Clear Application Cache")
        self.clear_cache_btn.clicked.connect(self.clear_cache)
        storage_layout.addWidget(self.clear_cache_btn)
        
        general_layout.addWidget(storage_group)
        general_layout.addStretch()
        
        self.tab_widget.addTab(general_widget, "General")
        
    def create_quiz_tab(self):
        """Create the quiz settings tab"""
        quiz_widget = QWidget()
        quiz_layout = QVBoxLayout(quiz_widget)
        quiz_layout.setSpacing(ProfessionalStyles.SPACING['LG'])
        
        # Quiz Behavior Group
        behavior_group = self.create_settings_group("Quiz Behavior")
        behavior_layout = QVBoxLayout(behavior_group)
        
        # Show explanations
        self.show_explanations_checkbox = QCheckBox("Show explanations after each question")
        self.show_explanations_checkbox.setChecked(True)
        behavior_layout.addWidget(self.show_explanations_checkbox)
        
        # Randomize options
        self.randomize_options_checkbox = QCheckBox("Randomize answer options")
        self.randomize_options_checkbox.setChecked(True)
        behavior_layout.addWidget(self.randomize_options_checkbox)
        
        # Timer settings
        timer_layout = QHBoxLayout()
        timer_label = QLabel("Timer Duration (minutes):")
        self.timer_spinbox = QSpinBox()
        self.timer_spinbox.setRange(1, 60)
        self.timer_spinbox.setValue(5)
        timer_layout.addWidget(timer_label)
        timer_layout.addWidget(self.timer_spinbox)
        timer_layout.addStretch()
        behavior_layout.addLayout(timer_layout)
        
        quiz_layout.addWidget(behavior_group)
        
        # Difficulty Settings Group
        difficulty_group = self.create_settings_group("Difficulty Settings")
        difficulty_layout = QVBoxLayout(difficulty_group)
        
        # Default difficulty
        diff_layout = QHBoxLayout()
        diff_label = QLabel("Default Difficulty:")
        self.difficulty_combo = QComboBox()
        self.difficulty_combo.addItems(["Easy", "Medium", "Hard", "Expert"])
        self.difficulty_combo.setCurrentText("Medium")
        diff_layout.addWidget(diff_label)
        diff_layout.addWidget(self.difficulty_combo)
        diff_layout.addStretch()
        difficulty_layout.addLayout(diff_layout)
        
        # Adaptive difficulty
        self.adaptive_difficulty_checkbox = QCheckBox("Enable adaptive difficulty")
        self.adaptive_difficulty_checkbox.setToolTip("Automatically adjust difficulty based on performance")
        difficulty_layout.addWidget(self.adaptive_difficulty_checkbox)
        
        quiz_layout.addWidget(difficulty_group)
        quiz_layout.addStretch()
        
        self.tab_widget.addTab(quiz_widget, "Quiz")
        
    def create_ai_tab(self):
        """Create the AI model settings tab"""
        ai_widget = QWidget()
        ai_layout = QVBoxLayout(ai_widget)
        # Initialize enterprise design system if not already done
        if not hasattr(self, 'design_system'):
            self.design_system = EnterpriseDesignSystem()
        if not hasattr(self, 'style_manager'):
            self.style_manager = EnterpriseStyleManager()

        ai_layout.setSpacing(self.design_system.spacing('lg'))
        
        # Model Selection Group
        model_group = self.create_settings_group("AI Model Selection")
        model_layout = QVBoxLayout(model_group)
        
        # Preferred model
        model_select_layout = QHBoxLayout()
        model_label = QLabel("Preferred Model:")
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "Mistral-7B-Instruct",
            "Llama-3-8B-Instruct", 
            "Qwen-Latest-Instruct",
            "Gemma-7B-Instruct"
        ])
        model_select_layout.addWidget(model_label)
        model_select_layout.addWidget(self.model_combo)
        model_select_layout.addStretch()
        model_layout.addLayout(model_select_layout)
        
        # Use offline mode
        self.offline_mode_checkbox = QCheckBox("Prefer offline mode when available")
        self.offline_mode_checkbox.setChecked(True)
        model_layout.addWidget(self.offline_mode_checkbox)
        
        ai_layout.addWidget(model_group)
        
        # Generation Settings Group
        generation_group = self.create_settings_group("Generation Settings")
        generation_layout = QVBoxLayout(generation_group)
        
        # Temperature slider
        temp_layout = QHBoxLayout()
        temp_label = QLabel("Creativity (Temperature):")
        self.temperature_slider = QSlider(Qt.Horizontal)
        self.temperature_slider.setRange(1, 100)
        self.temperature_slider.setValue(70)
        self.temp_value_label = QLabel("0.7")
        self.temperature_slider.valueChanged.connect(
            lambda v: self.temp_value_label.setText(f"{v/100:.1f}")
        )
        temp_layout.addWidget(temp_label)
        temp_layout.addWidget(self.temperature_slider)
        temp_layout.addWidget(self.temp_value_label)
        generation_layout.addLayout(temp_layout)
        
        # Max tokens
        tokens_layout = QHBoxLayout()
        tokens_label = QLabel("Max Response Length:")
        self.max_tokens_spinbox = QSpinBox()
        self.max_tokens_spinbox.setRange(100, 2000)
        self.max_tokens_spinbox.setValue(600)
        tokens_layout.addWidget(tokens_label)
        tokens_layout.addWidget(self.max_tokens_spinbox)
        tokens_layout.addStretch()
        generation_layout.addLayout(tokens_layout)
        
        ai_layout.addWidget(generation_group)
        ai_layout.addStretch()
        
        self.tab_widget.addTab(ai_widget, "AI Models")
        
    def create_appearance_tab(self):
        """Create the appearance settings tab"""
        appearance_widget = QWidget()
        appearance_layout = QVBoxLayout(appearance_widget)
        appearance_layout.setSpacing(self.design_system.spacing('lg'))
        
        # Theme Settings Group
        theme_group = self.create_settings_group("Theme Settings")
        theme_layout = QVBoxLayout(theme_group)
        
        # Theme selection
        theme_select_layout = QHBoxLayout()
        theme_label = QLabel("Theme:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Dark", "Light"])
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        theme_select_layout.addWidget(theme_label)
        theme_select_layout.addWidget(self.theme_combo)
        theme_select_layout.addStretch()
        theme_layout.addLayout(theme_select_layout)
        
        # Font size
        font_layout = QHBoxLayout()
        font_label = QLabel("Font Size:")
        self.font_size_combo = QComboBox()
        self.font_size_combo.addItems(["Small", "Medium", "Large", "Extra Large"])
        self.font_size_combo.setCurrentText("Medium")
        self.font_size_combo.currentTextChanged.connect(self.on_font_size_changed)
        font_layout.addWidget(font_label)
        font_layout.addWidget(self.font_size_combo)
        font_layout.addStretch()
        theme_layout.addLayout(font_layout)
        
        appearance_layout.addWidget(theme_group)
        
        # Animation Settings Group
        animation_group = self.create_settings_group("Animation Settings")
        animation_layout = QVBoxLayout(animation_group)
        
        # Enable animations
        self.animations_checkbox = QCheckBox("Enable UI animations")
        self.animations_checkbox.setChecked(True)
        animation_layout.addWidget(self.animations_checkbox)
        
        # Reduce motion
        self.reduce_motion_checkbox = QCheckBox("Reduce motion for accessibility")
        animation_layout.addWidget(self.reduce_motion_checkbox)
        
        appearance_layout.addWidget(animation_group)
        appearance_layout.addStretch()
        
        self.tab_widget.addTab(appearance_widget, "Appearance")
        
    def create_advanced_tab(self):
        """Create the advanced settings tab"""
        advanced_widget = QWidget()
        advanced_layout = QVBoxLayout(advanced_widget)
        advanced_layout.setSpacing(ProfessionalStyles.SPACING['LG'])
        
        # Performance Settings Group
        performance_group = self.create_settings_group("Performance Settings")
        performance_layout = QVBoxLayout(performance_group)
        
        # GPU acceleration
        self.gpu_acceleration_checkbox = QCheckBox("Enable GPU acceleration (requires restart)")
        self.gpu_acceleration_checkbox.setChecked(True)
        performance_layout.addWidget(self.gpu_acceleration_checkbox)
        
        # Memory management
        self.memory_management_checkbox = QCheckBox("Aggressive memory management")
        self.memory_management_checkbox.setToolTip("May improve performance on low-memory systems")
        performance_layout.addWidget(self.memory_management_checkbox)
        
        # Batch size
        batch_layout = QHBoxLayout()
        batch_label = QLabel("Processing Batch Size:")
        self.batch_size_spinbox = QSpinBox()
        self.batch_size_spinbox.setRange(1, 32)
        self.batch_size_spinbox.setValue(4)
        batch_layout.addWidget(batch_label)
        batch_layout.addWidget(self.batch_size_spinbox)
        batch_layout.addStretch()
        performance_layout.addLayout(batch_layout)
        
        advanced_layout.addWidget(performance_group)
        
        # Debug Settings Group
        debug_group = self.create_settings_group("Debug Settings")
        debug_layout = QVBoxLayout(debug_group)
        
        # Enable logging
        self.logging_checkbox = QCheckBox("Enable detailed logging")
        debug_layout.addWidget(self.logging_checkbox)
        
        # Log level
        log_layout = QHBoxLayout()
        log_label = QLabel("Log Level:")
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["ERROR", "WARNING", "INFO", "DEBUG"])
        self.log_level_combo.setCurrentText("INFO")
        log_layout.addWidget(log_label)
        log_layout.addWidget(self.log_level_combo)
        log_layout.addStretch()
        debug_layout.addLayout(log_layout)
        
        # Export logs button
        self.export_logs_btn = QPushButton("Export Logs")
        self.export_logs_btn.clicked.connect(self.export_logs)
        debug_layout.addWidget(self.export_logs_btn)
        
        advanced_layout.addWidget(debug_group)
        advanced_layout.addStretch()
        
        self.tab_widget.addTab(advanced_widget, "Advanced")

    def create_settings_group(self, title):
        """Create a professional settings group box"""
        group = QGroupBox(title)
        group.setObjectName("settingsGroup")

        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 15))
        shadow.setOffset(0, 2)
        group.setGraphicsEffect(shadow)

        return group

    def create_actions_section(self, parent_layout):
        """Create the action buttons section"""
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsFrame")
        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(ProfessionalStyles.SPACING['MD'])

        # Reset to defaults button
        self.reset_btn = QPushButton("Reset to Defaults")
        self.reset_btn.setObjectName("resetButton")
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        actions_layout.addWidget(self.reset_btn)

        # Spacer
        actions_layout.addStretch()

        # Cancel button
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.setObjectName("cancelButton")
        self.cancel_btn.clicked.connect(self.cancel_changes)
        actions_layout.addWidget(self.cancel_btn)

        # Apply button
        self.apply_btn = QPushButton("Apply")
        self.apply_btn.setObjectName("applyButton")
        self.apply_btn.clicked.connect(self.apply_settings)
        actions_layout.addWidget(self.apply_btn)

        # Save button
        self.save_btn = QPushButton("Save")
        self.save_btn.setObjectName("saveButton")
        self.save_btn.clicked.connect(self.save_settings)
        actions_layout.addWidget(self.save_btn)

        # Close button
        self.close_btn = QPushButton("Close")
        self.close_btn.setObjectName("closeButton")
        self.close_btn.clicked.connect(self.close_settings)
        actions_layout.addWidget(self.close_btn)

        parent_layout.addWidget(actions_frame)

    def apply_professional_styling(self):
        """Apply comprehensive enterprise styling"""
        try:
            # Ensure enterprise design system is initialized
            if not hasattr(self, 'design_system'):
                self.design_system = EnterpriseDesignSystem()
            if not hasattr(self, 'style_manager'):
                self.style_manager = EnterpriseStyleManager()

            style = f"""
                /* Main Container */
                ProfessionalSettingsScreen {{
                    background: {self.design_system.color('bg_primary')};
                    color: {self.design_system.color('text_primary')};
                }}

                /* Header Styling */
                #titleLabel {{
                    color: {self.design_system.color('primary')};
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }}

                #subtitleLabel {{
                    color: {self.design_system.color('text_secondary')};
                    font-size: 16px;
                    margin-bottom: 24px;
                }}

            /* Tab Widget */
            #settingsTabWidget {{
                background: {self.design_system.color('surface')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: 12px;
            }}

            #settingsTabWidget::pane {{
                background: {self.design_system.color('surface')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: 8px;
                margin-top: 2px;
            }}

            #settingsTabWidget::tab-bar {{
                alignment: left;
            }}

            QTabBar::tab {{
                background: {self.design_system.color('bg_secondary')};
                color: {self.design_system.color('text_secondary')};
                border: 1px solid {self.design_system.color('border')};
                border-bottom: none;
                border-radius: 8px 8px 0px 0px;
                padding: 8px 24px;
                margin-right: 2px;
                font-size: 14px;
                font-weight: 500;
                min-width: 100px;
            }}

            QTabBar::tab:selected {{
                background: {self.design_system.color('surface')};
                color: {self.design_system.color('primary')};
                border-color: {self.design_system.color('primary')};
                font-weight: 600;
            }}

            QTabBar::tab:hover:!selected {{
                background: {self.design_system.color('surface_hover')};
                color: {self.design_system.color('text_primary')};
            }}

            /* Settings Groups */
            #settingsGroup {{
                background: {self.design_system.color('surface_elevated')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: 12px;
                margin: 8px 0px;
                padding-top: 24px;
            }}

            #settingsGroup::title {{
                color: {self.design_system.color('primary')};
                font-size: 16px;
                font-weight: 600;
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px;
                background: {self.design_system.color('surface_elevated')};
            }}

            /* Form Controls */
            QCheckBox {{
                color: {self.design_system.color('text_primary')};
                margin: 4px 0px;
                spacing: 8px;
            }}

            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
                border: 2px solid {self.design_system.color('border')};
                border-radius: 4px;
                background: {self.design_system.color('surface')};
            }}

            QCheckBox::indicator:checked {{
                background: {self.design_system.color('primary')};
                border-color: {self.design_system.color('primary')};
            }}

            QComboBox {{
                background: {self.design_system.color('surface')};
                color: {self.design_system.color('text_primary')};
                border: 2px solid {self.design_system.color('border')};
                border-radius: 8px;
                padding: 8px 12px;
                min-height: 20px;
            }}

            QComboBox:hover {{
                border-color: {self.design_system.color('primary')};
            }}

            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}

            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {self.design_system.color('text_secondary')};
            }}

            QLineEdit {{
                background: {self.design_system.color('surface')};
                color: {self.design_system.color('text_primary')};
                border: 2px solid {self.design_system.color('border')};
                border-radius: 8px;
                padding: 8px 12px;
                min-height: 20px;
            }}

            QLineEdit:focus {{
                border-color: {self.design_system.color('primary')};
            }}

            QSpinBox {{
                background: {self.design_system.color('surface')};
                color: {self.design_system.color('text_primary')};
                border: 2px solid {self.design_system.color('border')};
                border-radius: 8px;
                padding: 8px 12px;
                min-height: 20px;
                max-width: 100px;
            }}

            QSpinBox:focus {{
                border-color: {self.design_system.color('primary')};
            }}

            QSlider::groove:horizontal {{
                background: {self.design_system.color('bg_secondary')};
                height: 6px;
                border-radius: 3px;
            }}

            QSlider::handle:horizontal {{
                background: {self.design_system.color('primary')};
                border: 2px solid {self.design_system.color('primary')};
                width: 20px;
                height: 20px;
                border-radius: 10px;
                margin: -7px 0;
            }}

            QSlider::handle:horizontal:hover {{
                background: {self.design_system.color('primary_hover')};
                border-color: {self.design_system.color('primary_hover')};
            }}

            QSlider::sub-page:horizontal {{
                background: {self.design_system.color('primary')};
                border-radius: 3px;
            }}

            /* Action Buttons */
            #resetButton {{
                background: {self.design_system.color('warning')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }}

            #resetButton:hover {{
                background: {self.design_system.color('warning_hover')};
            }}

            #cancelButton {{
                background: {self.design_system.color('bg_secondary')};
                color: {self.design_system.color('text_primary')};
                border: 2px solid {self.design_system.color('border')};
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }}

            #cancelButton:hover {{
                background: {self.design_system.color('surface_hover')};
            }}

            #applyButton {{
                background: {self.design_system.color('primary')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }}

            #applyButton:hover {{
                background: {self.design_system.color('primary_hover')};
            }}

            #saveButton {{
                background: {self.design_system.color('success')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }}

            #saveButton:hover {{
                background: {self.design_system.color('success_hover')};
            }}

            #closeButton {{
                background: {self.design_system.color('bg_secondary')};
                color: {self.design_system.color('text_primary')};
                border: 2px solid {self.design_system.color('border')};
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }}

            #closeButton:hover {{
                background: {self.design_system.color('surface_hover')};
            }}
        """

            self.setStyleSheet(style)

        except Exception as e:
            logger.error(f"Error applying enterprise styling: {e}")
            # Fallback to basic styling
            self.setStyleSheet("background-color: #2b2b2b; color: white;")

    def update_theme(self, theme=None):
        """Update theme and reapply styling"""
        try:
            if theme:
                # Update design system theme
                from .enterprise_design_system import Theme
                theme_enum = Theme.DARK if theme.lower() == 'dark' else Theme.LIGHT
                self.design_system.set_theme(theme_enum)
                logger.info(f"🎨 Settings screen theme updated to: {theme}")

            # Reapply styling with new theme
            self.apply_professional_styling()

            # Force repaint
            self.update()
            self.repaint()

        except Exception as e:
            logger.error(f"❌ Error updating settings screen theme: {e}")

    def update_font_size(self, font_size=None):
        """Update font size and reapply styling"""
        try:
            if font_size:
                # Store current font size
                self._current_font_size = font_size
                logger.info(f"🔤 Settings screen font size updated to: {font_size}")

            # Reapply styling with new font size
            self.apply_professional_styling()

            # Force repaint
            self.update()
            self.repaint()

        except Exception as e:
            logger.error(f"❌ Error updating settings screen font size: {e}")

    def load_settings(self):
        """Load settings from storage"""
        try:
            # General settings
            self.auto_save_checkbox.setChecked(
                self.settings.value('general/auto_save', True, type=bool)
            )
            self.check_updates_checkbox.setChecked(
                self.settings.value('general/check_updates', True, type=bool)
            )
            self.default_mode_combo.setCurrentText(
                self.settings.value('general/default_mode', 'Casual', type=str)
            )

            # Quiz settings
            self.show_explanations_checkbox.setChecked(
                self.settings.value('quiz/show_explanations', True, type=bool)
            )
            self.randomize_options_checkbox.setChecked(
                self.settings.value('quiz/randomize_options', True, type=bool)
            )
            self.timer_spinbox.setValue(
                self.settings.value('quiz/timer_duration', 5, type=int)
            )
            self.difficulty_combo.setCurrentText(
                self.settings.value('quiz/default_difficulty', 'Medium', type=str)
            )
            self.adaptive_difficulty_checkbox.setChecked(
                self.settings.value('quiz/adaptive_difficulty', False, type=bool)
            )

            # AI settings
            self.model_combo.setCurrentText(
                self.settings.value('ai/preferred_model', 'Mistral-7B-Instruct', type=str)
            )
            self.offline_mode_checkbox.setChecked(
                self.settings.value('ai/prefer_offline', True, type=bool)
            )
            self.temperature_slider.setValue(
                int(self.settings.value('ai/temperature', 0.7, type=float) * 100)
            )
            self.max_tokens_spinbox.setValue(
                self.settings.value('ai/max_tokens', 600, type=int)
            )

            # Appearance settings
            self.theme_combo.setCurrentText(
                self.settings.value('appearance/theme', 'Dark', type=str)
            )

            # Load font size with backward compatibility
            font_size_text = self.settings.value('appearance/font_size', 'Medium', type=str)

            # Handle case where numeric font size might be stored instead of text
            if font_size_text.isdigit():
                # Convert numeric to text using reverse mapping
                font_size_numeric = int(font_size_text)
                reverse_mapping = {v: k for k, v in self.FONT_SIZE_MAPPING.items()}
                font_size_text = reverse_mapping.get(font_size_numeric, 'Medium')
                logger.info(f"🔤 Converted numeric font size {font_size_numeric} to text '{font_size_text}'")

            self.font_size_combo.setCurrentText(font_size_text)
            self.animations_checkbox.setChecked(
                self.settings.value('appearance/animations', True, type=bool)
            )
            self.reduce_motion_checkbox.setChecked(
                self.settings.value('appearance/reduce_motion', False, type=bool)
            )

            # Advanced settings
            self.gpu_acceleration_checkbox.setChecked(
                self.settings.value('advanced/gpu_acceleration', True, type=bool)
            )
            self.memory_management_checkbox.setChecked(
                self.settings.value('advanced/memory_management', False, type=bool)
            )
            self.batch_size_spinbox.setValue(
                self.settings.value('advanced/batch_size', 4, type=int)
            )
            self.logging_checkbox.setChecked(
                self.settings.value('debug/enable_logging', False, type=bool)
            )
            self.log_level_combo.setCurrentText(
                self.settings.value('debug/log_level', 'INFO', type=str)
            )

            logger.info("Settings loaded successfully")

        except Exception as e:
            logger.error(f"Error loading settings: {e}")

    def save_settings(self):
        """Save current settings to storage"""
        try:
            # General settings
            self.settings.setValue('general/auto_save', self.auto_save_checkbox.isChecked())
            self.settings.setValue('general/check_updates', self.check_updates_checkbox.isChecked())
            self.settings.setValue('general/default_mode', self.default_mode_combo.currentText())

            # Quiz settings
            self.settings.setValue('quiz/show_explanations', self.show_explanations_checkbox.isChecked())
            self.settings.setValue('quiz/randomize_options', self.randomize_options_checkbox.isChecked())
            self.settings.setValue('quiz/timer_duration', self.timer_spinbox.value())
            self.settings.setValue('quiz/default_difficulty', self.difficulty_combo.currentText())
            self.settings.setValue('quiz/adaptive_difficulty', self.adaptive_difficulty_checkbox.isChecked())

            # AI settings
            self.settings.setValue('ai/preferred_model', self.model_combo.currentText())
            self.settings.setValue('ai/prefer_offline', self.offline_mode_checkbox.isChecked())
            self.settings.setValue('ai/temperature', self.temperature_slider.value() / 100.0)
            self.settings.setValue('ai/max_tokens', self.max_tokens_spinbox.value())

            # Appearance settings
            self.settings.setValue('appearance/theme', self.theme_combo.currentText())
            self.settings.setValue('appearance/font_size', self.font_size_combo.currentText())
            # Also save numeric font size for compatibility with main window
            font_size_numeric = self.FONT_SIZE_MAPPING.get(self.font_size_combo.currentText(), 16)
            self.settings.setValue('appearance/font_size_numeric', font_size_numeric)
            self.settings.setValue('appearance/animations', self.animations_checkbox.isChecked())
            self.settings.setValue('appearance/reduce_motion', self.reduce_motion_checkbox.isChecked())

            # Advanced settings
            self.settings.setValue('advanced/gpu_acceleration', self.gpu_acceleration_checkbox.isChecked())
            self.settings.setValue('advanced/memory_management', self.memory_management_checkbox.isChecked())
            self.settings.setValue('advanced/batch_size', self.batch_size_spinbox.value())
            self.settings.setValue('debug/enable_logging', self.logging_checkbox.isChecked())
            self.settings.setValue('debug/log_level', self.log_level_combo.currentText())

            # Sync settings
            self.settings.sync()

            logger.info("Settings saved successfully")

            # Emit settings changed signal
            self.settings_changed.emit(self.get_settings_dict())

        except Exception as e:
            logger.error(f"Error saving settings: {e}")

    def get_settings_dict(self):
        """Get current settings as dictionary"""
        return {
            'general': {
                'auto_save': self.auto_save_checkbox.isChecked(),
                'check_updates': self.check_updates_checkbox.isChecked(),
                'default_mode': self.default_mode_combo.currentText(),
            },
            'quiz': {
                'show_explanations': self.show_explanations_checkbox.isChecked(),
                'randomize_options': self.randomize_options_checkbox.isChecked(),
                'timer_duration': self.timer_spinbox.value(),
                'default_difficulty': self.difficulty_combo.currentText(),
                'adaptive_difficulty': self.adaptive_difficulty_checkbox.isChecked(),
            },
            'ai': {
                'preferred_model': self.model_combo.currentText(),
                'prefer_offline': self.offline_mode_checkbox.isChecked(),
                'temperature': self.temperature_slider.value() / 100.0,
                'max_tokens': self.max_tokens_spinbox.value(),
            },
            'appearance': {
                'theme': self.theme_combo.currentText(),
                'font_size': self.font_size_combo.currentText(),
                'font_size_numeric': self.FONT_SIZE_MAPPING.get(self.font_size_combo.currentText(), 16),
                'animations': self.animations_checkbox.isChecked(),
                'reduce_motion': self.reduce_motion_checkbox.isChecked(),
            },
            'advanced': {
                'gpu_acceleration': self.gpu_acceleration_checkbox.isChecked(),
                'memory_management': self.memory_management_checkbox.isChecked(),
                'batch_size': self.batch_size_spinbox.value(),
            },
            'debug': {
                'enable_logging': self.logging_checkbox.isChecked(),
                'log_level': self.log_level_combo.currentText(),
            }
        }

    def on_theme_changed(self, theme):
        """Handle theme change"""
        try:
            logger.info(f"🎨 Settings screen theme changed to: {theme}")

            # Update this settings screen's theme immediately
            self.update_theme(theme)

            # Emit signal to update parent/main window
            self.theme_changed.emit(theme)

        except Exception as e:
            logger.error(f"Error handling theme change: {e}")

    def on_font_size_changed(self, font_size_text):
        """Handle font size change"""
        try:
            logger.info(f"🔤 Settings screen font size changed to: {font_size_text}")

            # Convert text to numeric value
            font_size_numeric = self.FONT_SIZE_MAPPING.get(font_size_text, 16)
            logger.info(f"🔤 Mapped '{font_size_text}' to {font_size_numeric}px")

            # Update this settings screen's font size immediately
            self.update_font_size(font_size_numeric)

            # Emit signal to update parent/main window
            self.font_size_changed.emit(font_size_numeric)

        except Exception as e:
            logger.error(f"Error handling font size change: {e}")

    def browse_data_directory(self):
        """Browse for data directory"""
        try:
            directory = QFileDialog.getExistingDirectory(
                self,
                "Select Data Directory",
                self.data_dir_edit.text() or os.path.expanduser("~")
            )
            if directory:
                self.data_dir_edit.setText(directory)
        except Exception as e:
            logger.error(f"Error browsing data directory: {e}")

    def clear_cache(self):
        """Clear application cache"""
        try:
            reply = QMessageBox.question(
                self,
                "Clear Cache",
                "Are you sure you want to clear the application cache?\n\nThis will remove temporary files and may improve performance.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Implementation would go here
                QMessageBox.information(
                    self,
                    "Cache Cleared",
                    "Application cache has been cleared successfully."
                )
                logger.info("Application cache cleared")

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            QMessageBox.critical(self, "Error", f"Failed to clear cache: {e}")

    def export_logs(self):
        """Export application logs"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Export Logs",
                f"knowledge_app_logs_{QDateTime.currentDateTime().toString('yyyyMMdd_hhmmss')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if filename:
                # Implementation would go here
                QMessageBox.information(
                    self,
                    "Logs Exported",
                    f"Logs have been exported to:\n{filename}"
                )
                logger.info(f"Logs exported to: {filename}")

        except Exception as e:
            logger.error(f"Error exporting logs: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export logs: {e}")

    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        try:
            reply = QMessageBox.question(
                self,
                "Reset Settings",
                "Are you sure you want to reset all settings to their default values?\n\nThis action cannot be undone.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Clear all settings
                self.settings.clear()

                # Reload default values
                self.load_settings()

                QMessageBox.information(
                    self,
                    "Settings Reset",
                    "All settings have been reset to their default values."
                )
                logger.info("Settings reset to defaults")

        except Exception as e:
            logger.error(f"Error resetting settings: {e}")
            QMessageBox.critical(self, "Error", f"Failed to reset settings: {e}")

    def cancel_changes(self):
        """Cancel changes and reload original settings"""
        try:
            self.load_settings()
            if self.parent and hasattr(self.parent, 'stack') and hasattr(self.parent, 'main_menu'):
                self.parent.stack.setCurrentWidget(self.parent.main_menu)
        except Exception as e:
            logger.error(f"Error canceling changes: {e}")

    def apply_settings(self):
        """Apply settings without closing"""
        try:
            self.save_settings()
            QMessageBox.information(
                self,
                "Settings Applied",
                "Settings have been applied successfully."
            )
        except Exception as e:
            logger.error(f"Error applying settings: {e}")
            QMessageBox.critical(self, "Error", f"Failed to apply settings: {e}")

    def save_and_close(self):
        """Save settings and close (legacy method for compatibility)"""
        try:
            self.save_settings()
            self.close_settings()
        except Exception as e:
            logger.error(f"Error saving and closing: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save settings: {e}")

    def close_settings(self):
        """Close settings screen and return to main menu"""
        try:
            if self.parent and hasattr(self.parent, 'stack') and hasattr(self.parent, 'main_menu'):
                self.parent.stack.setCurrentWidget(self.parent.main_menu)
            elif self.parent and hasattr(self.parent, 'show_main_menu'):
                self.parent.show_main_menu()
            else:
                # Fallback: close the dialog if it's a dialog
                self.close()
        except Exception as e:
            logger.error(f"Error closing settings: {e}")
            # Fallback: try to close the widget
            try:
                self.close()
            except:
                pass
