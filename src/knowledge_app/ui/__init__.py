"""UI package initialization"""

# CRITICAL MEMORY FIX: Don't import UI components at package level to reduce startup memory
# These will be imported lazily when actually needed

# from .main_window import MainWindow
# from .splash_screen import ModernSplashScreen
# from .quiz_screen import QuizScreen
# from .quiz_setup_screen import QuizSetupScreen
# from .settings_menu import SettingsMenu

# Optional ML-related imports
# try:
#     from .training_dialog import AITrainingDialog
#     __all__ = ['MainWindow', 'ModernSplashScreen', 'AITrainingDialog', 'QuizScreen', 'QuizSetupScreen', 'SettingsMenu']
# except ImportError:
#     __all__ = ['MainWindow', 'ModernSplashScreen', 'QuizScreen', 'QuizSetupScreen', 'SettingsMenu']

# Empty __all__ to prevent eager imports
__all__ = []
