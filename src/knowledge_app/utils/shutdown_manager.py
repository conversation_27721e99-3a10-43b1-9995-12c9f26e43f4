"""
Shutdown Manager

This module provides utilities for graceful application shutdown,
ensuring resources are properly released and state is saved.
"""

import os
import sys
import signal
import logging
import atexit
import threading
import gc
from typing import Callable, List, Optional, Dict
from PyQt5.QtWidgets import QApplication
import torch
import psutil
from datetime import datetime, timedelta
from queue import Queue, Empty
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class ShutdownManager:
    """Manages application shutdown process"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ShutdownManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self._shutdown_handlers: List[Callable] = []
        self._is_shutting_down = False
        self._shutdown_lock = threading.Lock()
        self._executor = Thread<PERSON>oolExecutor(max_workers=4)
        
        # Centralized memory monitoring (disable individual manager monitoring)
        self._memory_monitor = None
        self._stop_monitoring = threading.Event()
        self._memory_threshold = 0.75  # 75% memory threshold (was 85%)
        self._gpu_memory_threshold = 0.80  # 80% GPU memory threshold (was 90%)
        self._last_cleanup = datetime.now()
        self._cleanup_interval = timedelta(minutes=1)  # More frequent cleanup (was 2 minutes)
        self._cleanup_in_progress = threading.Lock()  # Prevent concurrent cleanup
        
        # Register signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Register atexit handler
        atexit.register(self.shutdown)
        
        # Start memory monitoring
        self.start_memory_monitoring()
        
    def start_memory_monitoring(self):
        """Start memory monitoring thread"""
        if self._memory_monitor is None or not self._memory_monitor.is_alive():
            self._stop_monitoring.clear()
            self._memory_monitor = threading.Thread(
                target=self._monitor_memory,
                name="MemoryMonitor",
                daemon=True
            )
            self._memory_monitor.start()
            
    def stop_memory_monitoring(self):
        """Stop memory monitoring thread"""
        if self._memory_monitor is not None:
            self._stop_monitoring.set()
            if self._memory_monitor.is_alive():
                self._memory_monitor.join(timeout=2.0)
            self._memory_monitor = None
            
    def _monitor_memory(self):
        """Memory monitoring loop"""
        while not self._stop_monitoring.is_set():
            try:
                self._check_memory_usage()
                threading.Event().wait(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Error in memory monitor: {e}")
                threading.Event().wait(5)
                
    def _check_memory_usage(self):
        """Check system and GPU memory usage"""
        try:
            current_time = datetime.now()
            if current_time - self._last_cleanup < self._cleanup_interval:
                return
                
            needs_cleanup = False
            
            # Check system memory
            memory = psutil.virtual_memory()
            if memory.percent > self._memory_threshold * 100:
                logger.warning(f"High memory usage: {memory.percent}%")
                needs_cleanup = True
                
            # Check GPU memory
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    try:
                        allocated = torch.cuda.memory_allocated(i)
                        total = torch.cuda.get_device_properties(i).total_memory
                        usage = allocated / total
                        
                        if usage > self._gpu_memory_threshold:
                            logger.warning(f"High GPU {i} memory usage: {usage*100:.1f}%")
                            needs_cleanup = True
                    except Exception as e:
                        logger.error(f"Error checking GPU {i} memory: {e}")
                        
            if needs_cleanup:
                self._cleanup_memory()
                self._last_cleanup = current_time
                
        except Exception as e:
            logger.error(f"Error checking memory usage: {e}")
            
    def _cleanup_memory(self):
        """Clean up system and GPU memory with coordination"""
        # Prevent concurrent cleanup operations
        if not self._cleanup_in_progress.acquire(blocking=False):
            logger.debug("Memory cleanup already in progress, skipping")
            return

        try:
            logger.info("Starting coordinated memory cleanup")

            # Notify other managers to pause their cleanup
            cleanup_start_time = datetime.now()

            # System memory cleanup
            collected = gc.collect()
            logger.debug(f"Garbage collection freed {collected} objects")

            # GPU memory cleanup
            if torch.cuda.is_available():
                try:
                    for i in range(torch.cuda.device_count()):
                        with torch.cuda.device(i):
                            before_cleanup = torch.cuda.memory_allocated(i) / 1024**2  # MB
                            torch.cuda.empty_cache()
                            torch.cuda.reset_peak_memory_stats()
                            after_cleanup = torch.cuda.memory_allocated(i) / 1024**2  # MB
                            freed = before_cleanup - after_cleanup
                            if freed > 0:
                                logger.info(f"GPU {i}: Freed {freed:.1f}MB memory")
                except Exception as e:
                    logger.error(f"Error cleaning GPU memory: {e}")

            cleanup_duration = datetime.now() - cleanup_start_time
            logger.info(f"Memory cleanup completed in {cleanup_duration.total_seconds():.2f}s")

        except Exception as e:
            logger.error(f"Error during coordinated memory cleanup: {e}")
        finally:
            self._cleanup_in_progress.release()
        
    def register_handler(self, name: str, handler: Callable, category: str = 'default', priority: int = 50) -> None:
        """Register a shutdown handler with priority-based ordering

        Args:
            name: Name of the handler for identification
            handler: Callable to execute during shutdown
            category: Category of the handler for grouping (default: 'default')
            priority: Priority for execution order (higher = earlier, 0-100)
        """
        if not callable(handler):
            raise ValueError("Handler must be callable")

        with self._shutdown_lock:
            if not self._is_shutting_down:
                # Store with priority for proper ordering
                self._shutdown_handlers.append((name, handler, category, priority))
                # Sort by priority (higher priority first)
                self._shutdown_handlers.sort(key=lambda x: x[3], reverse=True)
                
    def _signal_handler(self, signum: int, frame) -> None:
        """Handle system signals
        
        Args:
            signum: Signal number
            frame: Current stack frame
        """
        logger.info(f"Received signal {signum}")
        self.shutdown()
        
    def shutdown(self) -> None:
        """Execute shutdown sequence"""
        with self._shutdown_lock:
            if self._is_shutting_down:
                return
                
            self._is_shutting_down = True
            
        try:
            # Stop memory monitoring
            self.stop_memory_monitoring()
            
            # Execute handlers by priority (already sorted)
            for name, handler, category, priority in self._shutdown_handlers:
                try:
                    logger.info(f"Running shutdown handler: {name} ({category}) [priority: {priority}]")
                    handler()
                except Exception as e:
                    logger.error(f"Error in shutdown handler {name}: {e}")
                    # Continue with other handlers even if one fails
                    
            # Shutdown thread pool
            self._executor.shutdown(wait=True)
            
            # Remove logging handlers
            root_logger = logging.getLogger()
            for handler in root_logger.handlers[:]:
                try:
                    handler.close()
                    root_logger.removeHandler(handler)
                except Exception as e:
                    logger.error(f"Error removing logging handler: {e}")
                    
            # Final cleanup
            sys.stdout.flush()
            sys.stderr.flush()

            logger.info("Graceful shutdown completed successfully")

        except Exception as e:
            # Don't log here since logging might be shut down
            print(f"Error during shutdown: {e}", file=sys.stderr)

        # Note: Removed os._exit(0) to allow normal Python shutdown process
            
    @property
    def is_shutting_down(self) -> bool:
        """Check if shutdown is in progress"""
        return self._is_shutting_down

# Global shutdown manager instance
shutdown_manager = ShutdownManager()

def get_shutdown_manager() -> ShutdownManager:
    """Get the global shutdown manager instance"""
    return shutdown_manager 