from PyQt5.QtGui import QFont, QPixmap, QPainter, QFontMetrics, QImage, QColor
from PyQt5.QtCore import Qt
import re
import logging

logger = logging.getLogger(__name__)

def render_latex_to_pixmap(latex, font_size=12, dpi=100, max_width=800, background_color=None):
    pixmap = QPixmap(max_width, 1000)
    pixmap.fill(Qt.transparent)

    painter = QPainter(pixmap)
    normal_font = QFont("Helvetica", font_size)
    math_font = QFont("Helvetica", font_size, QFont.Bold)
    painter.setFont(normal_font)

    parts = []
    current_text = ""
    i = 0
    while i < len(latex):
        if latex[i:i+2] == "$$":
            if current_text:
                parts.append((current_text, False))
                current_text = ""
            i += 2
            math_text = ""
            while i < len(latex) and latex[i:i+2] != "$$":
                math_text += latex[i]
                i += 1
            if i < len(latex): i += 2
            parts.append((math_text, True))
        else:
            current_text += latex[i]
            i += 1
    if current_text:
        parts.append((current_text, False))

    y = 20
    max_width_used = 0
    line_height = QFontMetrics(normal_font).height() + 10

    for text, is_math in parts:
        if not text.strip(): continue
        lines = text.split('\n')
        for line in lines:
            if not line.strip():
                y += line_height // 2
                continue

            font = math_font if is_math else normal_font
            painter.setFont(font)
            metrics = painter.fontMetrics()
            text_width = metrics.horizontalAdvance(line)

            if text_width > max_width - 40:
                words = line.split()
                current_line = ""
                for word in words:
                    test_line = current_line + " " + word if current_line else word
                    if metrics.horizontalAdvance(test_line) <= max_width - 40:
                        current_line = test_line
                    else:
                        x = (max_width - metrics.horizontalAdvance(current_line)) // 2
                        painter.drawText(x, y, current_line)
                        max_width_used = max(max_width_used, metrics.horizontalAdvance(current_line))
                        y += line_height
                        current_line = word
                if current_line:
                    x = (max_width - metrics.horizontalAdvance(current_line)) // 2
                    painter.drawText(x, y, current_line)
                    max_width_used = max(max_width_used, metrics.horizontalAdvance(current_line))
                    y += line_height
            else:
                x = (max_width - text_width) // 2
                painter.drawText(x, y, line)
                max_width_used = max(max_width_used, text_width)
                y += line_height

    painter.end()
    return pixmap.copy(0, 0, max(max_width_used + 40, max_width), y + 20)

def validate_latex(latex_code):
    stack = []
    i = 0
    while i < len(latex_code):
        if latex_code[i:i+2] == '$$':
            if not stack or stack[-1] != '$$':
                stack.append('$$')
            else:
                stack.pop()
            i += 2
        else:
            i += 1
    if stack:
        raise ValueError("Unbalanced $$ in LaTeX.")
    return latex_code
