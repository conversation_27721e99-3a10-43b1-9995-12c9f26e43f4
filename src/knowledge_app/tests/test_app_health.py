"""
test_app_health.py: Basic tests for dependency checking and model loading.
"""
import unittest
from knowledge_app import app_health

class TestAppHealth(unittest.TestCase):
    def setUp(self):
        print("\nRunning test case:", self._testMethodName)

    def test_dependencies(self):
        print("Starting dependency test")
        ok, msg = app_health.check_dependencies()
        print(f"Dependencies check result: ok={ok}, msg={msg}")
        self.assertTrue(ok, msg)

    def test_logging(self):
        print("Starting logging test")
        log_path = app_health.setup_logging()
        print(f"Log path: {log_path}")
        self.assertTrue(log_path.endswith("knowledge_app.log"))

if __name__ == "__main__":
    print("Starting test execution")
    unittest.main(verbosity=2)
