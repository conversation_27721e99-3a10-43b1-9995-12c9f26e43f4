{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 900.0, "eval_steps": 500, "global_step": 900, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "grad_norm": 0.0, "learning_rate": 0.0, "loss": 3.5297, "step": 1}, {"epoch": 10.0, "grad_norm": 0.0, "learning_rate": 1.2e-05, "loss": 3.5297, "step": 10}, {"epoch": 20.0, "grad_norm": 0.0, "learning_rate": 2.5333333333333337e-05, "loss": 3.5297, "step": 20}, {"epoch": 30.0, "grad_norm": 0.0, "learning_rate": 3.866666666666667e-05, "loss": 3.5297, "step": 30}, {"epoch": 40.0, "grad_norm": 0.0, "learning_rate": 5.2000000000000004e-05, "loss": 3.5297, "step": 40}, {"epoch": 50.0, "grad_norm": 0.0, "learning_rate": 6.533333333333334e-05, "loss": 3.5297, "step": 50}, {"epoch": 60.0, "grad_norm": 0.0, "learning_rate": 7.866666666666666e-05, "loss": 3.5297, "step": 60}, {"epoch": 70.0, "grad_norm": 0.0, "learning_rate": 9.200000000000001e-05, "loss": 3.5297, "step": 70}, {"epoch": 80.0, "grad_norm": 0.0, "learning_rate": 0.00010533333333333332, "loss": 3.5297, "step": 80}, {"epoch": 90.0, "grad_norm": 0.0, "learning_rate": 0.00011866666666666669, "loss": 3.5297, "step": 90}, {"epoch": 100.0, "grad_norm": 0.0, "learning_rate": 0.000132, "loss": 3.5297, "step": 100}, {"epoch": 110.0, "grad_norm": 0.0, "learning_rate": 0.00014533333333333333, "loss": 3.5297, "step": 110}, {"epoch": 120.0, "grad_norm": 0.0, "learning_rate": 0.00015866666666666668, "loss": 3.5297, "step": 120}, {"epoch": 130.0, "grad_norm": 0.0, "learning_rate": 0.000172, "loss": 3.5297, "step": 130}, {"epoch": 140.0, "grad_norm": 0.0, "learning_rate": 0.00018533333333333333, "loss": 3.5297, "step": 140}, {"epoch": 150.0, "grad_norm": 0.0, "learning_rate": 0.00019866666666666668, "loss": 3.5297, "step": 150}, {"epoch": 160.0, "grad_norm": 0.0, "learning_rate": 0.00019999507890797408, "loss": 3.5297, "step": 160}, {"epoch": 170.0, "grad_norm": 0.0, "learning_rate": 0.00019997806834748456, "loss": 3.5297, "step": 170}, {"epoch": 180.0, "grad_norm": 0.0, "learning_rate": 0.00019994890963073947, "loss": 3.5297, "step": 180}, {"epoch": 190.0, "grad_norm": 0.0, "learning_rate": 0.00019990760630076237, "loss": 3.5297, "step": 190}, {"epoch": 200.0, "grad_norm": 0.0, "learning_rate": 0.000199854163376247, "loss": 3.5297, "step": 200}, {"epoch": 210.0, "grad_norm": 0.0, "learning_rate": 0.00019978858735094753, "loss": 3.5297, "step": 210}, {"epoch": 220.0, "grad_norm": 0.0, "learning_rate": 0.0001997108861928895, "loss": 3.5297, "step": 220}, {"epoch": 230.0, "grad_norm": 0.0, "learning_rate": 0.0001996210693434016, "loss": 3.5297, "step": 230}, {"epoch": 240.0, "grad_norm": 0.0, "learning_rate": 0.0001995191477159686, "loss": 3.5297, "step": 240}, {"epoch": 250.0, "grad_norm": 0.0, "learning_rate": 0.00019940513369490516, "loss": 3.5297, "step": 250}, {"epoch": 260.0, "grad_norm": 0.0, "learning_rate": 0.00019927904113385098, "loss": 3.5297, "step": 260}, {"epoch": 270.0, "grad_norm": 0.0, "learning_rate": 0.00019914088535408767, "loss": 3.5297, "step": 270}, {"epoch": 280.0, "grad_norm": 0.0, "learning_rate": 0.00019899068314267688, "loss": 3.5297, "step": 280}, {"epoch": 290.0, "grad_norm": 0.0, "learning_rate": 0.0001988284527504207, "loss": 3.5297, "step": 290}, {"epoch": 300.0, "grad_norm": 0.0, "learning_rate": 0.00019865421388964383, "loss": 3.5297, "step": 300}, {"epoch": 310.0, "grad_norm": 0.0, "learning_rate": 0.00019846798773179866, "loss": 3.5297, "step": 310}, {"epoch": 320.0, "grad_norm": 0.0, "learning_rate": 0.00019826979690489252, "loss": 3.5297, "step": 320}, {"epoch": 330.0, "grad_norm": 0.0, "learning_rate": 0.00019805966549073825, "loss": 3.5297, "step": 330}, {"epoch": 340.0, "grad_norm": 0.0, "learning_rate": 0.00019783761902202813, "loss": 3.5297, "step": 340}, {"epoch": 350.0, "grad_norm": 0.0, "learning_rate": 0.00019760368447923146, "loss": 3.5297, "step": 350}, {"epoch": 360.0, "grad_norm": 0.0, "learning_rate": 0.00019735789028731604, "loss": 3.5297, "step": 360}, {"epoch": 370.0, "grad_norm": 0.0, "learning_rate": 0.0001971002663122945, "loss": 3.5297, "step": 370}, {"epoch": 380.0, "grad_norm": 0.0, "learning_rate": 0.00019683084385759523, "loss": 3.5297, "step": 380}, {"epoch": 390.0, "grad_norm": 0.0, "learning_rate": 0.0001965496556602588, "loss": 3.5297, "step": 390}, {"epoch": 400.0, "grad_norm": 0.0, "learning_rate": 0.00019625673588696008, "loss": 3.5297, "step": 400}, {"epoch": 410.0, "grad_norm": 0.0, "learning_rate": 0.00019595212012985682, "loss": 3.5297, "step": 410}, {"epoch": 420.0, "grad_norm": 0.0, "learning_rate": 0.00019563584540226481, "loss": 3.5297, "step": 420}, {"epoch": 430.0, "grad_norm": 0.0, "learning_rate": 0.00019530795013416046, "loss": 3.5297, "step": 430}, {"epoch": 440.0, "grad_norm": 0.0, "learning_rate": 0.00019496847416751125, "loss": 3.5297, "step": 440}, {"epoch": 450.0, "grad_norm": 0.0, "learning_rate": 0.00019461745875143477, "loss": 3.5297, "step": 450}, {"epoch": 460.0, "grad_norm": 0.0, "learning_rate": 0.0001942549465371863, "loss": 3.5297, "step": 460}, {"epoch": 470.0, "grad_norm": 0.0, "learning_rate": 0.0001938809815729766, "loss": 3.5297, "step": 470}, {"epoch": 480.0, "grad_norm": 0.0, "learning_rate": 0.00019349560929861958, "loss": 3.5297, "step": 480}, {"epoch": 490.0, "grad_norm": 0.0, "learning_rate": 0.00019309887654001096, "loss": 3.5297, "step": 490}, {"epoch": 500.0, "grad_norm": 0.0, "learning_rate": 0.00019269083150343859, "loss": 3.5297, "step": 500}, {"epoch": 510.0, "grad_norm": 0.0, "learning_rate": 0.00019227152376972506, "loss": 3.5297, "step": 510}, {"epoch": 520.0, "grad_norm": 0.0, "learning_rate": 0.000191841004288203, "loss": 3.5297, "step": 520}, {"epoch": 530.0, "grad_norm": 0.0, "learning_rate": 0.00019139932537052463, "loss": 3.5297, "step": 530}, {"epoch": 540.0, "grad_norm": 0.0, "learning_rate": 0.0001909465406843052, "loss": 3.5297, "step": 540}, {"epoch": 550.0, "grad_norm": 0.0, "learning_rate": 0.00019048270524660196, "loss": 3.5297, "step": 550}, {"epoch": 560.0, "grad_norm": 0.0, "learning_rate": 0.0001900078754172294, "loss": 3.5297, "step": 560}, {"epoch": 570.0, "grad_norm": 0.0, "learning_rate": 0.00018952210889191067, "loss": 3.5297, "step": 570}, {"epoch": 580.0, "grad_norm": 0.0, "learning_rate": 0.00018902546469526743, "loss": 3.5297, "step": 580}, {"epoch": 590.0, "grad_norm": 0.0, "learning_rate": 0.0001885180031736477, "loss": 3.5297, "step": 590}, {"epoch": 600.0, "grad_norm": 0.0, "learning_rate": 0.00018799978598779322, "loss": 3.5297, "step": 600}, {"epoch": 610.0, "grad_norm": 0.0, "learning_rate": 0.00018747087610534736, "loss": 3.5297, "step": 610}, {"epoch": 620.0, "grad_norm": 0.0, "learning_rate": 0.00018693133779320385, "loss": 3.5297, "step": 620}, {"epoch": 630.0, "grad_norm": 0.0, "learning_rate": 0.00018638123660969796, "loss": 3.5297, "step": 630}, {"epoch": 640.0, "grad_norm": 0.0, "learning_rate": 0.0001858206393966405, "loss": 3.5297, "step": 640}, {"epoch": 650.0, "grad_norm": 0.0, "learning_rate": 0.00018524961427119615, "loss": 3.5297, "step": 650}, {"epoch": 660.0, "grad_norm": 0.0, "learning_rate": 0.00018466823061760653, "loss": 3.5297, "step": 660}, {"epoch": 670.0, "grad_norm": 0.0, "learning_rate": 0.0001840765590787594, "loss": 3.5297, "step": 670}, {"epoch": 680.0, "grad_norm": 0.0, "learning_rate": 0.00018347467154760516, "loss": 3.5297, "step": 680}, {"epoch": 690.0, "grad_norm": 0.0, "learning_rate": 0.00018286264115842117, "loss": 3.5297, "step": 690}, {"epoch": 700.0, "grad_norm": 0.0, "learning_rate": 0.00018224054227792524, "loss": 3.5297, "step": 700}, {"epoch": 710.0, "grad_norm": 0.0, "learning_rate": 0.00018160845049623964, "loss": 3.5297, "step": 710}, {"epoch": 720.0, "grad_norm": 0.0, "learning_rate": 0.0001809664426177061, "loss": 3.5297, "step": 720}, {"epoch": 730.0, "grad_norm": 0.0, "learning_rate": 0.00018031459665155363, "loss": 3.5297, "step": 730}, {"epoch": 740.0, "grad_norm": 0.0, "learning_rate": 0.00017965299180241963, "loss": 3.5297, "step": 740}, {"epoch": 750.0, "grad_norm": 0.0, "learning_rate": 0.00017898170846072592, "loss": 3.5297, "step": 750}, {"epoch": 760.0, "grad_norm": 0.0, "learning_rate": 0.0001783008281929106, "loss": 3.5297, "step": 760}, {"epoch": 770.0, "grad_norm": 0.0, "learning_rate": 0.00017761043373151715, "loss": 3.5297, "step": 770}, {"epoch": 780.0, "grad_norm": 0.0, "learning_rate": 0.0001769106089651417, "loss": 3.5297, "step": 780}, {"epoch": 790.0, "grad_norm": 0.0, "learning_rate": 0.00017620143892823977, "loss": 3.5297, "step": 790}, {"epoch": 800.0, "grad_norm": 0.0, "learning_rate": 0.00017548300979079414, "loss": 3.5297, "step": 800}, {"epoch": 810.0, "grad_norm": 0.0, "learning_rate": 0.00017475540884784424, "loss": 3.5297, "step": 810}, {"epoch": 820.0, "grad_norm": 0.0, "learning_rate": 0.00017401872450887917, "loss": 3.5297, "step": 820}, {"epoch": 830.0, "grad_norm": 0.0, "learning_rate": 0.0001732730462870953, "loss": 3.5297, "step": 830}, {"epoch": 840.0, "grad_norm": 0.0, "learning_rate": 0.00017251846478851955, "loss": 3.5297, "step": 840}, {"epoch": 850.0, "grad_norm": 0.0, "learning_rate": 0.0001717550717010001, "loss": 3.5297, "step": 850}, {"epoch": 860.0, "grad_norm": 0.0, "learning_rate": 0.00017098295978306552, "loss": 3.5297, "step": 860}, {"epoch": 870.0, "grad_norm": 0.0, "learning_rate": 0.00017020222285265397, "loss": 3.5297, "step": 870}, {"epoch": 880.0, "grad_norm": 0.0, "learning_rate": 0.0001694129557757133, "loss": 3.5297, "step": 880}, {"epoch": 890.0, "grad_norm": 0.0, "learning_rate": 0.0001686152544546743, "loss": 3.5297, "step": 890}, {"epoch": 900.0, "grad_norm": 0.0, "learning_rate": 0.00016780921581679764, "loss": 3.5297, "step": 900}], "logging_steps": 10, "max_steps": 3000, "num_input_tokens_seen": 0, "num_train_epochs": 3000, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 5188639887360000.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}