"""
RAG-Enhanced MCQ Generator

This module combines Retrieval-Augmented Generation (RAG) with MCQ generation
to create highly accurate and contextually relevant questions based on the
knowledge base content.
"""

# CRITICAL: Apply comprehensive numpy pydantic fix BEFORE any imports
import logging

# Set up logger first
logger = logging.getLogger(__name__)

# ULTIMATE SOLUTION: Apply comprehensive numpy fix
try:
    # Import and apply the comprehensive fix immediately
    from .numpy_pydantic_fix import apply_comprehensive_numpy_fix, suppress_numpy_warnings

    # Suppress warnings first
    suppress_numpy_warnings()

    # Apply the comprehensive fix
    fix_applied = apply_comprehensive_numpy_fix()

    if fix_applied:
        logger.debug("✅ Ultimate numpy pydantic fix applied in RAG MCQ generator")
    else:
        logger.debug("⚠️ Ultimate numpy pydantic fix could not be applied")

except Exception as e:
    logger.warning(f"⚠️ Could not apply ultimate numpy fix: {e}")

# ADDITIONAL: Configure pydantic for numpy arrays
try:
    from .pydantic_config import configure_pydantic_for_dataframes
    config_result = configure_pydantic_for_dataframes()
    logger.debug(f"✅ Additional pydantic configuration: {config_result}")
except Exception as e:
    logger.debug(f"⚠️ Additional pydantic configuration failed: {e}")

# Import other modules after comprehensive fix
import asyncio
from typing import Dict, Any, List, Optional, Tuple
import re
import json

logger.debug("✅ RAG MCQ generator imports completed with comprehensive numpy fix")

class RAGMCQGenerator:
    """Generate MCQs using RAG for enhanced accuracy and relevance"""
    
    def __init__(self):
        self.rag_engine = None
        self.is_initialized = False
        self.fallback_generator = None
        
    def initialize(self) -> bool:
        """Initialize the RAG MCQ generator with proper error handling"""
        try:
            logger.info("🔄 Initializing RAG MCQ generator...")

            # Try to initialize RAG engine with comprehensive error handling
            try:
                from ..rag_engine import RAGEngine
                self.rag_engine = RAGEngine()

                # CRITICAL FIX: Test if RAG engine is actually functional with proper None checks
                if self.rag_engine is not None:
                    if hasattr(self.rag_engine, 'ask') and callable(getattr(self.rag_engine, 'ask', None)):
                        logger.info("✅ RAG engine initialized successfully")
                    else:
                        logger.warning("⚠️ RAG engine missing required methods")
                        self.rag_engine = None
                else:
                    logger.warning("⚠️ RAG engine is None after initialization attempt")
                    self.rag_engine = None

            except ImportError as e:
                logger.warning(f"⚠️ RAG engine module not available: {e}")
                logger.info("💡 Install haystack-ai for RAG functionality: pip install haystack-ai")
                self.rag_engine = None
            except Exception as e:
                logger.warning(f"⚠️ RAG engine initialization failed: {e}")
                if "haystack" in str(e).lower():
                    logger.info("💡 Install haystack-ai for RAG functionality: pip install haystack-ai")
                elif "pipeline" in str(e).lower():
                    logger.info("💡 RAG pipeline initialization failed - check Haystack configuration")
                self.rag_engine = None

            # Initialize fallback generator (lazy loading - no model loading during startup)
            try:
                from .offline_mcq_generator import OfflineMCQGenerator
                self.fallback_generator = OfflineMCQGenerator()
                # Don't initialize during startup - only when needed
                logger.info("✅ Fallback MCQ generator ready for lazy initialization")
            except ImportError as e:
                logger.warning(f"⚠️ Fallback generator module not available: {e}")
                self.fallback_generator = None
            except Exception as e:
                logger.warning(f"⚠️ Fallback generator initialization failed: {e}")
                self.fallback_generator = None

            # Mark as initialized even if some components failed
            # This allows graceful degradation
            self.is_initialized = True
            return True

        except Exception as e:
            logger.error(f"❌ RAG MCQ generator initialization failed: {e}")
            # Still mark as initialized to prevent repeated initialization attempts
            self.is_initialized = True
            return False
    
    async def generate_quiz_async(self, context: str, topic: str = None, difficulty: str = "medium",
                                 cognitive_level: str = "understanding", use_rag: bool = True) -> Dict[str, Any]:
        """
        Generate MCQ using RAG-enhanced approach

        Args:
            context: The input context/question topic
            topic: The topic name (optional, for compatibility)
            difficulty: Question difficulty level
            cognitive_level: Cognitive level for question complexity
            use_rag: Whether to use RAG retrieval

        Returns:
            Dictionary containing question, options, correct answer, and explanation
        """
        try:
            if use_rag and self.rag_engine:
                return await self._generate_rag_mcq(context, difficulty)
            else:
                return await self._generate_fallback_mcq(context, difficulty)
                
        except Exception as e:
            logger.error(f"❌ RAG MCQ generation failed: {e}")
            return await self._generate_fallback_mcq(context, difficulty)
    
    async def _generate_rag_mcq(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using RAG retrieval with comprehensive error handling"""
        logger.info("🔍 Generating RAG-enhanced MCQ...")

        # CRITICAL FIX: Validate RAG engine availability with explicit None check
        if self.rag_engine is None:
            logger.warning("⚠️ RAG engine not available (is None), falling back to basic generation")
            return await self._generate_fallback_mcq(context, difficulty)

        # Additional validation for RAG engine functionality
        if not hasattr(self.rag_engine, 'ask') or not callable(getattr(self.rag_engine, 'ask', None)):
            logger.warning("⚠️ RAG engine missing 'ask' method, falling back to basic generation")
            return await self._generate_fallback_mcq(context, difficulty)

        # Step 1: Extract key concepts from context
        key_concepts = self._extract_key_concepts(context)
        logger.info(f"📝 Extracted key concepts: {key_concepts[:3]}...")

        # Step 2: Retrieve relevant passages with proper error handling
        relevant_passages = []
        for concept in key_concepts[:5]:  # Use top 5 concepts
            try:
                # Use the new retrieve_context method instead of deprecated ask method
                if hasattr(self.rag_engine, 'retrieve_context') and callable(self.rag_engine.retrieve_context):
                    context_chunks = self.rag_engine.retrieve_context(concept, top_k=3)
                    if context_chunks and isinstance(context_chunks, list):
                        # Convert to legacy format for compatibility
                        for chunk in context_chunks:
                            relevant_passages.append({
                                'answer': chunk,
                                'context': chunk,
                                'score': 0.9,  # High confidence score
                                'meta': {'source': 'rag_retrieval'}
                            })
                else:
                    logger.warning(f"⚠️ RAG engine retrieve_context method not available")
                    break
            except Exception as e:
                logger.warning(f"⚠️ RAG retrieval failed for concept '{concept}': {e}")
        
        if not relevant_passages:
            logger.warning("⚠️ No relevant passages found, using fallback")
            return await self._generate_fallback_mcq(context, difficulty)
        
        # Step 3: Select best passage
        best_passage = self._select_best_passage(relevant_passages, context)
        logger.info(f"🎯 Selected best passage: {best_passage['context'][:100]}...")
        
        # Step 4: Generate MCQ from retrieved content
        enhanced_context = self._create_enhanced_context(context, best_passage)
        
        # Step 5: Generate question using enhanced context
        if self.fallback_generator:
            result = await self.fallback_generator.generate_quiz_async(enhanced_context, difficulty)
            
            # Enhance the result with RAG metadata
            if result:
                result['rag_enhanced'] = True
                result['source_document'] = best_passage.get('meta', {}).get('pdf_file', 'Unknown')
                result['confidence_score'] = best_passage.get('score', 0.0)
                
            return result
        else:
            return self._generate_basic_rag_mcq(enhanced_context, best_passage, difficulty)
    
    def _extract_key_concepts(self, context: str) -> List[str]:
        """Extract key concepts from the input context"""
        # Simple keyword extraction (can be enhanced with NLP)
        words = re.findall(r'\b[A-Za-z]{3,}\b', context.lower())
        
        # Filter out common words
        stop_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 
            'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 
            'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'what', 'with',
            'about', 'after', 'again', 'before', 'being', 'between', 'both', 'each',
            'from', 'into', 'more', 'most', 'other', 'some', 'such', 'than', 'that',
            'their', 'them', 'these', 'they', 'this', 'through', 'time', 'very',
            'when', 'where', 'which', 'while', 'will', 'with', 'would', 'your'
        }
        
        key_concepts = [word for word in words if word not in stop_words and len(word) > 3]
        
        # Return unique concepts, prioritizing longer ones
        unique_concepts = list(dict.fromkeys(key_concepts))
        return sorted(unique_concepts, key=len, reverse=True)[:10]
    
    def _select_best_passage(self, passages: List[Dict], context: str) -> Dict:
        """Select the most relevant passage based on context similarity"""
        if not passages:
            return {}
        
        # Simple scoring based on keyword overlap and confidence
        best_passage = passages[0]
        best_score = 0
        
        context_words = set(context.lower().split())
        
        for passage in passages:
            # Combine RAG score with keyword overlap
            rag_score = passage.get('score', 0.0)
            passage_words = set(passage.get('context', '').lower().split())
            overlap = len(context_words.intersection(passage_words))
            
            combined_score = rag_score * 0.7 + (overlap / max(len(context_words), 1)) * 0.3
            
            if combined_score > best_score:
                best_score = combined_score
                best_passage = passage
        
        return best_passage
    
    def _create_enhanced_context(self, original_context: str, passage: Dict) -> str:
        """Create enhanced context by combining original context with retrieved passage"""
        retrieved_content = passage.get('context', '')
        
        enhanced_context = f"""
        Topic: {original_context}
        
        Relevant Information:
        {retrieved_content}
        
        Based on the above information, create a comprehensive question about the topic.
        """
        
        return enhanced_context.strip()
    
    def _generate_basic_rag_mcq(self, context: str, passage: Dict, difficulty: str) -> Dict[str, Any]:
        """Generate a basic MCQ when no fallback generator is available"""
        
        # Extract key information from the passage
        content = passage.get('context', context)
        words = content.split()
        
        # Create a simple question based on the content
        if len(words) > 20:
            # Find potential answer candidates
            important_words = [w for w in words if len(w) > 5 and w.isalpha()]
            
            if important_words:
                correct_answer = important_words[0].title()
                question = f"According to the retrieved information, what is mentioned regarding {correct_answer.lower()}?"
                
                # Generate plausible distractors
                other_words = important_words[1:4] if len(important_words) > 3 else ['Option B', 'Option C', 'Option D']
                
                options = {
                    "A": correct_answer,
                    "B": other_words[0].title() if len(other_words) > 0 else "Alternative concept",
                    "C": other_words[1].title() if len(other_words) > 1 else "Different approach", 
                    "D": other_words[2].title() if len(other_words) > 2 else "Other method"
                }
                
                return {
                    "question": question,
                    "options": options,
                    "correct": "A",
                    "explanation": f"Based on the retrieved information from the knowledge base, {correct_answer} is the correct answer.",
                    "rag_enhanced": True,
                    "source_document": passage.get('meta', {}).get('pdf_file', 'Knowledge Base'),
                    "confidence_score": passage.get('score', 0.5)
                }
        
        # Fallback to basic question
        return {
            "question": "Based on the retrieved information, what is the main concept discussed?",
            "options": {
                "A": "Primary concept from the knowledge base",
                "B": "Secondary information",
                "C": "Related but different topic",
                "D": "Unrelated concept"
            },
            "correct": "A",
            "explanation": "This question is based on information retrieved from the knowledge base.",
            "rag_enhanced": True,
            "source_document": passage.get('meta', {}).get('pdf_file', 'Knowledge Base'),
            "confidence_score": passage.get('score', 0.3)
        }
    
    async def _generate_fallback_mcq(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using fallback method when RAG is not available"""
        if self.fallback_generator:
            logger.info("🔄 Using fallback MCQ generator...")
            result = await self.fallback_generator.generate_quiz_async(context, difficulty)
            if result:
                result['rag_enhanced'] = False
            return result
        else:
            logger.warning("⚠️ No fallback generator available, using basic generation")
            return self._generate_basic_fallback(context)
    
    def _generate_basic_fallback(self, context: str) -> Dict[str, Any]:
        """
        Generate a fallback question using Three-Layered Defense principles.
        NO MORE META-LEVEL QUESTIONS!
        """
        logger.warning(f"🛡️ RAG Basic Fallback - Applying Three-Layered Defense for context: {context[:50]}...")

        # Extract topic from context
        topic = context.split()[0] if context.split() else "the subject"

        # Apply Layer 2: Use curated content approach
        curated_content = self._get_curated_context(topic)

        if curated_content:
            # Create a content-based question instead of meta-level
            return {
                "question": f"Based on the fundamental principles, which characteristic is most important for understanding {topic}?",
                "options": {
                    "A": "The underlying mechanisms and their interactions",
                    "B": "Only the historical development timeline",
                    "C": "Isolated facts without connections",
                    "D": "Superficial observations without depth"
                },
                "correct": "A",
                "explanation": f"Understanding {topic} requires grasping the underlying mechanisms and how they interact, which is fundamental to the subject.",
                "rag_enhanced": False,
                "generation_method": "three_layer_defense_fallback"
            }
        else:
            # Final emergency fallback - still avoid meta-level questions
            return {
                "question": f"When studying {topic}, which approach leads to the deepest understanding?",
                "options": {
                    "A": "Examining the fundamental processes and relationships",
                    "B": "Memorizing definitions without context",
                    "C": "Focusing only on applications",
                    "D": "Avoiding theoretical foundations"
                },
                "correct": "A",
                "explanation": f"Deep understanding of {topic} comes from examining fundamental processes and their relationships.",
                "rag_enhanced": False,
                "generation_method": "emergency_three_layer_defense"
            }
    
    def generate_multiple_questions(self, context: str, num_questions: int = 5, 
                                  difficulty: str = "medium") -> List[Dict[str, Any]]:
        """Generate multiple RAG-enhanced MCQs"""
        questions = []
        
        try:
            # Use asyncio to generate questions
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                for i in range(num_questions):
                    # Vary the context slightly for each question
                    varied_context = f"{context} (Question {i+1})"
                    question = loop.run_until_complete(
                        self.generate_quiz_async(varied_context, difficulty)
                    )
                    if question:
                        questions.append(question)
                        logger.info(f"Generated RAG question {i+1}/{num_questions}")
                        
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"❌ Multiple RAG question generation failed: {e}")
            
        return questions
    
    def _get_curated_context(self, topic: str) -> str:
        """
        LAYER 2: Provides high-quality, pre-written context for common topics
        when the RAG engine fails to find user-specific content. This is our
        intelligent safety net.
        """
        topic_lower = topic.lower()

        # High-quality curated contexts
        curated_contexts = {
            "magnetism": """
Magnetism is a physical phenomenon produced by the motion of electric charge, resulting in attractive and repulsive forces between objects. The magnetic field, an invisible field that exerts magnetic force, is created by moving electric charges. The Lorentz force equation, F = q(E + v × B), describes the force on a point charge due to electromagnetic fields. A key aspect of magnetism is the existence of magnetic dipoles, or north and south poles, where field lines emerge from the north and enter the south.
            """,
            "physics": """
Classical mechanics, a major branch of physics, is governed by Newton's three laws of motion. The first law, the law of inertia, states an object remains at rest or in uniform motion unless acted upon by a net external force. The second law quantifies force as mass times acceleration (F=ma). The third law states that for every action, there is an equal and opposite reaction. These principles are fundamental to understanding the motion of macroscopic objects.
            """,
            "chemistry": """
Chemical bonding involves the forces that hold atoms together in molecules and compounds. The primary types are ionic bonds, formed by the electrostatic attraction between oppositely charged ions, and covalent bonds, where atoms share one or more pairs of electrons. The electronegativity difference between atoms determines the type of bond formed. Covalent bonds can be polar or nonpolar depending on the equality of electron sharing.
            """,
            "programming": """
Programming involves creating step-by-step instructions for computers to execute tasks. Modern programming languages like Python, Java, and C++ provide abstractions that hide complex machine-level operations. Object-oriented programming organizes code into classes and objects, promoting code reuse, modularity, and maintainability. Encapsulation hides internal implementation details, inheritance allows code reuse through class hierarchies, and polymorphism enables objects to be treated uniformly.
            """,
            "biology": """
Cellular biology studies the fundamental unit of life - the cell. All living organisms are composed of one or more cells, which contain genetic material (DNA) that controls cellular functions. Cellular respiration converts glucose and oxygen into ATP, the energy currency of cells. Photosynthesis in plants converts light energy into chemical energy, producing glucose and oxygen. Cell division through mitosis ensures growth and repair in multicellular organisms.
            """,
            "mathematics": """
Mathematics is the study of patterns, structures, and relationships using numbers, symbols, and logical reasoning. Algebra involves solving equations and working with variables to represent unknown quantities. Calculus studies rates of change (derivatives) and accumulation (integrals). Geometry examines shapes, sizes, and spatial relationships. Mathematical proofs provide logical arguments to establish the truth of mathematical statements.
            """
        }

        # Find the best match
        for key, content in curated_contexts.items():
            if key in topic_lower:
                return content.strip()

        # If no specific match, return an empty string to trigger the emergency fallback
        return ""

    def _get_context_for_topic(self, topic: str) -> str:
        """
        Get context for a topic using RAG engine or curated fallback.
        This method is called by MCQManager for content retrieval.
        """
        try:
            # Try RAG engine first if available
            if self.rag_engine and hasattr(self.rag_engine, 'ask'):
                try:
                    passages = self.rag_engine.ask(topic, top_k_retriever=3, top_k_reader=1)
                    if passages and isinstance(passages, list) and len(passages) > 0:
                        # Extract context from the best passage
                        best_passage = passages[0]
                        context = best_passage.get('context', '')
                        if context and len(context.strip()) >= 200:
                            return context
                except Exception as e:
                    logger.warning(f"⚠️ RAG engine query failed for topic '{topic}': {e}")

            # Fallback to curated context
            curated = self._get_curated_context(topic)
            if curated:
                return curated

            # Final fallback - return empty to trigger emergency fallback
            return ""

        except Exception as e:
            logger.error(f"❌ Context retrieval failed for topic '{topic}': {e}")
            return ""

    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base"""
        if not self.rag_engine:
            return {"status": "RAG engine not available"}

        try:
            # This would need to be implemented in the RAG engine
            return {
                "status": "available",
                "documents_indexed": "Unknown",  # Would need RAG engine method
                "last_updated": "Unknown"
            }
        except Exception as e:
            return {"status": "error", "message": str(e)}
