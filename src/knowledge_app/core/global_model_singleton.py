"""
🔥 FIRE METHOD 3: Global Model Singleton - Pre-warm and Pin Strategy

This is the nuclear brute force solution:
- Load model ONCE at application startup
- Keep it pinned in GPU memory FOREVER
- No loading/unloading during runtime
- No race conditions possible
- No deadlocks possible
- No memory corruption possible

Trade-offs:
- Slower startup (30-60 seconds)
- Higher memory usage (always 4GB+ GPU)
- But: ZER<PERSON> crashes, ZERO race conditions, ZERO complexity

This is the "pay once, use forever" approach.
"""

import threading
import time
import logging
import gc
from typing import Optional, Dict, Any
import torch

logger = logging.getLogger(__name__)

class GlobalModelSingleton:
    """
    Global singleton that owns the model forever.
    Load once, use forever, never unload.
    """
    
    _instance: Optional['GlobalModelSingleton'] = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
                
            # Model storage
            self.model = None
            self.tokenizer = None
            self.model_id = None
            self.device = None
            self.is_loaded = False
            self.load_time = None
            self.generation_count = 0
            
            # Thread safety
            self.generation_lock = threading.RLock()
            
            # Memory tracking
            self.initial_gpu_memory = 0
            self.model_gpu_memory = 0
            
            self._initialized = True
            logger.info("🏗️ GlobalModelSingleton initialized (not loaded yet)")
    
    def load_model_once(self, model_id: str = "mistralai/Mistral-7B-Instruct-v0.2") -> bool:
        """
        Load the model ONCE and keep it forever.
        This should be called at application startup.
        """
        with self._lock:
            if self.is_loaded:
                logger.info(f"✅ Model already loaded: {self.model_id}")
                return True
            
            logger.info("🔥 FIRE METHOD 3: Loading model ONCE and pinning forever")
            logger.info(f"🚀 Loading model: {model_id}")
            
            start_time = time.time()
            
            try:
                from transformers import AutoTokenizer, AutoModelForCausalLM
                
                # Clear any existing CUDA cache
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    self.initial_gpu_memory = torch.cuda.memory_allocated()
                    logger.info(f"Initial GPU memory: {self.initial_gpu_memory // 1024**2}MB")
                    self.device = "cuda"
                else:
                    self.device = "cpu"
                    logger.warning("CUDA not available, using CPU")
                
                # Load tokenizer
                logger.info("📝 Loading tokenizer...")
                self.tokenizer = AutoTokenizer.from_pretrained(model_id)
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
                logger.info("✅ Tokenizer loaded")
                
                # Load model with optimized settings for RTX 3060 12GB
                logger.info("🧠 Loading model with RTX 3060 optimizations...")
                
                if "7B" in model_id or "7b" in model_id:
                    # THE ROYAL DECREE: 7B model strategy with Swift Blade of Attention
                    logger.info("👑 THE ROYAL DECREE: Using 7B model optimizations with Swift Blade of Attention")

                    # Import attention optimizer
                    from .attention_optimizer import attention_optimizer

                    # Base model kwargs for 7B models on RTX 3060 12GB
                    model_kwargs = {
                        "torch_dtype": torch.bfloat16,  # Use bfloat16 for better performance
                        "device_map": "auto",
                        "load_in_4bit": True,  # 4-bit quantization for memory efficiency
                        "trust_remote_code": True,
                        "low_cpu_mem_usage": True,
                        # RTX 3060 specific optimizations
                        "max_memory": {0: "10GB"},  # Leave 2GB buffer
                        "offload_folder": "./offload_cache"  # Offload if needed
                    }

                    # Apply attention optimization
                    attention_kwargs = attention_optimizer.get_model_kwargs(model_id)
                    model_kwargs.update(attention_kwargs)

                    attn_impl = model_kwargs.get("attn_implementation", "eager")
                    logger.info(f"🗡️ Loading 7B model with {attn_impl} attention")

                    self.model = AutoModelForCausalLM.from_pretrained(
                        model_id,
                        **model_kwargs
                    )
                else:
                    # THE ROYAL DECREE: Smaller model strategy with Swift Blade of Attention
                    logger.info("👑 THE ROYAL DECREE: Using smaller model optimizations with Swift Blade of Attention")

                    # Base model kwargs for smaller models
                    model_kwargs = {
                        "torch_dtype": torch.float16,
                        "device_map": "auto",
                        "trust_remote_code": True,
                        "low_cpu_mem_usage": True
                    }

                    # Apply attention optimization
                    attention_kwargs = attention_optimizer.get_model_kwargs(model_id)
                    model_kwargs.update(attention_kwargs)

                    attn_impl = model_kwargs.get("attn_implementation", "eager")
                    logger.info(f"🗡️ Loading smaller model with {attn_impl} attention")

                    self.model = AutoModelForCausalLM.from_pretrained(
                        model_id,
                        **model_kwargs
                    )
                
                # Store model info
                self.model_id = model_id
                self.load_time = time.time() - start_time
                
                # Check final memory usage
                if torch.cuda.is_available():
                    final_memory = torch.cuda.memory_allocated()
                    self.model_gpu_memory = final_memory - self.initial_gpu_memory
                    logger.info(f"Final GPU memory: {final_memory // 1024**2}MB")
                    logger.info(f"Model memory usage: {self.model_gpu_memory // 1024**2}MB")
                
                # Verify model device
                if hasattr(self.model, 'device'):
                    logger.info(f"Model device: {self.model.device}")
                
                self.is_loaded = True
                
                logger.info("🎉 MODEL LOADED AND PINNED FOREVER!")
                logger.info(f"⏱️ Load time: {self.load_time:.1f} seconds")
                logger.info(f"💾 GPU memory used: {self.model_gpu_memory // 1024**2}MB")
                logger.info("🔒 Model will NEVER be unloaded during runtime")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Failed to load model: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                self.is_loaded = False
                return False
    
    def generate_text(self, prompt: str, max_tokens: int = 150, temperature: float = 0.7) -> Dict[str, Any]:
        """
        Generate text using the pinned model.
        This is thread-safe and can be called from anywhere.
        """
        if not self.is_loaded:
            return {
                "success": False,
                "error": "Model not loaded. Call load_model_once() first.",
                "result": None
            }
        
        with self.generation_lock:
            try:
                logger.info(f"🎯 Generating text (request #{self.generation_count + 1})")
                logger.debug(f"Prompt: {prompt[:50]}...")
                
                start_time = time.time()
                
                # Tokenize input
                inputs = self.tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
                
                # Move to model device
                device = next(self.model.parameters()).device
                inputs = {k: v.to(device) for k, v in inputs.items()}
                
                # Generate with no_grad for memory efficiency
                with torch.no_grad():
                    outputs = self.model.generate(
                        **inputs,
                        max_new_tokens=max_tokens,
                        temperature=temperature,
                        do_sample=True,
                        pad_token_id=self.tokenizer.eos_token_id,
                        eos_token_id=self.tokenizer.eos_token_id,
                        use_cache=True
                    )
                
                # Decode result
                result = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
                
                generation_time = time.time() - start_time
                self.generation_count += 1
                
                logger.info(f"✅ Generation successful in {generation_time:.1f}s")
                logger.debug(f"Result length: {len(result)} characters")
                
                return {
                    "success": True,
                    "result": result,
                    "prompt": prompt,
                    "length": len(result),
                    "generation_time": generation_time,
                    "generation_count": self.generation_count
                }
                
            except Exception as e:
                logger.error(f"❌ Generation failed: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                
                return {
                    "success": False,
                    "error": str(e),
                    "result": None
                }
    
    def get_status(self) -> Dict[str, Any]:
        """Get model status"""
        return {
            "is_loaded": self.is_loaded,
            "model_id": self.model_id,
            "device": self.device,
            "load_time": self.load_time,
            "generation_count": self.generation_count,
            "gpu_memory_mb": self.model_gpu_memory // 1024**2 if self.model_gpu_memory else 0,
            "uptime": time.time() - (time.time() - self.load_time) if self.load_time else 0
        }
    
    def force_cleanup(self):
        """
        EMERGENCY ONLY: Force cleanup of the model.
        This should NEVER be called during normal operation.
        Only use this for application shutdown.
        """
        logger.warning("🚨 EMERGENCY: Force cleanup requested")
        
        with self._lock:
            try:
                if self.model is not None:
                    del self.model
                    logger.info("Model deleted")
                
                if self.tokenizer is not None:
                    del self.tokenizer
                    logger.info("Tokenizer deleted")
                
                # Force garbage collection
                collected = gc.collect()
                logger.info(f"Garbage collection freed {collected} objects")
                
                # Clear CUDA cache
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    final_memory = torch.cuda.memory_allocated()
                    logger.info(f"Final GPU memory after cleanup: {final_memory // 1024**2}MB")
                
                # Reset state
                self.model = None
                self.tokenizer = None
                self.model_id = None
                self.is_loaded = False
                self.generation_count = 0
                
                logger.warning("🚨 EMERGENCY cleanup completed")
                
            except Exception as e:
                logger.error(f"❌ Error during emergency cleanup: {e}")

# Global instance
_global_model: Optional[GlobalModelSingleton] = None

def get_global_model() -> GlobalModelSingleton:
    """Get the global model singleton"""
    global _global_model
    if _global_model is None:
        _global_model = GlobalModelSingleton()
    return _global_model

def load_global_model(model_id: str = "mistralai/Mistral-7B-Instruct-v0.2") -> bool:
    """Load the global model once at application startup"""
    model = get_global_model()
    return model.load_model_once(model_id)

def generate_mcq_global(prompt: str, max_tokens: int = 150) -> Dict[str, Any]:
    """
    Generate MCQ using the global pinned model.
    This is the main function your UI should call.
    """
    model = get_global_model()
    return model.generate_text(prompt, max_tokens)

def is_global_model_ready() -> bool:
    """Check if the global model is ready"""
    model = get_global_model()
    return model.is_loaded

def get_global_model_status() -> Dict[str, Any]:
    """Get global model status"""
    model = get_global_model()
    return model.get_status()

def shutdown_global_model():
    """Shutdown the global model (emergency only)"""
    model = get_global_model()
    model.force_cleanup()
