import os
import sys
import subprocess
import platform
import logging
import json
from pathlib import Path
from PyQt5.QtCore import QObject, pyqtSignal

class AutoTrainingManager(QObject):
    # Signals for progress updates
    setup_progress = pyqtSignal(dict)  # Detailed progress information
    training_progress = pyqtSignal(dict)   # training metrics
    setup_complete = pyqtSignal(bool)      # success status
    error_occurred = pyqtSignal(str)       # error message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.setup_complete_flag = False
        self.cuda_available = False
        self.system_info = {}
        self.current_phase = ""
        self.training_start_time = None
        self.training_presets = {
            "Quick Training": {
                "max_steps": 1000,
                "batch_size": 4,
                "learning_rate": 1e-4,
                "gradient_accumulation_steps": 4,
                "warmup_steps": 50,
                "save_steps": 200,
                "logging_steps": 50
            },
            "Standard Training": {
                "max_steps": 2000,
                "batch_size": 8,
                "learning_rate": 2e-4,
                "gradient_accumulation_steps": 8,
                "warmup_steps": 100,
                "save_steps": 250,
                "logging_steps": 100
            },
            "High Accuracy Training": {
                "max_steps": 3000,
                "batch_size": 16,
                "learning_rate": 2e-4,
                "gradient_accumulation_steps": 16,
                "warmup_steps": 200,
                "save_steps": 300,
                "logging_steps": 150
            }
        }
        self._initialize_logging()

    def _initialize_logging(self):
        """Initialize logging configuration."""
        log_file = Path("logs/auto_training.log")
        log_file.parent.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.DEBUG)

    def check_system_requirements(self):
        """Check system requirements for AI training."""
        try:
            self.setup_progress.emit({"status": "Checking system requirements...", "value": 10})
            
            # Get system information
            self.system_info = {
                'os': platform.system(),
                'python_version': sys.version,
                'cpu_count': os.cpu_count(),
                'memory_gb': self._get_system_memory(),
                'cuda_available': self._check_cuda_availability()
            }
            
            # Check minimum requirements
            requirements_met = (
                self.system_info['cpu_count'] >= 4 and
                self.system_info['memory_gb'] >= 8
            )
            
            if not requirements_met:
                self.error_occurred.emit("System does not meet minimum requirements")
                return False
                
            self.setup_progress.emit({"status": "System requirements check passed", "value": 20})
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking system requirements: {str(e)}")
            self.error_occurred.emit(f"Failed to check system requirements: {str(e)}")
            return False

    def setup_training_environment(self):
        """Set up the training environment including dependencies."""
        try:
            self.setup_progress.emit({"status": "Setting up training environment...", "value": 30})
            
            # Create virtual environment if needed
            if not self._ensure_virtual_environment():
                return False
                
            # Install dependencies
            if not self._install_dependencies():
                return False
                
            # Configure GPU settings if available
            if self.system_info.get('cuda_available'):
                if not self._configure_gpu_settings():
                    return False
                    
            self.setup_progress.emit({"status": "Training environment setup complete", "value": 50})
            self.setup_complete_flag = True
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting up training environment: {str(e)}")
            self.error_occurred.emit(f"Failed to set up training environment: {str(e)}")
            return False

    def _emit_progress(self, status, value, phase_progress=None, metrics=None, log_message=None):
        """Helper method to emit progress updates with all information."""
        progress_info = {
            'status': status,
            'value': value
        }
        
        if phase_progress is not None:
            progress_info['phase_progress'] = phase_progress
            
        if metrics:
            progress_info.update(metrics)
            
        if log_message:
            progress_info['log_message'] = log_message
            self.logger.info(log_message)
            
        if self.training_start_time and 'step' in progress_info and 'total_steps' in progress_info:
            import time
            elapsed_time = time.time() - self.training_start_time
            steps_remaining = progress_info['total_steps'] - progress_info['step']
            if progress_info['step'] > 0:
                time_per_step = elapsed_time / progress_info['step']
                eta_seconds = steps_remaining * time_per_step
                eta_hours = eta_seconds / 3600
                if eta_hours >= 1:
                    progress_info['eta'] = f"{eta_hours:.1f} hours"
                else:
                    progress_info['eta'] = f"{eta_seconds/60:.0f} minutes"
        
        self.setup_progress.emit(progress_info)

    def start_training(self, config):
        """Start the AI model training process with the selected preset configuration."""
        if not self.setup_complete_flag:
            self.error_occurred.emit("Training environment not set up. Please run setup first.")
            return False
            
        try:
            import time
            self.training_start_time = time.time()
            self._emit_progress("Starting training process...", 5, 0,
                              log_message="Initializing training environment...")
            
            # Validate hardware requirements
            if not self._validate_hardware_requirements(config["training_preset"]):
                return False
            
            # Get preset configuration
            training_config = self.training_presets[config["training_preset"]].copy()
            
            # Add additional configuration
            training_config.update({
                "base_model": config["base_model"],
                "train_from_books": config["train_from_books"],
                "domain_name": config["domain_name"]
            })
            
            # Prepare output directory
            base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            output_dir = os.path.join(base_path, "lora_adapters", config["domain_name"])
            os.makedirs(output_dir, exist_ok=True)
            training_config["output_dir"] = output_dir
            
            self._emit_progress("Starting training process...", 10, 0,
                              log_message=f"Configuration prepared. Using {config['training_preset']} preset.")
            
            # Start training process
            success = self._execute_training(training_config)
            
            if success:
                self._emit_progress("Training completed successfully!", 100, 100,
                                  log_message="Training process completed successfully!")
                return True
            else:
                self.error_occurred.emit("Training process failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during training: {str(e)}")
            self.error_occurred.emit(f"Training error: {str(e)}")
            return False

    def _validate_hardware_requirements(self, preset):
        """Validate if the system meets the hardware requirements for the selected preset."""
        system_memory = self._get_system_memory()
        cuda_available = self._check_cuda_availability()
        
        requirements = {
            "Quick Training": {
                "min_ram": 8,
                "gpu_required": False
            },
            "Standard Training": {
                "min_ram": 16,
                "gpu_required": True
            },
            "High Accuracy Training": {
                "min_ram": 32,
                "gpu_required": True
            }
        }
        
        req = requirements[preset]
        
        if system_memory < req["min_ram"]:
            self.error_occurred.emit(
                f"Insufficient RAM. {preset} requires at least {req['min_ram']}GB RAM. "
                f"Your system has {system_memory:.1f}GB."
            )
            return False
            
        if req["gpu_required"] and not cuda_available:
            self.error_occurred.emit(
                f"{preset} requires a CUDA-capable GPU. No GPU detected on your system."
            )
            return False
            
        return True

    def _get_system_memory(self):
        """Get system memory in GB."""
        try:
            import psutil
            return psutil.virtual_memory().total / (1024 * 1024 * 1024)  # Convert to GB
        except:
            return 0

    def _check_cuda_availability(self):
        """Check if CUDA is available."""
        try:
            import torch
            return torch.cuda.is_available()
        except:
            return False

    def _ensure_virtual_environment(self):
        """Ensure virtual environment exists and is activated."""
        try:
            venv_path = Path("myenv")
            if not venv_path.exists():
                self.setup_progress.emit({"status": "Creating virtual environment...", "value": 35})
                subprocess.run([sys.executable, "-m", "venv", "myenv"], check=True)
            
            return True
        except Exception as e:
            self.logger.error(f"Virtual environment setup failed: {str(e)}")
            return False

    def _install_dependencies(self):
        """Install required dependencies."""
        try:
            self.setup_progress.emit({"status": "Installing dependencies...", "value": 40})
            
            pip_cmd = [
                os.path.join("myenv", "Scripts" if os.name == "nt" else "bin", "pip"),
                "install",
                "-r",
                "requirements.txt"
            ]
            
            process = subprocess.run(pip_cmd, capture_output=True, text=True)
            if process.returncode != 0:
                self.logger.error(f"Dependency installation failed: {process.stderr}")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"Dependency installation failed: {str(e)}")
            return False

    def _configure_gpu_settings(self):
        """Configure GPU settings for optimal training."""
        try:
            import torch
            if not torch.cuda.is_available():
                return True  # Skip if no GPU
                
            # Set memory growth
            for device in range(torch.cuda.device_count()):
                torch.cuda.set_per_process_memory_fraction(0.9, device)
                
            return True
            
        except Exception as e:
            self.logger.error(f"GPU configuration failed: {str(e)}")
            return False

    def _execute_training(self, config):
        """Execute the training process with the given configuration."""
        try:
            self._emit_progress("Initializing training environment...", 10, 0,
                              log_message="Loading training modules...")
            
            # Import training modules
            from knowledge_app.core.training_controller import TrainingController
            from knowledge_app.core.training_service import TrainingService
            
            # Initialize training services
            training_service = TrainingService()
            training_controller = TrainingController(training_service)
            
            # Configure progress tracking
            def progress_callback(progress_dict):
                # Calculate overall progress based on current phase and step
                if 'step' in progress_dict and 'total_steps' in progress_dict:
                    overall_progress = min(95, (progress_dict['step'] / progress_dict['total_steps']) * 100)
                    phase_progress = (progress_dict['step'] % 100) / 100 * 100  # Progress within current phase
                else:
                    overall_progress = progress_dict.get('value', 0)
                    phase_progress = progress_dict.get('phase_progress', 0)
                
                # Prepare metrics
                metrics = {
                    'loss': progress_dict.get('loss', 0),
                    'learning_rate': progress_dict.get('learning_rate', 0),
                    'step': progress_dict.get('step', 0),
                    'epoch': progress_dict.get('epoch', 0),
                    'total_steps': config['max_steps']
                }
                
                self._emit_progress(
                    progress_dict.get('status', 'Training in progress...'),
                    overall_progress,
                    phase_progress,
                    metrics,
                    progress_dict.get('log_message')
                )
            
            # Start training with configuration
            success = training_controller.start_training(
                config=config,
                progress_callback=progress_callback
            )
            
            return success
            
        except Exception as e:
            self.logger.error(f"Training execution failed: {str(e)}")
            return False
