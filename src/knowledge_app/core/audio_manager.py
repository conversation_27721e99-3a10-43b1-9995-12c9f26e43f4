"""
Audio management for Knowledge App
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from PyQt5.QtMultimedia import QSound, QMediaPlayer, QMediaPlaylist, QMediaContent
from PyQt5.QtCore import QUrl

logger = logging.getLogger(__name__)

class AudioManager:
    """Centralized audio management system"""
    
    _instance = None
    
    def __new__(cls, config: Optional[Dict[str, Any]] = None):
        if cls._instance is None:
            cls._instance = super(AudioManager, cls).__new__(cls)
            cls._instance._initialized = False
        if config is not None:
            cls._instance._config = config
        return cls._instance
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        if self._initialized:
            return
            
        if config is None:
            raise ValueError("Config is required for initialization")
            
        self._config = config
        self._initialized = True
        
        # Sound effects
        self.sounds_dir = Path(config.get('sounds_dir', 'assets/sounds'))
        self.sounds: Dict[str, QSound] = {}
        
        # Background music
        self.music_dir = Path(config.get('music_dir', 'assets/music'))
        self.media_player = QMediaPlayer()
        self.playlist = QMediaPlaylist()
        self.media_player.setPlaylist(self.playlist)
        self.media_player.setVolume(50)  # Default volume
        
        # Load sound effects
        self.load_sounds()
        
    def load_sounds(self) -> None:
        """Load sound effects"""
        sound_files = {
            'correct': 'correct.wav',
            'incorrect': 'incorrect.wav',
            'complete': 'complete.wav',
            'tick': 'tick.wav'
        }
        
        try:
            for name, file in sound_files.items():
                path = self.sounds_dir / file
                if path.exists():
                    self.sounds[name] = QSound(str(path))
                else:
                    logger.warning(f"Sound file not found: {path}")
        except Exception as e:
            logger.error(f"Error loading sounds: {e}")
            
    def play_sound(self, sound_name: str) -> None:
        """Play a sound effect"""
        if not self._config.get('sound_enabled', True):
            return
            
        try:
            if sound_name in self.sounds:
                self.sounds[sound_name].play()
        except Exception as e:
            logger.error(f"Error playing sound {sound_name}: {e}")
            
    def load_music_playlist(self) -> None:
        """Load music files into playlist"""
        try:
            if not self.music_dir.exists():
                logger.warning(f"Music directory not found: {self.music_dir}")
                return
                
            self.playlist.clear()
            for file in self.music_dir.glob('*.mp3'):
                url = QUrl.fromLocalFile(str(file))
                self.playlist.addMedia(QMediaContent(url))
                
            if self.playlist.mediaCount() > 0:
                self.playlist.setPlaybackMode(QMediaPlaylist.Loop)
                logger.info(f"Loaded {self.playlist.mediaCount()} music tracks")
            else:
                logger.warning("No music files found")
                
        except Exception as e:
            logger.error(f"Error loading music playlist: {e}")
            
    def play_music(self) -> None:
        """Start playing background music"""
        if not self._config.get('music_enabled', True):
            return
            
        try:
            if self.playlist.mediaCount() == 0:
                self.load_music_playlist()
            self.media_player.play()
        except Exception as e:
            logger.error(f"Error playing music: {e}")
            
    def pause_music(self) -> None:
        """Pause background music"""
        try:
            self.media_player.pause()
        except Exception as e:
            logger.error(f"Error pausing music: {e}")
            
    def stop_music(self) -> None:
        """Stop background music"""
        try:
            self.media_player.stop()
        except Exception as e:
            logger.error(f"Error stopping music: {e}")
            
    def set_volume(self, volume: int) -> None:
        """Set audio volume"""
        try:
            volume = max(0, min(100, volume))  # Clamp between 0-100
            self.media_player.setVolume(volume)
        except Exception as e:
            logger.error(f"Error setting volume: {e}")
            
    def toggle_music(self) -> bool:
        """Toggle music playback"""
        try:
            if self.media_player.state() == QMediaPlayer.PlayingState:
                self.pause_music()
                return False
            else:
                self.play_music()
                return True
        except Exception as e:
            logger.error(f"Error toggling music: {e}")
            return False
            
    def cleanup(self) -> None:
        """Clean up audio resources"""
        try:
            self.stop_music()
            self.media_player.setPlaylist(None)
            self.playlist.clear()
            self.sounds.clear()
        except Exception as e:
            logger.error(f"Error cleaning up audio resources: {e}")
            
    def __del__(self):
        """Ensure cleanup on deletion"""
        self.cleanup() 