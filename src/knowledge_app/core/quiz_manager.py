"""
Quiz manager implementation
"""

import logging
from typing import Dict, Optional
from datetime import datetime
import uuid

from .interfaces import (
    IQuizManager,
    IQuestionService,
    QuizSession,
    Question
)

logger = logging.getLogger(__name__)

class QuizManager(IQuizManager):
    """Implementation of the quiz manager"""
    
    def __init__(self, question_service: IQuestionService):
        """
        Initialize quiz manager
        
        Args:
            question_service: Question service instance
        """
        self._question_service = question_service
        self._active_sessions: Dict[str, QuizSession] = {}
        
    def create_session(self, user_id: str, category: str,
                      difficulty: int, num_questions: int) -> QuizSession:
        """
        Create a new quiz session
        
        Args:
            user_id: User identifier
            category: Question category
            difficulty: Question difficulty level
            num_questions: Number of questions
            
        Returns:
            New quiz session
            
        Raises:
            ValueError: If parameters are invalid
        """
        try:
            # Validate parameters
            if not user_id:
                raise ValueError("User ID is required")
            if difficulty < 1 or difficulty > 5:
                raise ValueError("Difficulty must be between 1 and 5")
            if num_questions < 1:
                raise ValueError("Number of questions must be positive")
                
            # Generate questions
            questions = []
            for _ in range(num_questions):
                try:
                    question = self._question_service.generate_question(
                        category=category,
                        difficulty=difficulty
                    )
                    if self._question_service.validate_question(question):
                        questions.append(question)
                except Exception as e:
                    logger.error(f"Error generating question: {e}")
                    continue
                    
            if not questions:
                raise ValueError("Failed to generate any valid questions")
                
            # Create session
            session = QuizSession(
                id=str(uuid.uuid4()),
                user_id=user_id,
                questions=questions,
                start_time=datetime.now(),
                end_time=None,
                score=None
            )
            
            # Store session
            self._active_sessions[session.id] = session
            
            logger.info(f"Created quiz session {session.id} for user {user_id}")
            return session
            
        except Exception as e:
            logger.error(f"Error creating quiz session: {e}")
            raise
            
    def end_session(self, session_id: str) -> QuizSession:
        """
        End a quiz session
        
        Args:
            session_id: Session identifier
            
        Returns:
            Updated session
            
        Raises:
            ValueError: If session not found
        """
        session = self.get_session(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
            
        try:
            # Calculate final score
            if session.score is None:
                total_score = 0.0
                answered_questions = 0
                
                for question in session.questions:
                    if "user_answer" in question.metadata:
                        score = self._question_service.grade_answer(
                            question,
                            question.metadata["user_answer"]
                        )
                        total_score += score
                        answered_questions += 1
                        
                session.score = (total_score / answered_questions) if answered_questions > 0 else 0.0
                
            # Set end time if not already set
            if session.end_time is None:
                session.end_time = datetime.now()
                
            logger.info(f"Ended quiz session {session_id} with score {session.score}")
            return session
            
        except Exception as e:
            logger.error(f"Error ending quiz session: {e}")
            raise
            
    def get_session(self, session_id: str) -> Optional[QuizSession]:
        """
        Get a quiz session
        
        Args:
            session_id: Session identifier
            
        Returns:
            Quiz session or None if not found
        """
        return self._active_sessions.get(session_id)
        
    def submit_answer(self, session_id: str, question_id: str,
                     answer: str) -> float:
        """
        Submit an answer for a question
        
        Args:
            session_id: Session identifier
            question_id: Question identifier
            answer: User's answer
            
        Returns:
            Score for the answer
            
        Raises:
            ValueError: If session or question not found
        """
        session = self.get_session(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
            
        try:
            # Find question
            question = next(
                (q for q in session.questions if q.id == question_id),
                None
            )
            if not question:
                raise ValueError(f"Question {question_id} not found in session {session_id}")
                
            # Grade answer
            score = self._question_service.grade_answer(question, answer)
            
            # Store answer and score in question metadata
            question.metadata["user_answer"] = answer
            question.metadata["score"] = score
            question.metadata["answered_at"] = datetime.now().isoformat()
            
            logger.info(f"Submitted answer for question {question_id} in session {session_id}, score: {score}")
            return score
            
        except Exception as e:
            logger.error(f"Error submitting answer: {e}")
            raise 