"""
Hardware Utilities

This module provides functionality for detecting and analyzing hardware capabilities
relevant for machine learning training.
"""

import os
import platform
import psutil
import logging
from typing import Dict, Any, Optional
import torch
import torch.cuda as cuda
import numpy as np

logger = logging.getLogger(__name__)

def get_gpu_info() -> Dict[str, Any]:
    """Get information about available GPU(s)"""
    gpu_info = {
        'available': False,
        'name': None,
        'vram_gb': 0,
        'compute_capability': 0,
        'cuda_version': None
    }
    
    try:
        if not torch.cuda.is_available():
            return gpu_info
            
        gpu_info['available'] = True
        gpu_info['name'] = torch.cuda.get_device_name(0)
        gpu_info['vram_gb'] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        gpu_info['compute_capability'] = float(f"{torch.cuda.get_device_capability()[0]}.{torch.cuda.get_device_capability()[1]}")
        gpu_info['cuda_version'] = torch.version.cuda
        
    except Exception as e:
        logger.warning(f"Error getting GPU info: {e}")
        
    return gpu_info

def get_cpu_info() -> Dict[str, Any]:
    """Get information about the CPU"""
    try:
        return {
            'cores': psutil.cpu_count(logical=False),
            'threads': psutil.cpu_count(logical=True),
            'frequency': psutil.cpu_freq().max if psutil.cpu_freq() else None,
            'ram_gb': psutil.virtual_memory().total / (1024**3)
        }
    except Exception as e:
        logger.warning(f"Error getting CPU info: {e}")
        return {
            'cores': None,
            'threads': None,
            'frequency': None,
            'ram_gb': None
        }

def get_hardware_specs() -> Dict[str, Any]:
    """Get comprehensive hardware specifications"""
    gpu_info = get_gpu_info()
    cpu_info = get_cpu_info()
    
    specs = {
        'gpu_available': gpu_info['available'],
        'gpu_name': gpu_info['name'],
        'gpu_vram_gb': gpu_info['vram_gb'],
        'gpu_compute_capability': gpu_info['compute_capability'],
        'cuda_version': gpu_info['cuda_version'],
        'cpu_cores': cpu_info['cores'],
        'cpu_threads': cpu_info['threads'],
        'cpu_frequency': cpu_info['frequency'],
        'ram_gb': cpu_info['ram_gb'],
        'os_system': platform.system(),
        'os_release': platform.release()
    }
    
    # Add derived metrics
    specs['compute_score'] = _calculate_compute_score(specs)
    
    return specs

def _calculate_compute_score(specs: Dict[str, Any]) -> float:
    """Calculate a normalized compute capability score (0-10)"""
    score = 0.0
    
    # GPU Score (up to 7 points)
    if specs['gpu_available']:
        # VRAM score (up to 3 points)
        vram_score = min(3.0, specs['gpu_vram_gb'] / 8.0)
        
        # Compute capability score (up to 4 points)
        compute_score = min(4.0, specs['gpu_compute_capability'] / 2.0)
        
        score += vram_score + compute_score
    
    # CPU Score (up to 3 points)
    if specs['cpu_cores']:
        # Core count score (up to 2 points)
        core_score = min(2.0, specs['cpu_cores'] / 8.0)
        
        # RAM score (up to 1 point)
        ram_score = min(1.0, specs['ram_gb'] / 32.0)
        
        score += core_score + ram_score
    
    return score

def estimate_optimal_batch_size(specs: Dict[str, Any]) -> int:
    """Estimate optimal batch size based on hardware"""
    if not specs['gpu_available']:
        # CPU-only mode - conservative batch size
        return 32
        
    # Start with base batch size
    base_size = 64
    
    # Adjust for VRAM
    vram_factor = min(4, max(1, specs['gpu_vram_gb'] / 8))
    
    # Adjust for compute capability
    compute_factor = min(2, max(1, specs['gpu_compute_capability'] / 7.0))
    
    # Calculate final size
    batch_size = int(base_size * vram_factor * compute_factor)
    
    # Round to nearest power of 2
    return 2 ** int(np.log2(batch_size) + 0.5)

def can_use_mixed_precision(specs: Dict[str, Any]) -> bool:
    """Check if mixed precision training is supported"""
    if not specs['gpu_available']:
        return False
        
    # Mixed precision requires Volta, Turing, or Ampere GPU
    return specs['gpu_compute_capability'] >= 7.0 