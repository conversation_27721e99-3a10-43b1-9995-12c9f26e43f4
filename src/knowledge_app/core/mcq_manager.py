"""
MCQ Manager - Unified interface for online and offline MCQ generation
Handles switching between Groq API and local models seamlessly
"""

import logging
from typing import Dict, List, Any, Optional
import asyncio

# CRITICAL MEMORY FIX: Defer torch import to reduce startup memory
# torch will be imported lazily when needed for cleanup
torch = None

logger = logging.getLogger(__name__)

class MCQManager:
    """Unified manager for MCQ generation with online/offline mode switching"""
    
    def __init__(self, config=None):
        self.config = config
        self.instant_generator = None
        self.offline_generator = None
        self.online_generator = None
        self.rag_generator = None
        self._offline_mode = False
        self._rag_mode = True  # Enable RAG by default when available
        self._instant_mode = True  # Enable instant generation by default

        # Initialize generators
        self._initialize_generators()
    
    def _initialize_generators(self):
        """Initialize instant, RAG, offline, and online generators"""
        try:
            # Initialize instant generator (highest priority - always works!)
            from .instant_mcq_generator import InstantMCQGenerator
            self.instant_generator = InstantMCQGenerator()
            logger.info("⚡ Instant MCQ generator ready - no model loading required!")

            # Initialize RAG-enhanced generator (high quality when available)
            from .rag_mcq_generator import RAGMCQGenerator
            self.rag_generator = RAGMCQGenerator()
            rag_ready = self.rag_generator.initialize()
            if rag_ready:
                logger.info("🔍 RAG MCQ generator ready with knowledge base")
            else:
                logger.warning("⚠️ RAG MCQ generator not ready - will use fallback")

            # Initialize offline generator (lazy loading - only when needed)
            from .offline_mcq_generator import OfflineMCQGenerator
            self.offline_generator = OfflineMCQGenerator()

            # Don't initialize offline generator during startup - only when explicitly requested
            logger.info("🏠 Offline MCQ generator ready for lazy initialization...")

            # Initialize online generator (existing inference system)
            from .inference import CloudInference
            if self.config:
                self.online_generator = CloudInference(self.config)

            logger.info("✅ MCQ Manager initialized with instant, RAG, offline, and online capabilities")

        except Exception as e:
            logger.error(f"❌ Error initializing MCQ Manager: {e}")
    
    def set_offline_mode(self, offline: bool):
        """Set the MCQ generation mode"""
        self._offline_mode = offline
        logger.info(f"MCQ generation mode set to: {'Offline' if offline else 'Online'}")

    def set_rag_mode(self, rag_enabled: bool):
        """Enable or disable RAG-enhanced MCQ generation"""
        self._rag_mode = rag_enabled
        logger.info(f"RAG mode set to: {'Enabled' if rag_enabled else 'Disabled'}")

    def set_instant_mode(self, instant_enabled: bool):
        """Enable or disable instant MCQ generation"""
        self._instant_mode = instant_enabled
        logger.info(f"Instant mode set to: {'Enabled' if instant_enabled else 'Disabled'}")

    def is_offline_mode(self) -> bool:
        """Check if currently in offline mode"""
        return self._offline_mode

    def is_rag_mode(self) -> bool:
        """Check if RAG mode is enabled"""
        return self._rag_mode

    def is_instant_mode(self) -> bool:
        """Check if instant mode is enabled"""
        return self._instant_mode

    def is_rag_available(self) -> bool:
        """Check if RAG mode is available"""
        return self.rag_generator is not None and self.rag_generator.is_initialized

    def is_instant_available(self) -> bool:
        """Check if instant mode is available (always True!)"""
        if self.instant_generator is None:
            # Try to reinitialize instant generator if it's missing
            try:
                from .instant_mcq_generator import InstantMCQGenerator
                self.instant_generator = InstantMCQGenerator()
                logger.info("⚡ Instant MCQ generator reinitialized")
            except Exception as e:
                logger.error(f"❌ Failed to reinitialize instant generator: {e}")
                return False
        return self.instant_generator is not None
    
    def is_offline_available(self) -> bool:
        """Check if offline mode is available (without initializing)"""
        if not self.offline_generator:
            return False

        try:
            # Check if already initialized
            if self.offline_generator.is_initialized:
                return True

            # CRITICAL MEMORY FIX: Don't import torch during availability check
            # Just check if local model inference module is available
            try:
                from .local_model_inference import LocalModelInference
                # Don't check CUDA availability here to avoid importing torch during startup
                return True  # Module available, assume it will work when needed
            except ImportError as e:
                logger.warning(f"⚠️ Local model inference not available: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Error checking offline availability: {e}")
            return False
    
    def is_online_available(self) -> bool:
        """Check if online mode is available"""
        if not self.online_generator:
            return False
        
        try:
            # Check if we have API configuration
            return hasattr(self.online_generator, 'client') and self.online_generator.client is not None
        except Exception:
            return False
    
    async def generate_quiz_async(self, context: str, difficulty: str = "medium") -> Dict[str, Any]:
        """
        Generate a single MCQ question using instant generation (no model loading!)

        Args:
            context: The text content to generate questions from
            difficulty: Question difficulty level

        Returns:
            Dictionary containing question, options, correct answer, and explanation
        """
        try:
            # Prioritize high-quality generation methods over instant generation

            # Try offline generation first if enabled and available (7B models)
            if self._offline_mode and self.is_offline_available():
                logger.info("🧠 Using offline 7B model MCQ generation")
                try:
                    result = await self._generate_offline(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Offline generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Offline generation failed: {e}")

            # Try RAG-enhanced generation if enabled and available
            if self._rag_mode and self.is_rag_available():
                logger.info("🔍 Using RAG-enhanced MCQ generation")
                try:
                    result = await self._generate_rag(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ RAG generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ RAG generation failed: {e}")

            # Try online generation if available
            if self.is_online_available():
                logger.info("🌐 Using online MCQ generation")
                try:
                    result = await self._generate_online(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Online generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Online generation failed: {e}")

            # Use instant generation as fallback only
            if self.is_instant_available():
                logger.info("⚡ Using instant MCQ generation (fallback - no model loading)")
                try:
                    result = await self._generate_instant(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Instant generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Instant generation failed: {e}")

            # If all else fails, use emergency fallback
            logger.warning("⚠️ All generation methods failed, using emergency fallback")
            return await self._generate_fallback(context, difficulty)

        except Exception as e:
            logger.error(f"❌ MCQ generation failed: {e}")
            return await self._generate_fallback(context, difficulty)
    
    async def _generate_offline(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using offline local models"""
        if not self.offline_generator:
            raise RuntimeError("Offline generator not available")

        # Smart initialization check - only initialize if truly needed
        if not self.offline_generator.is_initialized:
            logger.info("🔄 Initializing offline generator for MCQ generation...")
            if not self.offline_generator.initialize():
                raise RuntimeError("Failed to initialize offline generator")
        else:
            logger.debug("✅ Offline generator already initialized - reusing existing model")

        logger.info("🏠 Generating MCQ using local models (offline)")
        return await self.offline_generator.generate_quiz_async(context, difficulty)

    async def _generate_instant(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using instant content analysis (no model loading)"""
        if not self.instant_generator:
            raise RuntimeError("Instant generator not available")

        logger.info("⚡ Generating instant MCQ using content analysis")
        return await self.instant_generator.generate_quiz_async(context, difficulty)

    async def _generate_rag(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using RAG-enhanced approach"""
        if not self.rag_generator:
            raise RuntimeError("RAG generator not available")

        logger.info("🔍 Generating RAG-enhanced MCQ using knowledge base")
        return await self.rag_generator.generate_quiz_async(context, difficulty, use_rag=True)

    async def _generate_online(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using online API (Groq)"""
        if not self.online_generator:
            raise RuntimeError("Online generator not available")
        
        logger.info("🌐 Generating MCQ using external API (online)")
        return await self.online_generator.generate_quiz_async(context, difficulty)
    
    async def _generate_fallback(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using fallback method when primary method fails"""
        try:
            # Try RAG mode if not already tried and available
            if not self._rag_mode and self.is_rag_available():
                logger.warning("⚠️ Primary generation failed, trying RAG fallback")
                return await self._generate_rag(context, difficulty)

            # Try the opposite mode as fallback
            if self._offline_mode and self.is_online_available():
                logger.warning("⚠️ Offline generation failed, trying online fallback")
                return await self._generate_online(context, difficulty)
            elif not self._offline_mode and self.is_offline_available():
                logger.warning("⚠️ Online generation failed, trying offline fallback")
                return await self._generate_offline(context, difficulty)
            else:
                # Generate a basic fallback question
                logger.warning("⚠️ All modes failed, using basic fallback")
                return self._generate_basic_fallback(context)

        except Exception as e:
            logger.error(f"❌ Fallback generation also failed: {e}")
            return self._generate_basic_fallback(context)
    
    def _generate_basic_fallback(self, context: str) -> Dict[str, Any]:
        """Generate a very basic fallback question using simple text analysis"""
        import re

        # Extract key words from context
        words = re.findall(r'\b[A-Za-z]{4,}\b', context.lower())
        common_words = {'that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'}
        key_words = [w for w in words if w not in common_words][:10]

        # Generate context-aware question
        if key_words:
            primary_topic = key_words[0].title()
            question = f"Based on the content about {primary_topic}, what is the main focus of the discussion?"

            # Generate options based on content
            options = {
                "A": f"Technical aspects of {primary_topic}",
                "B": f"General overview of {primary_topic}",
                "C": f"Historical development of {primary_topic}",
                "D": f"Practical applications of {primary_topic}"
            }

            explanation = f"This question focuses on the main theme related to {primary_topic} discussed in the content."
        else:
            # Ultra-basic fallback
            question = "Based on the provided content, what is the main topic being discussed?"
            options = {
                "A": "Technical specifications and details",
                "B": "General concepts and principles",
                "C": "Historical background and context",
                "D": "Practical applications and examples"
            }
            explanation = "This is a general question generated when AI model generation is unavailable."

        return {
            "question": question,
            "options": options,
            "correct": "B",  # Usually the general overview is a safe choice
            "explanation": explanation
        }
    
    def generate_multiple_questions(self, context: str, num_questions: int = 5, 
                                  difficulty: str = "medium") -> List[Dict[str, Any]]:
        """
        Generate multiple MCQ questions using the current mode
        
        Args:
            context: The text content to generate questions from
            num_questions: Number of questions to generate
            difficulty: Question difficulty level
            
        Returns:
            List of question dictionaries
        """
        try:
            # Try instant generation first (no model loading!)
            if self._instant_mode and self.is_instant_available():
                logger.info(f"⚡ Generating {num_questions} instant MCQs (no model loading)")
                return self.instant_generator.generate_multiple_questions(context, num_questions, difficulty)

            # Try RAG-enhanced generation if enabled and available
            if self._rag_mode and self.is_rag_available():
                logger.info(f"🔍 Generating {num_questions} RAG-enhanced MCQs")
                return self.rag_generator.generate_multiple_questions(context, num_questions, difficulty)

            if self._offline_mode and self.offline_generator:
                logger.info(f"🏠 Generating {num_questions} MCQs using local models")
                return self.offline_generator.generate_multiple_questions(context, num_questions, difficulty)
            else:
                # For online mode, generate questions one by one
                logger.info(f"🌐 Generating {num_questions} MCQs using external API")
                return asyncio.run(self._generate_multiple_online(context, num_questions, difficulty))

        except Exception as e:
            logger.error(f"❌ Multiple question generation failed: {e}")
            return []

    def cleanup(self):
        """Clean up MCQ manager resources to prevent memory leaks and crashes"""
        try:
            logger.info("🧹 Starting MCQ manager cleanup...")

            # Clean up offline generator
            if self.offline_generator:
                try:
                    if hasattr(self.offline_generator, 'local_inference') and self.offline_generator.local_inference:
                        if hasattr(self.offline_generator.local_inference, 'unload_model'):
                            logger.info("🧹 Unloading offline model...")
                            self.offline_generator.local_inference.unload_model()
                    self.offline_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up offline generator: {e}")

            # Clean up online generator
            if self.online_generator:
                try:
                    # Close any open connections
                    if hasattr(self.online_generator, 'cleanup'):
                        self.online_generator.cleanup()
                    self.online_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up online generator: {e}")

            # Clean up RAG generator
            if self.rag_generator:
                try:
                    if hasattr(self.rag_generator, 'cleanup'):
                        self.rag_generator.cleanup()
                    self.rag_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up RAG generator: {e}")

            # Clean up instant generator
            if self.instant_generator:
                try:
                    if hasattr(self.instant_generator, 'cleanup'):
                        self.instant_generator.cleanup()
                    self.instant_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up instant generator: {e}")

            # Force garbage collection
            import gc
            gc.collect()
            gc.collect()  # Call twice for better cleanup

            # Clear CUDA cache if available (only if torch was imported)
            try:
                # CRITICAL MEMORY FIX: Import torch lazily only for cleanup
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()  # Wait for all CUDA operations to complete
            except ImportError:
                # torch not available, skip CUDA cleanup
                pass

            logger.info("✅ MCQ manager cleanup completed successfully")

        except Exception as e:
            logger.error(f"❌ Error during MCQ manager cleanup: {e}")
            # Still try basic cleanup
            try:
                import gc
                gc.collect()
                try:
                    # CRITICAL MEMORY FIX: Import torch lazily only for cleanup
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
            except:
                pass
    
    async def _generate_multiple_online(self, context: str, num_questions: int, difficulty: str) -> List[Dict[str, Any]]:
        """Generate multiple questions using online API"""
        questions = []
        
        for i in range(num_questions):
            try:
                question = await self._generate_online(context, difficulty)
                if question:
                    questions.append(question)
                    logger.info(f"Generated online question {i+1}/{num_questions}")
            except Exception as e:
                logger.warning(f"Failed to generate online question {i+1}: {e}")
        
        return questions
    
    def get_mode_status(self) -> Dict[str, Any]:
        """Get current mode status and availability"""
        return {
            "current_mode": "offline" if self._offline_mode else "online",
            "instant_enabled": self._instant_mode,
            "instant_available": self.is_instant_available(),
            "rag_enabled": self._rag_mode,
            "rag_available": self.is_rag_available(),
            "offline_available": self.is_offline_available(),
            "online_available": self.is_online_available(),
            "can_switch": True
        }
    
    def get_mode_info(self) -> str:
        """Get human-readable mode information"""
        instant_status = "⚡ Instant" if self.is_instant_available() and self._instant_mode else ""
        rag_status = "🔍 RAG Enhanced" if self.is_rag_available() and self._rag_mode else ""

        # Instant mode takes priority
        if self._instant_mode and self.is_instant_available():
            return f"⚡ Instant Mode - No model loading required {rag_status}".strip()

        if self._offline_mode:
            if self.is_offline_available():
                return f"🏠 Offline Mode - Using local models {rag_status}".strip()
            else:
                return "⚠️ Offline Mode - Local models not available"
        else:
            if self.is_online_available():
                return f"🌐 Online Mode - Using external API {rag_status}".strip()
            else:
                return "⚠️ Online Mode - API not configured"
    
    def switch_mode(self):
        """Switch between online and offline modes"""
        new_mode = not self._offline_mode
        
        # Check if the new mode is available
        if new_mode and not self.is_offline_available():
            logger.warning("Cannot switch to offline mode - local models not available")
            return False
        elif not new_mode and not self.is_online_available():
            logger.warning("Cannot switch to online mode - API not configured")
            return False
        
        self.set_offline_mode(new_mode)
        return True
    
    def update_config(self, config):
        """Update configuration and reinitialize if needed"""
        self.config = config
        
        # Check if offline mode setting changed
        if config and hasattr(config, 'get_value'):
            offline_mode = config.get_value('mcq_settings.offline_mode', False)
            if offline_mode != self._offline_mode:
                self.set_offline_mode(offline_mode)
        
        # Reinitialize online generator with new config
        try:
            from .inference import CloudInference
            self.online_generator = CloudInference(config)
        except Exception as e:
            logger.warning(f"Failed to update online generator config: {e}")

# Global instance for easy access
_mcq_manager = None

def get_mcq_manager(config=None) -> MCQManager:
    """Get the global MCQ manager instance"""
    global _mcq_manager
    if _mcq_manager is None:
        _mcq_manager = MCQManager(config)
    elif config:
        _mcq_manager.update_config(config)
    return _mcq_manager
