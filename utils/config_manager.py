"""
Advanced Configuration Manager for Devin Local AI
Provides sophisticated configuration management with validation, profiles, and hot-reloading.
"""

import os
import yaml
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class ConfigFormat(Enum):
    """Supported configuration formats"""
    YAML = "yaml"
    JSON = "json"
    TOML = "toml"


@dataclass
class ConfigProfile:
    """Configuration profile for different environments"""
    name: str
    description: str
    config: Dict[str, Any]
    active: bool = False


class ConfigValidator:
    """Validates configuration against schema"""
    
    def __init__(self, schema: Dict[str, Any]):
        self.schema = schema
    
    def validate(self, config: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate configuration against schema
        
        Returns:
            Dictionary with validation errors (empty if valid)
        """
        errors = {}
        self._validate_recursive(config, self.schema, "", errors)
        return errors
    
    def _validate_recursive(self, config: Dict, schema: Dict, path: str, errors: Dict):
        """Recursively validate configuration"""
        for key, schema_value in schema.items():
            current_path = f"{path}.{key}" if path else key
            
            if key not in config:
                if schema_value.get('required', False):
                    if 'missing' not in errors:
                        errors['missing'] = []
                    errors['missing'].append(current_path)
                continue
            
            config_value = config[key]
            
            # Type validation
            expected_type = schema_value.get('type')
            if expected_type and not isinstance(config_value, expected_type):
                if 'type_mismatch' not in errors:
                    errors['type_mismatch'] = []
                errors['type_mismatch'].append(
                    f"{current_path}: expected {expected_type.__name__}, got {type(config_value).__name__}"
                )
            
            # Value validation
            allowed_values = schema_value.get('allowed_values')
            if allowed_values and config_value not in allowed_values:
                if 'invalid_value' not in errors:
                    errors['invalid_value'] = []
                errors['invalid_value'].append(
                    f"{current_path}: {config_value} not in {allowed_values}"
                )
            
            # Range validation
            min_value = schema_value.get('min')
            max_value = schema_value.get('max')
            if isinstance(config_value, (int, float)):
                if min_value is not None and config_value < min_value:
                    if 'range_error' not in errors:
                        errors['range_error'] = []
                    errors['range_error'].append(f"{current_path}: {config_value} < {min_value}")
                
                if max_value is not None and config_value > max_value:
                    if 'range_error' not in errors:
                        errors['range_error'] = []
                    errors['range_error'].append(f"{current_path}: {config_value} > {max_value}")
            
            # Nested validation
            if isinstance(schema_value, dict) and 'properties' in schema_value:
                if isinstance(config_value, dict):
                    self._validate_recursive(
                        config_value, 
                        schema_value['properties'], 
                        current_path, 
                        errors
                    )


class ConfigWatcher(FileSystemEventHandler):
    """Watches configuration files for changes"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.last_modified = {}
    
    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix in ['.yaml', '.yml', '.json']:
            # Debounce rapid file changes
            current_time = time.time()
            last_time = self.last_modified.get(str(file_path), 0)
            
            if current_time - last_time > 1.0:  # 1 second debounce
                self.last_modified[str(file_path)] = current_time
                self.config_manager._reload_config(file_path)


class ConfigManager:
    """Advanced configuration manager with profiles and validation"""
    
    def __init__(self, config_path: Union[str, Path] = "config.yaml"):
        self.config_path = Path(config_path)
        self.config_dir = self.config_path.parent
        self.profiles: Dict[str, ConfigProfile] = {}
        self.current_config: Dict[str, Any] = {}
        self.active_profile: Optional[str] = None
        self.validator: Optional[ConfigValidator] = None
        self.watchers: List[Observer] = []
        self.reload_callbacks: List[callable] = []
        
        # Load schema if available
        schema_path = self.config_dir / "config_schema.yaml"
        if schema_path.exists():
            self.load_schema(schema_path)
        
        # Load main configuration
        self.load_config()
        
        # Load profiles
        self.load_profiles()
    
    def load_schema(self, schema_path: Path):
        """Load configuration schema for validation"""
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = yaml.safe_load(f)
            self.validator = ConfigValidator(schema)
        except Exception as e:
            print(f"Warning: Could not load config schema: {e}")
    
    def load_config(self, config_path: Path = None) -> Dict[str, Any]:
        """Load configuration from file"""
        if config_path is None:
            config_path = self.config_path
        
        try:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                raise ValueError(f"Unsupported config format: {config_path.suffix}")
            
            # Validate configuration
            if self.validator:
                errors = self.validator.validate(config)
                if errors:
                    print(f"Configuration validation errors: {errors}")
            
            self.current_config = config
            return config
            
        except Exception as e:
            print(f"Error loading config from {config_path}: {e}")
            return {}
    
    def save_config(self, config: Dict[str, Any] = None, config_path: Path = None):
        """Save configuration to file"""
        if config is None:
            config = self.current_config
        
        if config_path is None:
            config_path = self.config_path
        
        try:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2)
            
            print(f"Configuration saved to {config_path}")
            
        except Exception as e:
            print(f"Error saving config to {config_path}: {e}")
    
    def load_profiles(self):
        """Load configuration profiles"""
        profiles_dir = self.config_dir / "profiles"
        if not profiles_dir.exists():
            return
        
        for profile_file in profiles_dir.glob("*.yaml"):
            try:
                with open(profile_file, 'r', encoding='utf-8') as f:
                    profile_data = yaml.safe_load(f)
                
                profile = ConfigProfile(
                    name=profile_file.stem,
                    description=profile_data.get('description', ''),
                    config=profile_data.get('config', {}),
                    active=profile_data.get('active', False)
                )
                
                self.profiles[profile.name] = profile
                
                if profile.active:
                    self.activate_profile(profile.name)
                    
            except Exception as e:
                print(f"Error loading profile {profile_file}: {e}")
    
    def save_profile(self, profile: ConfigProfile):
        """Save a configuration profile"""
        profiles_dir = self.config_dir / "profiles"
        profiles_dir.mkdir(exist_ok=True)
        
        profile_file = profiles_dir / f"{profile.name}.yaml"
        profile_data = {
            'description': profile.description,
            'active': profile.active,
            'config': profile.config
        }
        
        try:
            with open(profile_file, 'w', encoding='utf-8') as f:
                yaml.dump(profile_data, f, default_flow_style=False, indent=2)
            
            self.profiles[profile.name] = profile
            
        except Exception as e:
            print(f"Error saving profile {profile.name}: {e}")
    
    def activate_profile(self, profile_name: str) -> bool:
        """Activate a configuration profile"""
        if profile_name not in self.profiles:
            print(f"Profile '{profile_name}' not found")
            return False
        
        # Deactivate current profile
        if self.active_profile:
            self.profiles[self.active_profile].active = False
        
        # Activate new profile
        profile = self.profiles[profile_name]
        profile.active = True
        self.active_profile = profile_name
        
        # Merge profile config with base config
        self.current_config = self._merge_configs(self.current_config, profile.config)
        
        # Notify callbacks
        self._notify_reload_callbacks()
        
        print(f"Activated profile: {profile_name}")
        return True
    
    def create_profile(self, name: str, description: str, config: Dict[str, Any]) -> ConfigProfile:
        """Create a new configuration profile"""
        profile = ConfigProfile(
            name=name,
            description=description,
            config=config,
            active=False
        )
        
        self.save_profile(profile)
        return profile
    
    def get_profiles(self) -> List[ConfigProfile]:
        """Get list of available profiles"""
        return list(self.profiles.values())
    
    def get_config(self, key: str = None, default: Any = None) -> Any:
        """Get configuration value"""
        if key is None:
            return self.current_config.copy()
        
        # Support nested keys with dot notation
        keys = key.split('.')
        value = self.current_config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set_config(self, key: str, value: Any):
        """Set configuration value"""
        keys = key.split('.')
        config = self.current_config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        
        # Notify callbacks
        self._notify_reload_callbacks()
    
    def start_watching(self):
        """Start watching configuration files for changes"""
        if not self.watchers:
            observer = Observer()
            event_handler = ConfigWatcher(self)
            observer.schedule(event_handler, str(self.config_dir), recursive=True)
            observer.start()
            self.watchers.append(observer)
            print("Started watching configuration files")
    
    def stop_watching(self):
        """Stop watching configuration files"""
        for observer in self.watchers:
            observer.stop()
            observer.join()
        self.watchers.clear()
        print("Stopped watching configuration files")
    
    def add_reload_callback(self, callback: callable):
        """Add callback to be called when config is reloaded"""
        self.reload_callbacks.append(callback)
    
    def remove_reload_callback(self, callback: callable):
        """Remove reload callback"""
        if callback in self.reload_callbacks:
            self.reload_callbacks.remove(callback)
    
    def _reload_config(self, file_path: Path):
        """Reload configuration from file"""
        print(f"Reloading configuration from {file_path}")
        
        if file_path == self.config_path:
            self.load_config()
        elif file_path.parent.name == "profiles":
            self.load_profiles()
        
        self._notify_reload_callbacks()
    
    def _notify_reload_callbacks(self):
        """Notify all reload callbacks"""
        for callback in self.reload_callbacks:
            try:
                callback(self.current_config)
            except Exception as e:
                print(f"Error in reload callback: {e}")
    
    def _merge_configs(self, base: Dict, override: Dict) -> Dict:
        """Merge two configuration dictionaries"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def export_config(self, format: ConfigFormat = ConfigFormat.YAML) -> str:
        """Export current configuration as string"""
        if format == ConfigFormat.YAML:
            return yaml.dump(self.current_config, default_flow_style=False, indent=2)
        elif format == ConfigFormat.JSON:
            return json.dumps(self.current_config, indent=2)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get summary of current configuration"""
        return {
            'config_file': str(self.config_path),
            'active_profile': self.active_profile,
            'available_profiles': [p.name for p in self.profiles.values()],
            'watching': len(self.watchers) > 0,
            'validation_enabled': self.validator is not None,
            'config_keys': list(self.current_config.keys())
        }


def create_config_manager(config_path: Union[str, Path] = "config.yaml") -> ConfigManager:
    """
    Create a configuration manager
    
    Args:
        config_path: Path to the main configuration file
        
    Returns:
        ConfigManager instance
    """
    return ConfigManager(config_path)
