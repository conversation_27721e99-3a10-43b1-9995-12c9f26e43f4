"""
Model Discovery and Orchestration for Devin Local AI
Auto-discovers and manages all available local models across different providers.
"""

import subprocess
import requests
import json
import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class ModelProvider(Enum):
    """Supported model providers"""
    OLLAMA = "ollama"
    LM_STUDIO = "lm_studio"
    OOBABOOGA = "oobabooga"
    KOBOLDCPP = "koboldcpp"
    LLAMACPP = "llamacpp"
    OPENAI_COMPATIBLE = "openai_compatible"


@dataclass
class ModelInfo:
    """Information about a discovered model"""
    name: str
    provider: ModelProvider
    base_url: str
    size: Optional[str] = None
    type: Optional[str] = None
    capabilities: List[str] = None
    status: str = "unknown"  # available, running, error
    performance_score: float = 0.0
    
    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = []


class ModelDiscovery:
    """Discovers and manages all available local models"""
    
    def __init__(self):
        self.discovered_models: List[ModelInfo] = []
        self.active_providers: Dict[ModelProvider, Dict] = {}
        self.best_model: Optional[ModelInfo] = None
        
        # Common ports and endpoints to check
        self.provider_configs = {
            ModelProvider.OLLAMA: {
                "default_port": 11434,
                "health_endpoint": "/api/tags",
                "chat_endpoint": "/api/generate"
            },
            ModelProvider.LM_STUDIO: {
                "default_port": 1234,
                "health_endpoint": "/v1/models",
                "chat_endpoint": "/v1/chat/completions"
            },
            ModelProvider.OOBABOOGA: {
                "default_port": 5000,
                "health_endpoint": "/api/v1/model",
                "chat_endpoint": "/api/v1/chat/completions"
            },
            ModelProvider.KOBOLDCPP: {
                "default_port": 5001,
                "health_endpoint": "/api/v1/model",
                "chat_endpoint": "/api/v1/generate"
            }
        }
    
    def discover_all_models(self) -> List[ModelInfo]:
        """Discover all available models across all providers"""
        print("🔍 Discovering all available models...")
        
        self.discovered_models.clear()
        
        # Check each provider
        self._discover_ollama_models()
        self._discover_lm_studio_models()
        self._discover_oobabooga_models()
        self._discover_koboldcpp_models()
        self._discover_custom_endpoints()
        
        # Rank models by capability and performance
        self._rank_models()
        
        print(f"✅ Found {len(self.discovered_models)} models across {len(self.active_providers)} providers")
        
        return self.discovered_models
    
    def _discover_ollama_models(self):
        """Discover Ollama models"""
        try:
            # Check if Ollama is running
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                print("🦙 Found Ollama installation")
                
                # Parse model list
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            model_name = parts[0]
                            size = parts[1] if len(parts) > 1 else "unknown"
                            
                            # Determine capabilities based on model name
                            capabilities = self._analyze_model_capabilities(model_name)
                            
                            model = ModelInfo(
                                name=model_name,
                                provider=ModelProvider.OLLAMA,
                                base_url="http://localhost:11434",
                                size=size,
                                type="chat",
                                capabilities=capabilities,
                                status="available"
                            )
                            
                            self.discovered_models.append(model)
                
                self.active_providers[ModelProvider.OLLAMA] = {
                    "base_url": "http://localhost:11434",
                    "status": "active"
                }
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("⚠️  Ollama not found or not running")
    
    def _discover_lm_studio_models(self):
        """Discover LM Studio models"""
        base_url = "http://localhost:1234"
        
        try:
            response = requests.get(f"{base_url}/v1/models", timeout=3)
            if response.status_code == 200:
                print("🎬 Found LM Studio server")
                
                models_data = response.json()
                for model_data in models_data.get('data', []):
                    model_name = model_data.get('id', 'unknown')
                    
                    capabilities = self._analyze_model_capabilities(model_name)
                    
                    model = ModelInfo(
                        name=model_name,
                        provider=ModelProvider.LM_STUDIO,
                        base_url=base_url,
                        type="chat",
                        capabilities=capabilities,
                        status="available"
                    )
                    
                    self.discovered_models.append(model)
                
                self.active_providers[ModelProvider.LM_STUDIO] = {
                    "base_url": base_url,
                    "status": "active"
                }
                
        except requests.RequestException:
            print("⚠️  LM Studio not found or not running")
    
    def _discover_oobabooga_models(self):
        """Discover Oobabooga Text Generation WebUI models"""
        base_url = "http://localhost:5000"
        
        try:
            response = requests.get(f"{base_url}/api/v1/model", timeout=3)
            if response.status_code == 200:
                print("🕸️  Found Oobabooga WebUI")
                
                model_data = response.json()
                model_name = model_data.get('result', 'unknown')
                
                if model_name and model_name != 'None':
                    capabilities = self._analyze_model_capabilities(model_name)
                    
                    model = ModelInfo(
                        name=model_name,
                        provider=ModelProvider.OOBABOOGA,
                        base_url=base_url,
                        type="chat",
                        capabilities=capabilities,
                        status="available"
                    )
                    
                    self.discovered_models.append(model)
                
                self.active_providers[ModelProvider.OOBABOOGA] = {
                    "base_url": base_url,
                    "status": "active"
                }
                
        except requests.RequestException:
            print("⚠️  Oobabooga WebUI not found or not running")
    
    def _discover_koboldcpp_models(self):
        """Discover KoboldCpp models"""
        base_url = "http://localhost:5001"
        
        try:
            response = requests.get(f"{base_url}/api/v1/model", timeout=3)
            if response.status_code == 200:
                print("🐉 Found KoboldCpp server")
                
                model_data = response.json()
                model_name = model_data.get('result', 'koboldcpp-model')
                
                capabilities = self._analyze_model_capabilities(model_name)
                
                model = ModelInfo(
                    name=model_name,
                    provider=ModelProvider.KOBOLDCPP,
                    base_url=base_url,
                    type="chat",
                    capabilities=capabilities,
                    status="available"
                )
                
                self.discovered_models.append(model)
                
                self.active_providers[ModelProvider.KOBOLDCPP] = {
                    "base_url": base_url,
                    "status": "active"
                }
                
        except requests.RequestException:
            print("⚠️  KoboldCpp not found or not running")
    
    def _discover_custom_endpoints(self):
        """Discover custom OpenAI-compatible endpoints"""
        # Check common alternative ports
        custom_ports = [8000, 8001, 3000, 7860, 5555]
        
        for port in custom_ports:
            base_url = f"http://localhost:{port}"
            
            try:
                # Try OpenAI-compatible endpoint
                response = requests.get(f"{base_url}/v1/models", timeout=2)
                if response.status_code == 200:
                    print(f"🔌 Found custom endpoint on port {port}")
                    
                    models_data = response.json()
                    for model_data in models_data.get('data', []):
                        model_name = model_data.get('id', f'custom-model-{port}')
                        
                        capabilities = self._analyze_model_capabilities(model_name)
                        
                        model = ModelInfo(
                            name=model_name,
                            provider=ModelProvider.OPENAI_COMPATIBLE,
                            base_url=base_url,
                            type="chat",
                            capabilities=capabilities,
                            status="available"
                        )
                        
                        self.discovered_models.append(model)
                    
                    self.active_providers[ModelProvider.OPENAI_COMPATIBLE] = {
                        "base_url": base_url,
                        "status": "active"
                    }
                    
            except requests.RequestException:
                continue
    
    def _analyze_model_capabilities(self, model_name: str) -> List[str]:
        """Analyze model capabilities based on name"""
        capabilities = []
        model_lower = model_name.lower()
        
        # Code-specific models
        if any(keyword in model_lower for keyword in ['code', 'coder', 'coding', 'llama', 'mistral']):
            capabilities.extend(['code_generation', 'debugging', 'explanation'])
        
        # Instruction-following models
        if any(keyword in model_lower for keyword in ['instruct', 'chat', 'assistant']):
            capabilities.extend(['instruction_following', 'conversation'])
        
        # Large models (likely more capable)
        if any(size in model_lower for size in ['13b', '34b', '70b', '7b']):
            capabilities.append('large_context')
        
        # Specific model families
        if 'codellama' in model_lower:
            capabilities.extend(['code_generation', 'code_completion', 'debugging'])
        elif 'mistral' in model_lower:
            capabilities.extend(['reasoning', 'analysis', 'instruction_following'])
        elif 'llama' in model_lower:
            capabilities.extend(['general_purpose', 'reasoning'])
        
        return capabilities if capabilities else ['general_purpose']
    
    def _rank_models(self):
        """Rank models by capability and suitability for coding tasks"""
        for model in self.discovered_models:
            score = 0.0
            
            # Base score for being available
            score += 1.0
            
            # Bonus for code capabilities
            if 'code_generation' in model.capabilities:
                score += 3.0
            if 'debugging' in model.capabilities:
                score += 2.0
            if 'instruction_following' in model.capabilities:
                score += 1.5
            
            # Bonus for model size (rough proxy for capability)
            if model.size:
                if '13b' in model.size.lower():
                    score += 2.0
                elif '7b' in model.size.lower():
                    score += 1.5
                elif '34b' in model.size.lower() or '70b' in model.size.lower():
                    score += 3.0
            
            # Bonus for specific good models
            if 'codellama' in model.name.lower():
                score += 2.0
            elif 'mistral' in model.name.lower() and 'instruct' in model.name.lower():
                score += 1.5
            
            # Provider reliability bonus
            if model.provider == ModelProvider.OLLAMA:
                score += 0.5  # Ollama is very reliable
            
            model.performance_score = score
        
        # Sort by score
        self.discovered_models.sort(key=lambda m: m.performance_score, reverse=True)
        
        # Set best model
        if self.discovered_models:
            self.best_model = self.discovered_models[0]
    
    def get_best_model(self) -> Optional[ModelInfo]:
        """Get the best available model for coding tasks"""
        return self.best_model
    
    def get_models_by_provider(self, provider: ModelProvider) -> List[ModelInfo]:
        """Get all models from a specific provider"""
        return [m for m in self.discovered_models if m.provider == provider]
    
    def test_model_connection(self, model: ModelInfo) -> bool:
        """Test if a model is actually responsive"""
        try:
            if model.provider == ModelProvider.OLLAMA:
                response = requests.post(
                    f"{model.base_url}/api/generate",
                    json={"model": model.name, "prompt": "test", "stream": False},
                    timeout=10
                )
                return response.status_code == 200
            
            elif model.provider in [ModelProvider.LM_STUDIO, ModelProvider.OPENAI_COMPATIBLE]:
                response = requests.post(
                    f"{model.base_url}/v1/chat/completions",
                    json={
                        "model": model.name,
                        "messages": [{"role": "user", "content": "test"}],
                        "max_tokens": 1
                    },
                    timeout=10
                )
                return response.status_code == 200
            
            return False
            
        except requests.RequestException:
            return False
    
    def get_discovery_summary(self) -> Dict[str, Any]:
        """Get summary of discovered models"""
        summary = {
            "total_models": len(self.discovered_models),
            "active_providers": list(self.active_providers.keys()),
            "best_model": self.best_model.name if self.best_model else None,
            "models_by_provider": {}
        }
        
        for provider in ModelProvider:
            models = self.get_models_by_provider(provider)
            if models:
                summary["models_by_provider"][provider.value] = [m.name for m in models]
        
        return summary
    
    def auto_configure_best_model(self) -> Dict[str, Any]:
        """Auto-configure the best available model"""
        if not self.best_model:
            return {"error": "No models found"}
        
        model = self.best_model
        
        # Test the model
        if self.test_model_connection(model):
            config = {
                "llm": {
                    "provider": model.provider.value,
                    "model": model.name,
                    "base_url": model.base_url,
                    "temperature": 0.1,
                    "max_tokens": 2048
                }
            }
            
            return {
                "success": True,
                "config": config,
                "model_info": {
                    "name": model.name,
                    "provider": model.provider.value,
                    "capabilities": model.capabilities,
                    "score": model.performance_score
                }
            }
        else:
            return {"error": f"Best model {model.name} is not responding"}


def discover_and_configure() -> Dict[str, Any]:
    """
    Discover all models and return the best configuration
    
    Returns:
        Dictionary with auto-configuration or error info
    """
    discovery = ModelDiscovery()
    discovery.discover_all_models()
    return discovery.auto_configure_best_model()


def get_all_available_models() -> List[ModelInfo]:
    """
    Get all available models
    
    Returns:
        List of all discovered models
    """
    discovery = ModelDiscovery()
    return discovery.discover_all_models()
