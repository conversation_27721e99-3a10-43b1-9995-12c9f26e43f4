"""
Error Parser for Devin Local AI
Extracts actionable information from Python errors and tracebacks.
"""

import re
import traceback
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class ErrorInfo:
    """Structured error information"""
    error_type: str
    message: str
    line_number: Optional[int] = None
    file_name: Optional[str] = None
    suggestion: Optional[str] = None
    code_snippet: Optional[str] = None


class ErrorParser:
    """Parses Python errors and provides actionable feedback"""
    
    def __init__(self):
        self.common_fixes = {
            "ModuleNotFoundError": "Install the missing module using pip install",
            "ImportError": "Check import statements and module availability",
            "NameError": "Define the missing variable or function",
            "SyntaxError": "Fix syntax issues in the code",
            "IndentationError": "Fix indentation (use 4 spaces consistently)",
            "TypeError": "Check data types and function arguments",
            "AttributeError": "Check if the object has the specified attribute",
            "KeyError": "Check if the key exists in the dictionary",
            "IndexError": "Check list/array bounds",
            "FileNotFoundError": "Check if the file path exists",
            "PermissionError": "Check file/directory permissions",
        }
    
    def parse_error(self, error_output: str, code: str = "") -> ErrorInfo:
        """
        Parse error output and return structured error information
        
        Args:
            error_output: The stderr output from code execution
            code: The original code that caused the error
            
        Returns:
            ErrorInfo object with parsed error details
        """
        if not error_output.strip():
            return ErrorInfo("NoError", "No error detected")
        
        # Extract the last line which usually contains the main error
        lines = error_output.strip().split('\n')
        error_line = lines[-1] if lines else ""
        
        # Parse error type and message
        error_type, message = self._extract_error_type_and_message(error_line)
        
        # Extract line number and file name from traceback
        line_number, file_name = self._extract_location_info(error_output)
        
        # Get code snippet around the error
        code_snippet = self._extract_code_snippet(code, line_number) if code else None
        
        # Generate suggestion
        suggestion = self._generate_suggestion(error_type, message, code_snippet)
        
        return ErrorInfo(
            error_type=error_type,
            message=message,
            line_number=line_number,
            file_name=file_name,
            suggestion=suggestion,
            code_snippet=code_snippet
        )
    
    def _extract_error_type_and_message(self, error_line: str) -> Tuple[str, str]:
        """Extract error type and message from error line"""
        # Pattern: ErrorType: error message
        match = re.match(r'^(\w+(?:Error|Exception)?):\s*(.*)$', error_line)
        if match:
            return match.group(1), match.group(2)
        
        # Fallback for other formats
        if ':' in error_line:
            parts = error_line.split(':', 1)
            return parts[0].strip(), parts[1].strip()
        
        return "UnknownError", error_line
    
    def _extract_location_info(self, traceback_text: str) -> Tuple[Optional[int], Optional[str]]:
        """Extract line number and file name from traceback"""
        # Look for patterns like: File "filename", line 123, in function_name
        pattern = r'File "([^"]+)", line (\d+)'
        matches = re.findall(pattern, traceback_text)
        
        if matches:
            # Get the last match (closest to the error)
            file_name, line_num = matches[-1]
            return int(line_num), file_name
        
        return None, None
    
    def _extract_code_snippet(self, code: str, line_number: Optional[int]) -> Optional[str]:
        """Extract code snippet around the error line"""
        if not code or line_number is None:
            return None
        
        lines = code.split('\n')
        if line_number > len(lines):
            return None
        
        # Get 2 lines before and after the error line
        start = max(0, line_number - 3)
        end = min(len(lines), line_number + 2)
        
        snippet_lines = []
        for i in range(start, end):
            marker = ">>> " if i == line_number - 1 else "    "
            snippet_lines.append(f"{marker}{i+1:3d}: {lines[i]}")
        
        return '\n'.join(snippet_lines)
    
    def _generate_suggestion(self, error_type: str, message: str, code_snippet: str = None) -> str:
        """Generate helpful suggestion based on error type and message"""
        base_suggestion = self.common_fixes.get(error_type, "Check the error message for details")
        
        # Add specific suggestions based on error message
        specific_suggestions = []
        
        if error_type == "ModuleNotFoundError":
            # Extract module name from message
            module_match = re.search(r"No module named '([^']+)'", message)
            if module_match:
                module_name = module_match.group(1)
                specific_suggestions.append(f"Run: pip install {module_name}")
        
        elif error_type == "NameError":
            # Extract variable name
            name_match = re.search(r"name '([^']+)' is not defined", message)
            if name_match:
                var_name = name_match.group(1)
                specific_suggestions.append(f"Define variable '{var_name}' before using it")
        
        elif error_type == "SyntaxError":
            if "invalid syntax" in message:
                specific_suggestions.append("Check for missing colons, parentheses, or quotes")
            elif "unexpected EOF" in message:
                specific_suggestions.append("Check for unclosed parentheses, brackets, or quotes")
        
        elif error_type == "IndentationError":
            specific_suggestions.append("Use consistent indentation (4 spaces recommended)")
        
        # Combine base and specific suggestions
        all_suggestions = [base_suggestion] + specific_suggestions
        return " | ".join(all_suggestions)
    
    def format_error_feedback(self, error_info: ErrorInfo) -> str:
        """Format error information for LLM feedback"""
        feedback_parts = [
            f"ERROR: {error_info.error_type}",
            f"Message: {error_info.message}"
        ]
        
        if error_info.line_number:
            feedback_parts.append(f"Line: {error_info.line_number}")
        
        if error_info.suggestion:
            feedback_parts.append(f"Suggestion: {error_info.suggestion}")
        
        if error_info.code_snippet:
            feedback_parts.append(f"Code context:\n{error_info.code_snippet}")
        
        return "\n".join(feedback_parts)


def parse_execution_result(stdout: str, stderr: str, return_code: int, code: str = "") -> Dict:
    """
    Parse execution results and return structured information
    
    Args:
        stdout: Standard output from execution
        stderr: Standard error from execution  
        return_code: Process return code
        code: Original code that was executed
        
    Returns:
        Dictionary with parsed results
    """
    parser = ErrorParser()
    
    result = {
        "success": return_code == 0,
        "return_code": return_code,
        "stdout": stdout,
        "stderr": stderr,
        "has_error": bool(stderr.strip()),
        "error_info": None
    }
    
    if stderr.strip():
        error_info = parser.parse_error(stderr, code)
        result["error_info"] = error_info
        result["error_feedback"] = parser.format_error_feedback(error_info)
    
    return result
