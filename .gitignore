# Byte-compiled / cache
__pycache__/
*.py[cod]
*.pyo

# Virtual environments
venv/
env/
ENV/
.venv/

# Jupyter Notebooks Checkpoints
.ipynb_checkpoints

# System Files
.DS_Store
Thumbs.db

# Logs & local settings
*.log
*.sqlite3
*.db
*.pid

# IDEs
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Python testing
coverage/
*.cover
*.py,cover
.hypothesis/

# Environment files
.env
.env.*

# Compressed files
*.zip
*.tar.gz
*.rar

# Build artifacts
build/
dist/
*.egg-info/
.eggs/

# Image caching folders
image_cache/
extracted_training_images/

# Mac specific
*.DS_Store

# Model files and caches
quiz_model/*.safetensors
uploaded_books/*.pdf
cache/
models/
data/models/
data/cache/
data/lora_adapters*/
data/memory_cache/
data/oracle_cache/
data/fire_cache/
data/image_cache/
data/processed_docs/
data/processed_documents/
data/exports/
*.pt
*.bin
*.safetensors
*.gguf
*.ggml

# Large automation files
automation_screenshots/
*.png
*.jpg
*.jpeg

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.swo
*~

# Large text files
*.txt
!requirements*.txt
!README.txt

# Executables
*.exe
*.msi

# Wheels
wheels/

# Test outputs
test_*/
*_test_output/
test_cache/
test_fire_cache/
test_oracle_cache/

# Generated files
complete_python_code.txt
knowledge_app_*.txt
mistral_cleanup_analysis.txt

# NLTK data
nltk_data_fixed/

# User data
user_data/
quiz_storage/

# Faiss index
faiss_index.db.sqlite3
