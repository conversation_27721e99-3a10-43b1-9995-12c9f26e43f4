#!/usr/bin/env python3
"""
Setup script for Devin Local AI
Helps users install dependencies and configure the system.
"""

import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def check_ollama():
    """Check if Ollama is available"""
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Ollama is installed")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("⚠️  Ollama not found")
    print("   Install from: https://ollama.ai")
    return False


def check_models():
    """Check available Ollama models"""
    try:
        result = subprocess.run(['ollama', 'list'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            models = result.stdout.strip()
            if models:
                print("✅ Available Ollama models:")
                for line in models.split('\n')[1:]:  # Skip header
                    if line.strip():
                        model_name = line.split()[0]
                        print(f"   - {model_name}")
                return True
            else:
                print("⚠️  No Ollama models found")
                print("   Download a model: ollama pull codellama:13b")
                return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    return False


def create_directories():
    """Create required directories"""
    dirs = ['agents', 'executor', 'memory', 'planner', 'utils', 'logs', 'workspace', 'examples']
    
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("✅ Directory structure created")


def run_tests():
    """Run system tests"""
    print("🧪 Running system tests...")
    
    try:
        result = subprocess.run([sys.executable, 'test_system.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Some tests failed")
            print(result.stdout)
            return False
    except subprocess.TimeoutExpired:
        print("❌ Tests timed out")
        return False
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False


def show_next_steps():
    """Show next steps to the user"""
    print("\n🎉 Setup complete!")
    print("\n📋 Next steps:")
    print("1. Configure your LLM in config.yaml:")
    print("   - Set provider (ollama/lm_studio)")
    print("   - Set model name")
    print("   - Set base_url if needed")
    print("\n2. Test the system:")
    print("   python main.py -i")
    print("\n3. Try an example task:")
    print('   python main.py "Create a simple calculator"')
    print("\n📚 Documentation:")
    print("   - README.md for detailed instructions")
    print("   - examples/ folder for sample code")
    print("   - config.yaml for configuration options")


def main():
    """Main setup function"""
    print("🧠 Devin Local AI - Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("\n⚠️  Dependency installation failed, but you can continue")
        print("   Try: pip install -r requirements.txt")
    
    # Check Ollama
    ollama_available = check_ollama()
    if ollama_available:
        check_models()
    
    # Run tests
    if run_tests():
        show_next_steps()
    else:
        print("\n⚠️  Setup completed with some issues")
        print("   Check the test output above for details")
        print("   You may still be able to use the system")
    
    print("\n🚀 Ready to start coding with AI!")


if __name__ == "__main__":
    main()
