#!/usr/bin/env python3
"""
Fix get_config_manager calls in files modified by migration script
"""

import os
import re

def fix_file(filepath):
    """Fix get_config_manager calls in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to match the problematic get_config_manager calls
        pattern = r'get_config_manager\(\)\.get_model_path\("[^"]+"\) or get_config_manager\(\)\.get_model_path\("[^"]+"\) or "([^"]+)"'
        
        # Replace with just the fallback path
        def replacement(match):
            return f'"{match.group(1)}"'
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ Fixed: {filepath}")
            return True
        else:
            print(f"ℹ️ No changes needed: {filepath}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {filepath}: {e}")
        return False

def main():
    """Fix all files with get_config_manager issues"""
    files_to_fix = [
        'src/knowledge_app/core/unified_inference_manager.py'
    ]
    
    print("🔧 Fixing get_config_manager calls...")
    
    fixed_count = 0
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            if fix_file(filepath):
                fixed_count += 1
        else:
            print(f"⚠️ File not found: {filepath}")
    
    print(f"\n✅ Fixed {fixed_count} files")

if __name__ == "__main__":
    main()
