#!/usr/bin/env python3
"""
Setup Visual Studio Build Environment and install llama-cpp-python
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def find_vs_tools():
    """Find Visual Studio Build Tools installation."""
    possible_paths = [
        r"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"✅ Found VS Build Tools: {path}")
            return path
    
    logger.error("❌ Visual Studio Build Tools not found")
    return None

def setup_build_environment():
    """Setup the build environment with Visual Studio tools."""
    vs_path = find_vs_tools()
    if not vs_path:
        return False
    
    # Create a batch script that sets up the environment and runs pip
    batch_script = """
@echo off
call "{vs_path}"
echo Setting up Visual Studio environment...
set CMAKE_GENERATOR=Visual Studio 17 2022
set CMAKE_GENERATOR_PLATFORM=x64
python -m pip install llama-cpp-python --force-reinstall --no-cache-dir
""".format(vs_path=vs_path)
    
    with open("install_llama.bat", "w") as f:
        f.write(batch_script)
    
    logger.info("🔧 Running installation with VS environment...")
    
    try:
        result = subprocess.run(
            ["install_llama.bat"], 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=600
        )
        
        if result.returncode == 0:
            logger.info("✅ Installation successful!")
            return True
        else:
            logger.error(f"❌ Installation failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Installation timed out")
        return False
    finally:
        # Clean up
        if os.path.exists("install_llama.bat"):
            os.remove("install_llama.bat")

def try_precompiled_wheel():
    """Try to find and install a precompiled wheel."""
    logger.info("🔍 Trying precompiled wheels...")
    
    # Try different wheel sources
    wheel_urls = [
        "https://github.com/abetlen/llama-cpp-python/releases/download/v0.2.11/llama_cpp_python-0.2.11-cp310-cp310-win_amd64.whl",
        "https://github.com/jllllll/llama-cpp-python-cuBLAS-wheels/releases/download/textgen-webui/llama_cpp_python_cuda-0.2.11+cu121-cp310-cp310-win_amd64.whl"
    ]
    
    for url in wheel_urls:
        logger.info(f"🔧 Trying: {url}")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", url
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✅ Precompiled wheel installed!")
                return True
        except:
            continue
    
    return False

def test_installation():
    """Test if llama-cpp-python works."""
    try:
        from llama_cpp import Llama
        logger.info("✅ llama-cpp-python imported successfully!")
        return True
    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False

def main():
    """Main installation process."""
    print("🛠️  LLAMA-CPP-PYTHON BUILD ENVIRONMENT SETUP")
    print("=" * 50)
    
    # Method 1: Try precompiled wheels first
    if try_precompiled_wheel():
        if test_installation():
            print("\n🎉 SUCCESS with precompiled wheel!")
            return True
    
    # Method 2: Setup build environment and compile
    logger.info("📦 Trying compilation with VS Build Tools...")
    if setup_build_environment():
        if test_installation():
            print("\n🎉 SUCCESS with compilation!")
            return True
    
    # Method 3: Last resort - manual instructions
    print("\n❌ Automatic installation failed.")
    print("\n🔧 MANUAL STEPS:")
    print("1. Open 'Developer Command Prompt for VS 2022'")
    print("2. Navigate to your project folder:")
    print(f"   cd \"{os.getcwd()}\"")
    print("3. Run: pip install llama-cpp-python")
    print("\nOr download Visual Studio Build Tools:")
    print("https://visualstudio.microsoft.com/visual-cpp-build-tools/")
    
    return False

if __name__ == "__main__":
    if not main():
        sys.exit(1)
