#!/usr/bin/env python3
"""
Model Manager for Devin Local AI
Manage, test, and switch between all your local models.
"""

import argparse
import sys
import yaml
import json
import time
from pathlib import Path
from utils.model_discovery import ModelDiscovery, ModelProvider


def list_models():
    """List all discovered models"""
    print("🔍 Discovering models...")
    discovery = ModelDiscovery()
    models = discovery.discover_all_models()
    
    if not models:
        print("❌ No models found!")
        return
    
    print(f"\n📋 FOUND {len(models)} MODELS:")
    print("=" * 80)
    
    for i, model in enumerate(models, 1):
        status_emoji = "🟢" if model.status == "available" else "🔴"
        score_stars = "⭐" * min(5, int(model.performance_score))
        
        print(f"{i:2d}. {status_emoji} {model.name}")
        print(f"    Provider: {model.provider.value}")
        print(f"    URL: {model.base_url}")
        print(f"    Score: {model.performance_score:.1f}/10 {score_stars}")
        print(f"    Capabilities: {', '.join(model.capabilities)}")
        if model.size:
            print(f"    Size: {model.size}")
        print()


def test_model(model_name: str = None):
    """Test a specific model or all models"""
    discovery = ModelDiscovery()
    models = discovery.discover_all_models()
    
    if not models:
        print("❌ No models found!")
        return
    
    if model_name:
        # Test specific model
        target_models = [m for m in models if model_name.lower() in m.name.lower()]
        if not target_models:
            print(f"❌ Model '{model_name}' not found!")
            return
        models_to_test = target_models
    else:
        # Test all models
        models_to_test = models
    
    print(f"🧪 Testing {len(models_to_test)} model(s)...")
    print("=" * 50)
    
    for model in models_to_test:
        print(f"Testing {model.name}...", end=" ")
        
        start_time = time.time()
        success = discovery.test_model_connection(model)
        duration = time.time() - start_time
        
        if success:
            print(f"✅ OK ({duration:.2f}s)")
        else:
            print(f"❌ FAILED ({duration:.2f}s)")


def benchmark_models():
    """Benchmark all available models"""
    discovery = ModelDiscovery()
    models = discovery.discover_all_models()
    
    if not models:
        print("❌ No models found!")
        return
    
    print("🏁 Benchmarking models...")
    print("=" * 60)
    
    results = []
    
    for model in models:
        print(f"Benchmarking {model.name}...", end=" ")
        
        # Test connection speed
        start_time = time.time()
        success = discovery.test_model_connection(model)
        response_time = time.time() - start_time
        
        if success:
            print(f"✅ {response_time:.2f}s")
            results.append({
                "model": model,
                "response_time": response_time,
                "success": True
            })
        else:
            print(f"❌ FAILED")
            results.append({
                "model": model,
                "response_time": float('inf'),
                "success": False
            })
    
    # Sort by performance
    results.sort(key=lambda x: (not x["success"], x["response_time"]))
    
    print("\n🏆 BENCHMARK RESULTS:")
    print("=" * 60)
    
    for i, result in enumerate(results, 1):
        model = result["model"]
        if result["success"]:
            print(f"{i:2d}. 🥇 {model.name}")
            print(f"    Response Time: {result['response_time']:.2f}s")
            print(f"    Score: {model.performance_score:.1f}/10")
            print(f"    Provider: {model.provider.value}")
        else:
            print(f"{i:2d}. ❌ {model.name} (Not responding)")
        print()


def switch_model(model_name: str):
    """Switch to a specific model"""
    discovery = ModelDiscovery()
    models = discovery.discover_all_models()
    
    # Find the model
    target_model = None
    for model in models:
        if model_name.lower() in model.name.lower():
            target_model = model
            break
    
    if not target_model:
        print(f"❌ Model '{model_name}' not found!")
        print("\nAvailable models:")
        for model in models:
            print(f"  • {model.name}")
        return
    
    # Test the model first
    print(f"🧪 Testing {target_model.name}...", end=" ")
    if not discovery.test_model_connection(target_model):
        print("❌ FAILED - Model is not responding!")
        return
    print("✅ OK")
    
    # Update config
    config_path = Path("config.yaml")
    
    try:
        # Load existing config
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f) or {}
        else:
            config = {}
        
        # Update LLM config
        config["llm"] = {
            "provider": target_model.provider.value,
            "model": target_model.name,
            "base_url": target_model.base_url,
            "temperature": 0.1,
            "max_tokens": 2048
        }
        
        # Save config
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        print(f"✅ Switched to {target_model.name}")
        print(f"   Provider: {target_model.provider.value}")
        print(f"   URL: {target_model.base_url}")
        print(f"   Score: {target_model.performance_score:.1f}/10")
        
    except Exception as e:
        print(f"❌ Failed to update configuration: {e}")


def show_current():
    """Show currently configured model"""
    config_path = Path("config.yaml")
    
    if not config_path.exists():
        print("❌ No configuration file found!")
        return
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        llm_config = config.get("llm", {})
        
        if not llm_config:
            print("❌ No LLM configuration found!")
            return
        
        print("🎯 CURRENT MODEL:")
        print("=" * 30)
        print(f"Model: {llm_config.get('model', 'Unknown')}")
        print(f"Provider: {llm_config.get('provider', 'Unknown')}")
        print(f"URL: {llm_config.get('base_url', 'Unknown')}")
        print(f"Temperature: {llm_config.get('temperature', 'Unknown')}")
        print(f"Max Tokens: {llm_config.get('max_tokens', 'Unknown')}")
        
        # Test if it's working
        print(f"\nTesting connection...", end=" ")
        
        # Quick test
        discovery = ModelDiscovery()
        from utils.model_discovery import ModelInfo, ModelProvider
        
        # Create model info from config
        provider_enum = None
        for p in ModelProvider:
            if p.value == llm_config.get('provider'):
                provider_enum = p
                break
        
        if provider_enum:
            test_model = ModelInfo(
                name=llm_config.get('model', ''),
                provider=provider_enum,
                base_url=llm_config.get('base_url', '')
            )
            
            if discovery.test_model_connection(test_model):
                print("✅ OK")
            else:
                print("❌ NOT RESPONDING")
        else:
            print("❓ UNKNOWN PROVIDER")
        
    except Exception as e:
        print(f"❌ Failed to read configuration: {e}")


def auto_select():
    """Auto-select the best model"""
    print("🤖 Auto-selecting best model...")
    
    discovery = ModelDiscovery()
    models = discovery.discover_all_models()
    
    if not models:
        print("❌ No models found!")
        return
    
    best_model = discovery.get_best_model()
    
    if not best_model:
        print("❌ No suitable model found!")
        return
    
    print(f"🏆 Best model: {best_model.name}")
    print(f"   Score: {best_model.performance_score:.1f}/10")
    
    # Switch to it
    switch_model(best_model.name)


def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(description="Devin Local AI - Model Manager")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    subparsers.add_parser('list', help='List all available models')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Test model(s)')
    test_parser.add_argument('model', nargs='?', help='Model name to test (optional)')
    
    # Benchmark command
    subparsers.add_parser('benchmark', help='Benchmark all models')
    
    # Switch command
    switch_parser = subparsers.add_parser('switch', help='Switch to a specific model')
    switch_parser.add_argument('model', help='Model name to switch to')
    
    # Current command
    subparsers.add_parser('current', help='Show current model configuration')
    
    # Auto command
    subparsers.add_parser('auto', help='Auto-select the best model')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🧠 DEVIN LOCAL AI - MODEL MANAGER")
    print("=" * 40)
    
    if args.command == 'list':
        list_models()
    elif args.command == 'test':
        test_model(getattr(args, 'model', None))
    elif args.command == 'benchmark':
        benchmark_models()
    elif args.command == 'switch':
        switch_model(args.model)
    elif args.command == 'current':
        show_current()
    elif args.command == 'auto':
        auto_select()


if __name__ == "__main__":
    main()
