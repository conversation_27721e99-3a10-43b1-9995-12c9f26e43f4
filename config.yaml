# Devin Local AI Configuration

# LLM Settings
llm:
  provider: "ollama"  # ollama, lm_studio, or openai
  model: "llama3.1:8b"  # Your best model for code generation
  base_url: "http://localhost:11434"  # Ollama default URL
  temperature: 0.1  # Low temperature for more deterministic code
  max_tokens: 2048
  timeout: 60

# Alternative LM Studio settings
# llm:
#   provider: "lm_studio"
#   model: "mistral-7b-instruct"
#   base_url: "http://localhost:1234/v1"

# Execution Settings
execution:
  timeout: 30  # Max seconds for code execution
  max_retries: 5  # Max retry attempts per subtask
  sandbox_mode: false  # Use Docker sandbox (requires Docker)
  working_directory: "./workspace"  # Where generated code runs
  allowed_imports:  # Whitelist of allowed Python imports
    - "os"
    - "sys"
    - "json"
    - "requests"
    - "flask"
    - "fastapi"
    - "pandas"
    - "numpy"
    - "matplotlib"
    - "sqlite3"

# Memory Settings
memory:
  file_path: "./memory/memory.json"
  max_history: 100  # Max number of task histories to keep
  auto_save: true

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "./logs/devin.log"
  console: true

# Task Planning
planning:
  max_subtasks: 20  # Max subtasks per main task
  detail_level: "high"  # low, medium, high
  include_testing: true  # Auto-generate test steps

# Error Handling
error_handling:
  parse_tracebacks: true
  suggest_fixes: true
  auto_install_packages: false  # Dangerous - ask user first
  common_fixes:
    - "ModuleNotFoundError: Install missing package"
    - "SyntaxError: Fix syntax issues"
    - "NameError: Define missing variables"
    - "ImportError: Check import statements"
