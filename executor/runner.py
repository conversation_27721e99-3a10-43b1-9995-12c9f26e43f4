"""
Code Executor for Devin Local AI
Safely executes Python code and captures output/errors.
"""

import os
import sys
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import psutil


class CodeExecutor:
    """Executes Python code safely and captures results"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.timeout = self.config.get('timeout', 30)
        self.working_directory = Path(self.config.get('working_directory', './workspace')).resolve()
        self.sandbox_mode = self.config.get('sandbox_mode', False)
        self.allowed_imports = set(self.config.get('allowed_imports', []))
        
        # Ensure working directory exists
        self.working_directory.mkdir(exist_ok=True)
    
    def execute_code(self, code: str, filename: str = None) -> Dict:
        """
        Execute Python code and return results
        
        Args:
            code: Python code to execute
            filename: Optional filename for the code file
            
        Returns:
            Dictionary with execution results
        """
        if not filename:
            filename = f"temp_code_{int(time.time())}.py"
        
        # Validate code before execution
        validation_result = self._validate_code(code)
        if not validation_result['valid']:
            return {
                'success': False,
                'stdout': '',
                'stderr': validation_result['error'],
                'return_code': -1,
                'execution_time': 0,
                'filename': filename
            }
        
        # Write code to file
        code_file = self.working_directory / filename
        try:
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(code)
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Failed to write code file: {str(e)}",
                'return_code': -1,
                'execution_time': 0,
                'filename': filename
            }
        
        # Execute the code
        if self.sandbox_mode:
            result = self._execute_in_sandbox(code_file)
        else:
            result = self._execute_directly(code_file)
        
        result['filename'] = filename
        return result
    
    def _validate_code(self, code: str) -> Dict:
        """Validate code for security and syntax"""
        # Check for dangerous operations
        dangerous_patterns = [
            'import os',
            'import subprocess',
            'import shutil',
            'import sys',
            '__import__',
            'eval(',
            'exec(',
            'open(',
            'file(',
            'input(',
            'raw_input(',
        ]
        
        # Allow specific imports if they're in the allowed list
        if self.allowed_imports:
            dangerous_patterns = [
                pattern for pattern in dangerous_patterns 
                if not any(allowed in pattern for allowed in self.allowed_imports)
            ]
        
        for pattern in dangerous_patterns:
            if pattern in code:
                return {
                    'valid': False,
                    'error': f"Potentially dangerous operation detected: {pattern}"
                }
        
        # Check syntax
        try:
            compile(code, '<string>', 'exec')
        except SyntaxError as e:
            return {
                'valid': False,
                'error': f"Syntax error: {str(e)}"
            }
        
        return {'valid': True}
    
    def _execute_directly(self, code_file: Path) -> Dict:
        """Execute code directly using subprocess"""
        start_time = time.time()
        
        try:
            # Run the Python file
            process = subprocess.Popen(
                [sys.executable, str(code_file)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=str(code_file.parent)
            )

            stdout, stderr = process.communicate(timeout=self.timeout)
            return_code = process.returncode
            
        except subprocess.TimeoutExpired:
            # Kill the process if it times out
            try:
                parent = psutil.Process(process.pid)
                for child in parent.children(recursive=True):
                    child.kill()
                parent.kill()
            except:
                pass
            
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Execution timed out after {self.timeout} seconds",
                'return_code': -1,
                'execution_time': self.timeout
            }
        
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Execution error: {str(e)}",
                'return_code': -1,
                'execution_time': time.time() - start_time
            }
        
        execution_time = time.time() - start_time
        
        return {
            'success': return_code == 0,
            'stdout': stdout,
            'stderr': stderr,
            'return_code': return_code,
            'execution_time': execution_time
        }
    
    def _execute_in_sandbox(self, code_file: Path) -> Dict:
        """Execute code in Docker sandbox (if available)"""
        # This is a placeholder for Docker sandbox execution
        # For now, fall back to direct execution with warnings
        print("Warning: Sandbox mode requested but not implemented. Using direct execution.")
        return self._execute_directly(code_file)
    
    def install_package(self, package_name: str) -> Dict:
        """
        Install a Python package using pip
        
        Args:
            package_name: Name of the package to install
            
        Returns:
            Dictionary with installation results
        """
        try:
            process = subprocess.Popen(
                [sys.executable, '-m', 'pip', 'install', package_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=120  # 2 minutes timeout for package installation
            )
            
            stdout, stderr = process.communicate()
            return_code = process.returncode
            
            return {
                'success': return_code == 0,
                'stdout': stdout,
                'stderr': stderr,
                'return_code': return_code,
                'package': package_name
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Package installation timed out: {package_name}",
                'return_code': -1,
                'package': package_name
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Installation error: {str(e)}",
                'return_code': -1,
                'package': package_name
            }
    
    def list_files(self) -> List[str]:
        """List files in the working directory"""
        try:
            return [f.name for f in self.working_directory.iterdir() if f.is_file()]
        except Exception:
            return []
    
    def read_file(self, filename: str) -> Optional[str]:
        """Read content of a file in the working directory"""
        try:
            file_path = self.working_directory / filename
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            return None
    
    def delete_file(self, filename: str) -> bool:
        """Delete a file in the working directory"""
        try:
            file_path = self.working_directory / filename
            if file_path.exists():
                file_path.unlink()
                return True
        except Exception:
            pass
        return False
    
    def cleanup_temp_files(self) -> int:
        """Clean up temporary code files"""
        count = 0
        try:
            for file_path in self.working_directory.glob("temp_code_*.py"):
                file_path.unlink()
                count += 1
        except Exception:
            pass
        return count


def execute_python_code(code: str, config: Dict = None, filename: str = None) -> Dict:
    """
    Convenience function to execute Python code
    
    Args:
        code: Python code to execute
        config: Optional configuration dictionary
        filename: Optional filename for the code
        
    Returns:
        Dictionary with execution results
    """
    executor = CodeExecutor(config)
    return executor.execute_code(code, filename)
