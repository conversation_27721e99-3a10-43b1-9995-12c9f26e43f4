"""
Docker Sandbox for Devin Local AI
Provides isolated code execution environment using Docker containers.
"""

import os
import sys
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional
import json


class DockerSandbox:
    """Docker-based sandbox for safe code execution"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.image_name = self.config.get('docker_image', 'python:3.10-slim')
        self.timeout = self.config.get('timeout', 30)
        self.memory_limit = self.config.get('memory_limit', '128m')
        self.cpu_limit = self.config.get('cpu_limit', '0.5')
        self.network_mode = self.config.get('network_mode', 'none')  # No network access
        
        # Check if Docker is available
        self.docker_available = self._check_docker()
    
    def _check_docker(self) -> bool:
        """Check if Docker is available and running"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                # Check if Docker daemon is running
                result = subprocess.run(['docker', 'info'], 
                                      capture_output=True, text=True, timeout=5)
                return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        return False
    
    def execute_code(self, code: str, filename: str = None) -> Dict:
        """
        Execute Python code in Docker sandbox
        
        Args:
            code: Python code to execute
            filename: Optional filename for the code
            
        Returns:
            Dictionary with execution results
        """
        if not self.docker_available:
            return {
                'success': False,
                'stdout': '',
                'stderr': 'Docker is not available or not running',
                'return_code': -1,
                'execution_time': 0,
                'sandbox_used': False
            }
        
        if not filename:
            filename = f"sandbox_code_{int(time.time())}.py"
        
        # Create temporary directory for code execution
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            code_file = temp_path / filename
            
            # Write code to file
            try:
                with open(code_file, 'w', encoding='utf-8') as f:
                    f.write(code)
            except Exception as e:
                return {
                    'success': False,
                    'stdout': '',
                    'stderr': f"Failed to write code file: {str(e)}",
                    'return_code': -1,
                    'execution_time': 0,
                    'sandbox_used': True
                }
            
            # Execute in Docker container
            return self._run_in_container(temp_path, filename)
    
    def _run_in_container(self, host_dir: Path, filename: str) -> Dict:
        """Run code in Docker container"""
        start_time = time.time()
        
        # Docker run command
        docker_cmd = [
            'docker', 'run',
            '--rm',  # Remove container after execution
            '--network', self.network_mode,  # Network isolation
            '--memory', self.memory_limit,  # Memory limit
            '--cpus', str(self.cpu_limit),  # CPU limit
            '--user', 'nobody',  # Run as non-root user
            '--read-only',  # Read-only filesystem
            '--tmpfs', '/tmp',  # Writable tmp directory
            '--volume', f"{host_dir}:/code:ro",  # Mount code directory as read-only
            '--workdir', '/code',
            self.image_name,
            'python', filename
        ]
        
        try:
            process = subprocess.Popen(
                docker_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate(timeout=self.timeout)
            return_code = process.returncode
            
        except subprocess.TimeoutExpired:
            # Kill the container
            try:
                subprocess.run(['docker', 'kill', process.pid], 
                             capture_output=True, timeout=5)
            except:
                pass
            
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Execution timed out after {self.timeout} seconds",
                'return_code': -1,
                'execution_time': self.timeout,
                'sandbox_used': True
            }
        
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Docker execution error: {str(e)}",
                'return_code': -1,
                'execution_time': time.time() - start_time,
                'sandbox_used': True
            }
        
        execution_time = time.time() - start_time
        
        return {
            'success': return_code == 0,
            'stdout': stdout,
            'stderr': stderr,
            'return_code': return_code,
            'execution_time': execution_time,
            'sandbox_used': True
        }
    
    def install_package_in_image(self, package_name: str) -> Dict:
        """
        Create a new Docker image with additional packages
        
        Args:
            package_name: Name of the package to install
            
        Returns:
            Dictionary with installation results
        """
        if not self.docker_available:
            return {
                'success': False,
                'error': 'Docker is not available'
            }
        
        # Create Dockerfile
        dockerfile_content = f"""
FROM {self.image_name}
RUN pip install {package_name}
"""
        
        # Build new image
        new_image_name = f"devin-local-{package_name.replace('_', '-').lower()}"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            dockerfile_path = Path(temp_dir) / "Dockerfile"
            
            try:
                with open(dockerfile_path, 'w') as f:
                    f.write(dockerfile_content)
                
                # Build image
                build_cmd = [
                    'docker', 'build',
                    '-t', new_image_name,
                    str(temp_dir)
                ]
                
                result = subprocess.run(
                    build_cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minutes for building
                )
                
                if result.returncode == 0:
                    # Update image name for future use
                    self.image_name = new_image_name
                    return {
                        'success': True,
                        'image_name': new_image_name,
                        'package': package_name
                    }
                else:
                    return {
                        'success': False,
                        'error': result.stderr,
                        'package': package_name
                    }
                    
            except Exception as e:
                return {
                    'success': False,
                    'error': str(e),
                    'package': package_name
                }
    
    def list_available_images(self) -> List[str]:
        """List available Docker images"""
        if not self.docker_available:
            return []
        
        try:
            result = subprocess.run(
                ['docker', 'images', '--format', '{{.Repository}}:{{.Tag}}'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return [line.strip() for line in result.stdout.split('\n') if line.strip()]
        except:
            pass
        
        return []
    
    def cleanup_images(self) -> Dict:
        """Clean up unused Docker images"""
        if not self.docker_available:
            return {'success': False, 'error': 'Docker not available'}
        
        try:
            # Remove dangling images
            result = subprocess.run(
                ['docker', 'image', 'prune', '-f'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr if result.returncode != 0 else None
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


class HybridExecutor:
    """Hybrid executor that uses Docker when available, falls back to direct execution"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.sandbox = DockerSandbox(config)
        
        # Import the regular executor as fallback
        from .runner import CodeExecutor
        self.fallback_executor = CodeExecutor(config)
        
        self.prefer_sandbox = self.config.get('prefer_sandbox', True)
        self.force_sandbox = self.config.get('force_sandbox', False)
    
    def execute_code(self, code: str, filename: str = None) -> Dict:
        """
        Execute code using the best available method
        
        Args:
            code: Python code to execute
            filename: Optional filename
            
        Returns:
            Dictionary with execution results
        """
        # Check if we should use sandbox
        use_sandbox = (
            self.prefer_sandbox and 
            self.sandbox.docker_available
        ) or self.force_sandbox
        
        if use_sandbox:
            result = self.sandbox.execute_code(code, filename)
            
            # If sandbox fails and we're not forcing it, fall back
            if not result['success'] and not self.force_sandbox:
                if 'Docker' in result.get('stderr', ''):
                    print("Warning: Docker sandbox failed, falling back to direct execution")
                    return self.fallback_executor.execute_code(code, filename)
            
            return result
        else:
            return self.fallback_executor.execute_code(code, filename)
    
    def install_package(self, package_name: str) -> Dict:
        """Install package in the appropriate environment"""
        if self.sandbox.docker_available and self.prefer_sandbox:
            return self.sandbox.install_package_in_image(package_name)
        else:
            return self.fallback_executor.install_package(package_name)
    
    def get_execution_info(self) -> Dict:
        """Get information about execution capabilities"""
        return {
            'docker_available': self.sandbox.docker_available,
            'prefer_sandbox': self.prefer_sandbox,
            'force_sandbox': self.force_sandbox,
            'current_image': self.sandbox.image_name if self.sandbox.docker_available else None,
            'available_images': self.sandbox.list_available_images()
        }


def create_hybrid_executor(config: Dict = None) -> HybridExecutor:
    """
    Create a hybrid executor with the given configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        HybridExecutor instance
    """
    return HybridExecutor(config)
