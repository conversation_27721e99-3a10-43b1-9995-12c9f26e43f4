"""
Streamlit Web UI for Devin Local AI
Provides a user-friendly web interface for the coding agent.
"""

import streamlit as st
import sys
import os
import time
import json
from pathlib import Path

# Add parent directory to path to import our modules
sys.path.append(str(Path(__file__).parent.parent))

from main import DevinL<PERSON>al
from memory.memory_manager import <PERSON><PERSON>anager
from planner.task_planner import TaskPlanner
from executor.runner import <PERSON>Executor


def init_session_state():
    """Initialize Streamlit session state"""
    if 'devin' not in st.session_state:
        st.session_state.devin = None
    if 'current_task' not in st.session_state:
        st.session_state.current_task = ""
    if 'task_history' not in st.session_state:
        st.session_state.task_history = []
    if 'config_loaded' not in st.session_state:
        st.session_state.config_loaded = False


def load_config():
    """Load configuration and initialize Devin"""
    try:
        st.session_state.devin = DevinLocal()
        st.session_state.config_loaded = True
        return True
    except Exception as e:
        st.error(f"Failed to load configuration: {e}")
        return False


def main():
    """Main Streamlit app"""
    st.set_page_config(
        page_title="Devin Local AI",
        page_icon="🧠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    init_session_state()
    
    # Header
    st.title("🧠 Devin Local AI")
    st.markdown("*Self-Iterating Coding Agent*")
    
    # Sidebar
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        if not st.session_state.config_loaded:
            if st.button("🔄 Load Configuration"):
                if load_config():
                    st.success("Configuration loaded!")
                    st.rerun()
        else:
            st.success("✅ System Ready")
            
            # System status
            if st.session_state.devin:
                memory = st.session_state.devin.memory
                stats = memory.get_statistics()
                
                st.subheader("📊 Statistics")
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Total Sessions", stats['total_sessions'])
                    st.metric("Success Rate", f"{stats['success_rate']:.1f}%")
                with col2:
                    st.metric("Successful", stats['successful_sessions'])
                    st.metric("Total Attempts", stats['total_attempts'])
        
        st.divider()
        
        # Configuration display
        st.subheader("🔧 Settings")
        if st.session_state.config_loaded and st.session_state.devin:
            config = st.session_state.devin.config
            llm_config = config.get('llm', {})
            
            st.text(f"Provider: {llm_config.get('provider', 'Not set')}")
            st.text(f"Model: {llm_config.get('model', 'Not set')}")
            st.text(f"Base URL: {llm_config.get('base_url', 'Not set')}")
    
    # Main content
    if not st.session_state.config_loaded:
        st.warning("Please load the configuration first using the sidebar.")
        
        # Show setup instructions
        st.subheader("🚀 Getting Started")
        st.markdown("""
        1. **Install Dependencies**: `pip install -r requirements.txt`
        2. **Set up LLM**: Install Ollama or LM Studio
        3. **Configure**: Update `config.yaml` with your LLM settings
        4. **Load Configuration**: Click the button in the sidebar
        """)
        
        # Show example config
        with st.expander("📄 Example Configuration"):
            st.code("""
# config.yaml
llm:
  provider: "ollama"
  model: "codellama:13b"
  base_url: "http://localhost:11434"
  temperature: 0.1
            """, language="yaml")
        
        return
    
    # Task input
    st.subheader("💭 Task Input")
    
    # Predefined examples
    examples = [
        "Build a REST API in Flask that returns random jokes",
        "Create a data analysis script for CSV files",
        "Build a command-line calculator with basic operations",
        "Create a web scraper for product information",
        "Build a simple todo list manager with file storage",
        "Create a password generator with customizable options"
    ]
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        task_input = st.text_area(
            "Describe your coding task:",
            value=st.session_state.current_task,
            height=100,
            placeholder="e.g., Build a REST API that manages a todo list..."
        )
    
    with col2:
        st.write("**Quick Examples:**")
        for example in examples[:3]:
            if st.button(f"📝 {example[:30]}...", key=f"ex_{hash(example)}"):
                st.session_state.current_task = example
                st.rerun()
    
    # Task execution
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        if st.button("🚀 Execute Task", type="primary", disabled=not task_input.strip()):
            execute_task(task_input.strip())
    
    with col2:
        if st.button("📋 Plan Only"):
            plan_task(task_input.strip())
    
    with col3:
        if st.button("🧪 Demo Mode"):
            run_demo()
    
    # Results area
    if 'task_result' in st.session_state:
        display_task_result(st.session_state.task_result)
    
    # Task history
    display_task_history()


def execute_task(task_description: str):
    """Execute a coding task"""
    if not st.session_state.devin:
        st.error("System not initialized")
        return
    
    with st.spinner("🤖 Executing task..."):
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Update progress
        progress_bar.progress(0.1)
        status_text.text("Planning task...")
        
        try:
            result = st.session_state.devin.run_task(task_description)
            
            progress_bar.progress(1.0)
            status_text.text("Task completed!")
            
            # Store result
            st.session_state.task_result = result
            st.session_state.task_history.append({
                'task': task_description,
                'result': result,
                'timestamp': time.time()
            })
            
            # Clear progress
            progress_bar.empty()
            status_text.empty()
            
        except Exception as e:
            st.error(f"Task execution failed: {e}")
            progress_bar.empty()
            status_text.empty()


def plan_task(task_description: str):
    """Plan a task without executing"""
    planner = TaskPlanner()
    
    with st.spinner("📋 Planning task..."):
        subtasks = planner.plan_task(task_description)
    
    st.subheader("📋 Task Plan")
    
    for i, subtask in enumerate(subtasks, 1):
        with st.expander(f"{i}. {subtask.description}"):
            st.write(f"**Type:** {subtask.type.value}")
            if subtask.code_hint:
                st.write(f"**Hint:** {subtask.code_hint}")
            if subtask.expected_output:
                st.write(f"**Expected Output:** {subtask.expected_output}")


def run_demo():
    """Run demo mode"""
    st.subheader("🧪 Demo Mode")
    
    demo_tasks = [
        "Create a simple calculator function",
        "Build a greeting function with multiple languages",
        "Create a basic file organizer script"
    ]
    
    selected_demo = st.selectbox("Choose a demo task:", demo_tasks)
    
    if st.button("Run Demo Task"):
        # Simulate task execution for demo
        with st.spinner("Running demo..."):
            time.sleep(2)  # Simulate processing
            
            demo_result = {
                'success': True,
                'session_id': f"demo_{int(time.time())}",
                'subtasks_completed': 4,
                'total_subtasks': 4,
                'final_code': f"""
# Demo code for: {selected_demo}
def demo_function():
    print("This is a demo implementation")
    return "Demo completed successfully!"

if __name__ == "__main__":
    result = demo_function()
    print(result)
"""
            }
            
            st.session_state.task_result = demo_result


def display_task_result(result: dict):
    """Display task execution results"""
    st.subheader("📊 Task Results")
    
    # Status
    if result['success']:
        st.success(f"✅ Task completed successfully!")
    else:
        st.error(f"❌ Task failed")
    
    # Metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Subtasks Completed", 
                 f"{result.get('subtasks_completed', 0)}/{result.get('total_subtasks', 0)}")
    
    with col2:
        st.metric("Session ID", result.get('session_id', 'Unknown'))
    
    with col3:
        success_rate = (result.get('subtasks_completed', 0) / 
                       max(result.get('total_subtasks', 1), 1) * 100)
        st.metric("Success Rate", f"{success_rate:.1f}%")
    
    # Final code
    if result.get('final_code'):
        st.subheader("📄 Generated Code")
        st.code(result['final_code'], language="python")
        
        # Download button
        st.download_button(
            label="💾 Download Code",
            data=result['final_code'],
            file_name=f"devin_output_{result.get('session_id', 'unknown')}.py",
            mime="text/plain"
        )


def display_task_history():
    """Display task execution history"""
    if not st.session_state.task_history:
        return
    
    st.subheader("📚 Task History")
    
    for i, entry in enumerate(reversed(st.session_state.task_history[-5:]), 1):
        with st.expander(f"{i}. {entry['task'][:50]}..."):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Task:** {entry['task']}")
                st.write(f"**Status:** {'✅ Success' if entry['result']['success'] else '❌ Failed'}")
                st.write(f"**Session:** {entry['result'].get('session_id', 'Unknown')}")
            
            with col2:
                timestamp = time.strftime('%Y-%m-%d %H:%M:%S', 
                                        time.localtime(entry['timestamp']))
                st.write(f"**Time:** {timestamp}")
                
                if st.button(f"🔄 Retry", key=f"retry_{i}"):
                    st.session_state.current_task = entry['task']
                    st.rerun()


if __name__ == "__main__":
    main()
