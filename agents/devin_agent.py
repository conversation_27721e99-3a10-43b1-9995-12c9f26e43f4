"""
Devin Agent - Core AI agent for self-iterating coding tasks
Integrates with local LLMs (Ollama/LM Studio) for code generation and debugging.
"""

import json
import requests
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict


@dataclass
class AgentContext:
    """Context information for the agent"""
    task_description: str
    current_subtask: str
    previous_attempts: List[Dict] = None
    error_history: List[str] = None
    code_history: List[str] = None
    
    def __post_init__(self):
        if self.previous_attempts is None:
            self.previous_attempts = []
        if self.error_history is None:
            self.error_history = []
        if self.code_history is None:
            self.code_history = []


class DevinAgent:
    """Main AI agent for coding tasks"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.llm_config = config.get('llm', {})
        self.provider = self.llm_config.get('provider', 'ollama')
        self.model = self.llm_config.get('model', 'codellama:13b')
        self.base_url = self.llm_config.get('base_url', 'http://localhost:11434')
        self.temperature = self.llm_config.get('temperature', 0.1)
        self.max_tokens = self.llm_config.get('max_tokens', 2048)
        self.timeout = self.llm_config.get('timeout', 60)
        
        # System prompts
        self.system_prompts = {
            'code_generation': self._get_code_generation_prompt(),
            'debugging': self._get_debugging_prompt(),
            'planning': self._get_planning_prompt()
        }
    
    def generate_code(self, context: AgentContext) -> str:
        """
        Generate code for the current subtask
        
        Args:
            context: Current agent context
            
        Returns:
            Generated Python code
        """
        prompt = self._build_code_prompt(context)
        response = self._call_llm(prompt, 'code_generation')
        
        # Extract code from response
        code = self._extract_code_from_response(response)
        
        # Add to code history
        context.code_history.append(code)
        
        return code
    
    def debug_code(self, context: AgentContext, error_info: Dict) -> str:
        """
        Debug code based on error information
        
        Args:
            context: Current agent context
            error_info: Error information from execution
            
        Returns:
            Fixed Python code
        """
        prompt = self._build_debug_prompt(context, error_info)
        response = self._call_llm(prompt, 'debugging')
        
        # Extract fixed code from response
        fixed_code = self._extract_code_from_response(response)
        
        # Add to histories
        context.error_history.append(error_info.get('error_feedback', ''))
        context.code_history.append(fixed_code)
        
        return fixed_code
    
    def _call_llm(self, prompt: str, prompt_type: str = 'code_generation') -> str:
        """Call the local LLM with the given prompt"""
        if self.provider == 'ollama':
            return self._call_ollama(prompt, prompt_type)
        elif self.provider == 'lm_studio':
            return self._call_lm_studio(prompt, prompt_type)
        elif self.provider == 'openai':
            return self._call_openai_compatible(prompt, prompt_type)
        else:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")
    
    def _call_ollama(self, prompt: str, prompt_type: str) -> str:
        """Call Ollama API"""
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "system": self.system_prompts.get(prompt_type, ''),
            "stream": False,
            "options": {
                "temperature": self.temperature,
                "num_predict": self.max_tokens
            }
        }
        
        try:
            response = requests.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()
            
            result = response.json()
            return result.get('response', '')
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"Ollama API error: {str(e)}")
    
    def _call_lm_studio(self, prompt: str, prompt_type: str) -> str:
        """Call LM Studio API (OpenAI compatible)"""
        url = f"{self.base_url}/chat/completions"
        
        messages = [
            {"role": "system", "content": self.system_prompts.get(prompt_type, '')},
            {"role": "user", "content": prompt}
        ]
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }
        
        try:
            response = requests.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()
            
            result = response.json()
            return result['choices'][0]['message']['content']
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"LM Studio API error: {str(e)}")
    
    def _call_openai_compatible(self, prompt: str, prompt_type: str) -> str:
        """Call OpenAI-compatible API"""
        # Similar to LM Studio but with different headers/auth if needed
        return self._call_lm_studio(prompt, prompt_type)
    
    def _build_code_prompt(self, context: AgentContext) -> str:
        """Build prompt for code generation"""
        prompt_parts = [
            f"Task: {context.task_description}",
            f"Current subtask: {context.current_subtask}",
            "",
            "Generate Python code to complete this subtask."
        ]
        
        # Add context from previous attempts
        if context.previous_attempts:
            prompt_parts.extend([
                "",
                "Previous attempts:",
                *[f"- {attempt.get('description', 'Unknown')}" for attempt in context.previous_attempts[-3:]]
            ])
        
        # Add error context
        if context.error_history:
            prompt_parts.extend([
                "",
                "Previous errors to avoid:",
                *[f"- {error}" for error in context.error_history[-2:]]
            ])
        
        prompt_parts.extend([
            "",
            "Requirements:",
            "- Write complete, executable Python code",
            "- Include necessary imports",
            "- Add error handling where appropriate",
            "- Use clear variable names and comments",
            "",
            "Code:"
        ])
        
        return "\n".join(prompt_parts)
    
    def _build_debug_prompt(self, context: AgentContext, error_info: Dict) -> str:
        """Build prompt for debugging"""
        last_code = context.code_history[-1] if context.code_history else "No previous code"
        error_feedback = error_info.get('error_feedback', 'Unknown error')
        
        prompt_parts = [
            f"Task: {context.task_description}",
            f"Current subtask: {context.current_subtask}",
            "",
            "The following code produced an error:",
            "```python",
            last_code,
            "```",
            "",
            f"Error: {error_feedback}",
            "",
            "Fix the code to resolve this error.",
            "Provide the complete corrected code.",
            "",
            "Fixed code:"
        ]
        
        return "\n".join(prompt_parts)
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract Python code from LLM response"""
        # Look for code blocks
        import re
        
        # Try to find code in markdown code blocks
        code_block_pattern = r'```(?:python)?\s*(.*?)\s*```'
        matches = re.findall(code_block_pattern, response, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # If no code blocks, try to extract code after "Code:" or similar
        code_start_patterns = [
            r'Code:\s*(.*)',
            r'Fixed code:\s*(.*)',
            r'Solution:\s*(.*)',
            r'```\s*(.*)',
        ]
        
        for pattern in code_start_patterns:
            match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # If all else fails, return the entire response
        return response.strip()
    
    def _get_code_generation_prompt(self) -> str:
        """System prompt for code generation"""
        return """You are an expert Python programmer. Your task is to write clean, efficient, and working Python code.

Guidelines:
- Write complete, executable code
- Include all necessary imports
- Use proper error handling
- Write clear, readable code with comments
- Follow Python best practices
- Test your logic before responding

Focus on creating working solutions that can be executed immediately."""
    
    def _get_debugging_prompt(self) -> str:
        """System prompt for debugging"""
        return """You are an expert Python debugger. Your task is to analyze errors and fix code.

Guidelines:
- Carefully analyze the error message
- Identify the root cause of the problem
- Fix the issue while preserving the original intent
- Ensure the fixed code is complete and executable
- Add improvements to prevent similar errors

Focus on creating robust, error-free code."""
    
    def _get_planning_prompt(self) -> str:
        """System prompt for planning"""
        return """You are an expert software architect. Your task is to break down complex coding tasks into manageable steps.

Guidelines:
- Create clear, actionable subtasks
- Consider dependencies between tasks
- Include testing and validation steps
- Think about error handling and edge cases
- Plan for iterative development

Focus on creating comprehensive, executable plans."""


def create_agent(config: Dict) -> DevinAgent:
    """
    Create a Devin agent with the given configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        DevinAgent instance
    """
    return DevinAgent(config)
